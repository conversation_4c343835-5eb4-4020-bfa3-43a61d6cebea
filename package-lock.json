{"name": "super-copy-coder-vue", "version": "0.1.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "super-copy-coder-vue", "version": "0.1.0", "dependencies": {"@techstark/opencv-js": "^4.10.0-release.1", "axios": "^1.9.0", "core-js": "^3.8.3", "element-ui": "^2.15.14", "marked": "^4.3.0", "openai": "^4.96.0", "tesseract.js": "^6.0.1", "vue": "^2.6.14", "vue-router": "^3.5.1", "vuex": "^3.6.2"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-service": "~5.0.0", "buffer": "^6.0.3", "crypto-browserify": "^3.12.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "os-browserify": "^0.3.0", "path-browserify": "^1.0.1", "process": "^0.11.10", "stream-browserify": "^3.0.0", "util": "^0.12.5", "vue-template-compiler": "^2.6.14"}}, "node_modules/@achrinza/node-ipc": {"version": "9.2.9", "resolved": "http://r.npm.sankuai.com/@achrinza/node-ipc/download/@achrinza/node-ipc-9.2.9.tgz", "integrity": "sha1-q0gV2bFvHIOkef6HkVIqOr67HGo=", "dev": true, "license": "MIT", "dependencies": {"@node-ipc/js-queue": "2.0.3", "event-pubsub": "4.3.0", "js-message": "1.0.7"}, "engines": {"node": "8 || 9 || 10 || 11 || 12 || 13 || 14 || 15 || 16 || 17 || 18 || 19 || 20 || 21 || 22"}}, "node_modules/@ampproject/remapping": {"version": "2.3.0", "resolved": "http://r.npm.sankuai.com/@ampproject/remapping/download/@ampproject/remapping-2.3.0.tgz", "integrity": "sha1-7UQbb6YAByUgzhi0PSyMyMrsx/Q=", "dev": true, "license": "Apache-2.0", "dependencies": {"@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.24"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@babel/code-frame": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/code-frame/download/@babel/code-frame-7.27.1.tgz", "integrity": "sha1-IA9xXmbVKiOyIalDVTSpHME61b4=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-validator-identifier": "^7.27.1", "js-tokens": "^4.0.0", "picocolors": "^1.1.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/compat-data": {"version": "7.27.2", "resolved": "http://r.npm.sankuai.com/@babel/compat-data/download/@babel/compat-data-7.27.2.tgz", "integrity": "sha1-QYP55kL9hOdOPup/+pOkEuOxAsk=", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/core": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/core/download/@babel/core-7.27.1.tgz", "integrity": "sha1-id5R6GvRIkYAPjUkcExJVBsWw+Y=", "dev": true, "license": "MIT", "dependencies": {"@ampproject/remapping": "^2.2.0", "@babel/code-frame": "^7.27.1", "@babel/generator": "^7.27.1", "@babel/helper-compilation-targets": "^7.27.1", "@babel/helper-module-transforms": "^7.27.1", "@babel/helpers": "^7.27.1", "@babel/parser": "^7.27.1", "@babel/template": "^7.27.1", "@babel/traverse": "^7.27.1", "@babel/types": "^7.27.1", "convert-source-map": "^2.0.0", "debug": "^4.1.0", "gensync": "^1.0.0-beta.2", "json5": "^2.2.3", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/babel"}}, "node_modules/@babel/eslint-parser": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/eslint-parser/download/@babel/eslint-parser-7.27.1.tgz", "integrity": "sha1-4Ub7L6zvYsbF0ab9B8/Xnun3sPE=", "dev": true, "license": "MIT", "dependencies": {"@nicolo-ribaudo/eslint-scope-5-internals": "5.1.1-v1", "eslint-visitor-keys": "^2.1.0", "semver": "^6.3.1"}, "engines": {"node": "^10.13.0 || ^12.13.0 || >=14.0.0"}, "peerDependencies": {"@babel/core": "^7.11.0", "eslint": "^7.5.0 || ^8.0.0 || ^9.0.0"}}, "node_modules/@babel/generator": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/generator/download/@babel/generator-7.27.1.tgz", "integrity": "sha1-hi1PrYWPcgjt1IfCi1gUQDa3YjA=", "dev": true, "license": "MIT", "dependencies": {"@babel/parser": "^7.27.1", "@babel/types": "^7.27.1", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25", "jsesc": "^3.0.2"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-annotate-as-pure": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/helper-annotate-as-pure/download/@babel/helper-annotate-as-pure-7.27.1.tgz", "integrity": "sha1-Q0XYGppGpkhuJNBpRp8T5gRFwF0=", "dev": true, "license": "MIT", "dependencies": {"@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-compilation-targets": {"version": "7.27.2", "resolved": "http://r.npm.sankuai.com/@babel/helper-compilation-targets/download/@babel/helper-compilation-targets-7.27.2.tgz", "integrity": "sha1-RqD276uAjVHSnOloWN0Qzocycz0=", "dev": true, "license": "MIT", "dependencies": {"@babel/compat-data": "^7.27.2", "@babel/helper-validator-option": "^7.27.1", "browserslist": "^4.24.0", "lru-cache": "^5.1.1", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-compilation-targets/node_modules/lru-cache": {"version": "5.1.1", "resolved": "http://r.npm.sankuai.com/lru-cache/download/lru-cache-5.1.1.tgz", "integrity": "sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA=", "dev": true, "license": "ISC", "dependencies": {"yallist": "^3.0.2"}}, "node_modules/@babel/helper-compilation-targets/node_modules/yallist": {"version": "3.1.1", "resolved": "http://r.npm.sankuai.com/yallist/download/yallist-3.1.1.tgz", "integrity": "sha1-27fa+b/YusmrRev2ArjLrQ1dCP0=", "dev": true, "license": "ISC"}, "node_modules/@babel/helper-create-class-features-plugin": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/helper-create-class-features-plugin/download/@babel/helper-create-class-features-plugin-7.27.1.tgz", "integrity": "sha1-W+5CYqbqXdyFLQgGGZ6xfKPekoE=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.27.1", "@babel/helper-member-expression-to-functions": "^7.27.1", "@babel/helper-optimise-call-expression": "^7.27.1", "@babel/helper-replace-supers": "^7.27.1", "@babel/helper-skip-transparent-expression-wrappers": "^7.27.1", "@babel/traverse": "^7.27.1", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/helper-create-regexp-features-plugin": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/helper-create-regexp-features-plugin/download/@babel/helper-create-regexp-features-plugin-7.27.1.tgz", "integrity": "sha1-BbCILZe6HU0DUZ5LzmFdcK+hjFM=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.27.1", "regexpu-core": "^6.2.0", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/helper-define-polyfill-provider": {"version": "0.6.4", "resolved": "http://r.npm.sankuai.com/@babel/helper-define-polyfill-provider/download/@babel/helper-define-polyfill-provider-0.6.4.tgz", "integrity": "sha1-Feh0Y2i/pnF4X1km/3SzBkwpH6s=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-compilation-targets": "^7.22.6", "@babel/helper-plugin-utils": "^7.22.5", "debug": "^4.1.1", "lodash.debounce": "^4.0.8", "resolve": "^1.14.2"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}}, "node_modules/@babel/helper-member-expression-to-functions": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/helper-member-expression-to-functions/download/@babel/helper-member-expression-to-functions-7.27.1.tgz", "integrity": "sha1-6hIRJ2vpPnmM4ZA32m8G+7mU+kQ=", "dev": true, "license": "MIT", "dependencies": {"@babel/traverse": "^7.27.1", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-module-imports": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/helper-module-imports/download/@babel/helper-module-imports-7.27.1.tgz", "integrity": "sha1-fvdpoyPiZV4SZnO7bS1pE7vq0gQ=", "dev": true, "license": "MIT", "dependencies": {"@babel/traverse": "^7.27.1", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-module-transforms": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/helper-module-transforms/download/@babel/helper-module-transforms-7.27.1.tgz", "integrity": "sha1-4WY7i3HS3pSNpcT7KiDKTz7Cem8=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-module-imports": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1", "@babel/traverse": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/helper-optimise-call-expression": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/helper-optimise-call-expression/download/@babel/helper-optimise-call-expression-7.27.1.tgz", "integrity": "sha1-xlIhthpkPz5icF5d0rXxFeNfkgA=", "dev": true, "license": "MIT", "dependencies": {"@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/helper-plugin-utils/download/@babel/helper-plugin-utils-7.27.1.tgz", "integrity": "sha1-3bL4dlNP+AE+bCspm/TTmzxR1Ew=", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-remap-async-to-generator": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/helper-remap-async-to-generator/download/@babel/helper-remap-async-to-generator-7.27.1.tgz", "integrity": "sha1-RgHVx84usq6lgyjUNyVSP802LOY=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.27.1", "@babel/helper-wrap-function": "^7.27.1", "@babel/traverse": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/helper-replace-supers": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/helper-replace-supers/download/@babel/helper-replace-supers-7.27.1.tgz", "integrity": "sha1-se0tY0zjvbcw5LUt4w+MzP1pK8A=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-member-expression-to-functions": "^7.27.1", "@babel/helper-optimise-call-expression": "^7.27.1", "@babel/traverse": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/helper-skip-transparent-expression-wrappers": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/helper-skip-transparent-expression-wrappers/download/@babel/helper-skip-transparent-expression-wrappers-7.27.1.tgz", "integrity": "sha1-YruRs6u6jH8f7AJS2dvqEbPuelY=", "dev": true, "license": "MIT", "dependencies": {"@babel/traverse": "^7.27.1", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-string-parser": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/helper-string-parser/download/@babel/helper-string-parser-7.27.1.tgz", "integrity": "sha1-VNp5YJerGc5n7Z+ItHuy7Ek2doc=", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-validator-identifier": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/helper-validator-identifier/download/@babel/helper-validator-identifier-7.27.1.tgz", "integrity": "sha1-pwVNzBRaln3U3I/uhFpXwTFsnfg=", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-validator-option": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/helper-validator-option/download/@babel/helper-validator-option-7.27.1.tgz", "integrity": "sha1-+lL1sefbGrBJRFtCHERxMDiXcC8=", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-wrap-function": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/helper-wrap-function/download/@babel/helper-wrap-function-7.27.1.tgz", "integrity": "sha1-uIKFAJwxQnrzGNT+N2Uc1ioUJAk=", "dev": true, "license": "MIT", "dependencies": {"@babel/template": "^7.27.1", "@babel/traverse": "^7.27.1", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helpers": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/helpers/download/@babel/helpers-7.27.1.tgz", "integrity": "sha1-/8JwEwOGB826MojmksNhHAahiqQ=", "dev": true, "license": "MIT", "dependencies": {"@babel/template": "^7.27.1", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/highlight": {"version": "7.25.9", "resolved": "http://r.npm.sankuai.com/@babel/highlight/download/@babel/highlight-7.25.9.tgz", "integrity": "sha1-gUHOaPxzdXlG+YOzQ/EjH0aRrMY=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-validator-identifier": "^7.25.9", "chalk": "^2.4.2", "js-tokens": "^4.0.0", "picocolors": "^1.0.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/highlight/node_modules/ansi-styles": {"version": "3.2.1", "resolved": "http://r.npm.sankuai.com/ansi-styles/download/ansi-styles-3.2.1.tgz", "integrity": "sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0=", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^1.9.0"}, "engines": {"node": ">=4"}}, "node_modules/@babel/highlight/node_modules/chalk": {"version": "2.4.2", "resolved": "http://r.npm.sankuai.com/chalk/download/chalk-2.4.2.tgz", "integrity": "sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ=", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}, "engines": {"node": ">=4"}}, "node_modules/@babel/highlight/node_modules/color-convert": {"version": "1.9.3", "resolved": "http://r.npm.sankuai.com/color-convert/download/color-convert-1.9.3.tgz", "integrity": "sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg=", "dev": true, "license": "MIT", "dependencies": {"color-name": "1.1.3"}}, "node_modules/@babel/highlight/node_modules/color-name": {"version": "1.1.3", "resolved": "http://r.npm.sankuai.com/color-name/download/color-name-1.1.3.tgz", "integrity": "sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=", "dev": true, "license": "MIT"}, "node_modules/@babel/highlight/node_modules/has-flag": {"version": "3.0.0", "resolved": "http://r.npm.sankuai.com/has-flag/download/has-flag-3.0.0.tgz", "integrity": "sha1-tdRU3CGZriJWmfNGfloH87lVuv0=", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/@babel/highlight/node_modules/supports-color": {"version": "5.5.0", "resolved": "http://r.npm.sankuai.com/supports-color/download/supports-color-5.5.0.tgz", "integrity": "sha1-4uaaRKyHcveKHsCzW2id9lMO/I8=", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/@babel/parser": {"version": "7.27.2", "resolved": "http://r.npm.sankuai.com/@babel/parser/download/@babel/parser-7.27.2.tgz", "integrity": "sha1-V3UYvtsXos5CEq/QUuAfffCUESc=", "license": "MIT", "dependencies": {"@babel/types": "^7.27.1"}, "bin": {"parser": "bin/babel-parser.js"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@babel/plugin-bugfix-firefox-class-in-computed-class-key": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/plugin-bugfix-firefox-class-in-computed-class-key/download/@babel/plugin-bugfix-firefox-class-in-computed-class-key-7.27.1.tgz", "integrity": "sha1-Yd2KjmH361aCaNG18SnaPu42S/k=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/traverse": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/plugin-bugfix-safari-class-field-initializer-scope": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/plugin-bugfix-safari-class-field-initializer-scope/download/@babel/plugin-bugfix-safari-class-field-initializer-scope-7.27.1.tgz", "integrity": "sha1-Q/cKbX79UjcO7731WuA9kbKThW0=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/download/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-7.27.1.tgz", "integrity": "sha1-vrYjvVc7i28wR70EwyUGrcPlinI=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining/download/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining-7.27.1.tgz", "integrity": "sha1-4TSlR56yupwCcU6MHr8eyQdhJP0=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-skip-transparent-expression-wrappers": "^7.27.1", "@babel/plugin-transform-optional-chaining": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.13.0"}}, "node_modules/@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly/download/@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly-7.27.1.tgz", "integrity": "sha1-uxwlrzTXURXOIpod5/pEv4+VVnA=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/traverse": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/plugin-proposal-class-properties": {"version": "7.18.6", "resolved": "http://r.npm.sankuai.com/@babel/plugin-proposal-class-properties/download/@babel/plugin-proposal-class-properties-7.18.6.tgz", "integrity": "sha1-sRD1l0GJX37CGm//aW7EYmXERqM=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-create-class-features-plugin": "^7.18.6", "@babel/helper-plugin-utils": "^7.18.6"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-proposal-decorators": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/plugin-proposal-decorators/download/@babel/plugin-proposal-decorators-7.27.1.tgz", "integrity": "sha1-Nob0JLL4sv7nV5qk3xM6T1JEpZY=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-create-class-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1", "@babel/plugin-syntax-decorators": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-proposal-private-property-in-object": {"version": "7.21.0-placeholder-for-preset-env.2", "resolved": "http://r.npm.sankuai.com/@babel/plugin-proposal-private-property-in-object/download/@babel/plugin-proposal-private-property-in-object-7.21.0-placeholder-for-preset-env.2.tgz", "integrity": "sha1-eET5KJVG76n+usLeTP41igUL1wM=", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-decorators": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/plugin-syntax-decorators/download/@babel/plugin-syntax-decorators-7.27.1.tgz", "integrity": "sha1-7n3ZWQruvAX51MjAVgAHsFl5pj0=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-dynamic-import": {"version": "7.8.3", "resolved": "http://r.npm.sankuai.com/@babel/plugin-syntax-dynamic-import/download/@babel/plugin-syntax-dynamic-import-7.8.3.tgz", "integrity": "sha1-Yr+Ysto80h1iYVT8lu5bPLaOrLM=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-import-assertions": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/plugin-syntax-import-assertions/download/@babel/plugin-syntax-import-assertions-7.27.1.tgz", "integrity": "sha1-iIlK79KwO17mrRVip8jhWHSWrs0=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-import-attributes": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/plugin-syntax-import-attributes/download/@babel/plugin-syntax-import-attributes-7.27.1.tgz", "integrity": "sha1-NMAX1USW+bEbYUdOfqPf1VY//gc=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-jsx": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/plugin-syntax-jsx/download/@babel/plugin-syntax-jsx-7.27.1.tgz", "integrity": "sha1-L5vrXv8w+lB8VTLRB9qse4iPo0w=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-unicode-sets-regex": {"version": "7.18.6", "resolved": "http://r.npm.sankuai.com/@babel/plugin-syntax-unicode-sets-regex/download/@babel/plugin-syntax-unicode-sets-regex-7.18.6.tgz", "integrity": "sha1-1Jo7PmtS5b5nQAIjF1gCNKakc1c=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.18.6", "@babel/helper-plugin-utils": "^7.18.6"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/plugin-transform-arrow-functions": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/plugin-transform-arrow-functions/download/@babel/plugin-transform-arrow-functions-7.27.1.tgz", "integrity": "sha1-biBhBnujqwJm2DSp+UgRGW8qupo=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-async-generator-functions": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/plugin-transform-async-generator-functions/download/@babel/plugin-transform-async-generator-functions-7.27.1.tgz", "integrity": "sha1-ykM9+YPWjhN1OY58pxvypPb9idc=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-remap-async-to-generator": "^7.27.1", "@babel/traverse": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-async-to-generator": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/plugin-transform-async-to-generator/download/@babel/plugin-transform-async-to-generator-7.27.1.tgz", "integrity": "sha1-mpOJO5N5s5Rmx0R09VrwPeeMZuc=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-module-imports": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-remap-async-to-generator": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-block-scoped-functions": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/plugin-transform-block-scoped-functions/download/@babel/plugin-transform-block-scoped-functions-7.27.1.tgz", "integrity": "sha1-VYqdbiTPcoAt07YqS1Hg1iwPV/k=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-block-scoping": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/plugin-transform-block-scoping/download/@babel/plugin-transform-block-scoping-7.27.1.tgz", "integrity": "sha1-vA2+isbeVgKYG6WO9oxt+O+b+7M=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-class-properties": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/plugin-transform-class-properties/download/@babel/plugin-transform-class-properties-7.27.1.tgz", "integrity": "sha1-3UCmo3Df1J0yNiriBt2vK7CCqSU=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-create-class-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-class-static-block": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/plugin-transform-class-static-block/download/@babel/plugin-transform-class-static-block-7.27.1.tgz", "integrity": "sha1-fpINViWyW7zNMGGu+8wFgF7VbOQ=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-create-class-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.12.0"}}, "node_modules/@babel/plugin-transform-classes": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/plugin-transform-classes/download/@babel/plugin-transform-classes-7.27.1.tgz", "integrity": "sha1-A7sEvqLHsvcR8NtzBKjaRqhcztQ=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.27.1", "@babel/helper-compilation-targets": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-replace-supers": "^7.27.1", "@babel/traverse": "^7.27.1", "globals": "^11.1.0"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-computed-properties": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/plugin-transform-computed-properties/download/@babel/plugin-transform-computed-properties-7.27.1.tgz", "integrity": "sha1-gWYueL9ec0qXmCwrfwp5MojvPKo=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/template": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-destructuring": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/plugin-transform-destructuring/download/@babel/plugin-transform-destructuring-7.27.1.tgz", "integrity": "sha1-1ZFu9wicslTfBBiuUkUzwbcrplY=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-dotall-regex": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/plugin-transform-dotall-regex/download/@babel/plugin-transform-dotall-regex-7.27.1.tgz", "integrity": "sha1-qmgh3oZMUosf7PKG8KF0446Cb00=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-duplicate-keys": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/plugin-transform-duplicate-keys/download/@babel/plugin-transform-duplicate-keys-7.27.1.tgz", "integrity": "sha1-8fv2KOzhjhLnsysXWUDmg1j1RtE=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-duplicate-named-capturing-groups-regex": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/plugin-transform-duplicate-named-capturing-groups-regex/download/@babel/plugin-transform-duplicate-named-capturing-groups-regex-7.27.1.tgz", "integrity": "sha1-UEOFTKYgqUFJNy5pAw/4y2qesOw=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/plugin-transform-dynamic-import": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/plugin-transform-dynamic-import/download/@babel/plugin-transform-dynamic-import-7.27.1.tgz", "integrity": "sha1-THjzVVKsDgaqH248Vz1naV6K9aQ=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-exponentiation-operator": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/plugin-transform-exponentiation-operator/download/@babel/plugin-transform-exponentiation-operator-7.27.1.tgz", "integrity": "sha1-/El7EtgnflWXR/Wj7YaN2AZPg+E=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-export-namespace-from": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/plugin-transform-export-namespace-from/download/@babel/plugin-transform-export-namespace-from-7.27.1.tgz", "integrity": "sha1-ccpp00ce3W2qcRz038NABBXfnCM=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-for-of": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/plugin-transform-for-of/download/@babel/plugin-transform-for-of-7.27.1.tgz", "integrity": "sha1-vCT3CA6f9yG2OnCseyVkyhW2xAo=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-skip-transparent-expression-wrappers": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-function-name": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/plugin-transform-function-name/download/@babel/plugin-transform-function-name-7.27.1.tgz", "integrity": "sha1-TQvzB3IOTc5tfDD8sf1sp3ves6c=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-compilation-targets": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1", "@babel/traverse": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-json-strings": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/plugin-transform-json-strings/download/@babel/plugin-transform-json-strings-7.27.1.tgz", "integrity": "sha1-ouDObvJWN2vVJ/KQ2gI5g1J6T0w=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-literals": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/plugin-transform-literals/download/@babel/plugin-transform-literals-7.27.1.tgz", "integrity": "sha1-uq76TRCh1CBvnc3aUNfVgnu3CyQ=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-logical-assignment-operators": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/plugin-transform-logical-assignment-operators/download/@babel/plugin-transform-logical-assignment-operators-7.27.1.tgz", "integrity": "sha1-iQyyDgJw4OW+vj8CW0NIQcMtW6o=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-member-expression-literals": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/plugin-transform-member-expression-literals/download/@babel/plugin-transform-member-expression-literals-7.27.1.tgz", "integrity": "sha1-N7iLpZTYUkGOmVNvVhL3lfI66vk=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-modules-amd": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/plugin-transform-modules-amd/download/@babel/plugin-transform-modules-amd-7.27.1.tgz", "integrity": "sha1-pBRfnYfCKR/i0F+ZS2XbpOPnGW8=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-module-transforms": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-modules-commonjs": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/plugin-transform-modules-commonjs/download/@babel/plugin-transform-modules-commonjs-7.27.1.tgz", "integrity": "sha1-jkTtN8J4fswjvcNn9Jl3R2YU6DI=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-module-transforms": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-modules-systemjs": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/plugin-transform-modules-systemjs/download/@babel/plugin-transform-modules-systemjs-7.27.1.tgz", "integrity": "sha1-AOBbYYYwcNDzKSoAEmwWwOAkxO0=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-module-transforms": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1", "@babel/traverse": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-modules-umd": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/plugin-transform-modules-umd/download/@babel/plugin-transform-modules-umd-7.27.1.tgz", "integrity": "sha1-Y/LPT23BXevBL2lORHFIY9NM0zQ=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-module-transforms": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-named-capturing-groups-regex": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/plugin-transform-named-capturing-groups-regex/download/@babel/plugin-transform-named-capturing-groups-regex-7.27.1.tgz", "integrity": "sha1-8yuPeBjY/AzEbuIKjvdfBxr5duE=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/plugin-transform-new-target": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/plugin-transform-new-target/download/@babel/plugin-transform-new-target-7.27.1.tgz", "integrity": "sha1-JZxDk5coytFwasFzUbfmp76hq+s=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-nullish-coalescing-operator": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/plugin-transform-nullish-coalescing-operator/download/@babel/plugin-transform-nullish-coalescing-operator-7.27.1.tgz", "integrity": "sha1-T50xU79ngtc91CeFqdItAxl7yR0=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-numeric-separator": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/plugin-transform-numeric-separator/download/@babel/plugin-transform-numeric-separator-7.27.1.tgz", "integrity": "sha1-YU4LFcyADlmX2t2b1upSTtbIGcY=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-object-rest-spread": {"version": "7.27.2", "resolved": "http://r.npm.sankuai.com/@babel/plugin-transform-object-rest-spread/download/@babel/plugin-transform-object-rest-spread-7.27.2.tgz", "integrity": "sha1-Z/mrgiNHqivO6R6JlnY9p5veqXM=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-compilation-targets": "^7.27.2", "@babel/helper-plugin-utils": "^7.27.1", "@babel/plugin-transform-destructuring": "^7.27.1", "@babel/plugin-transform-parameters": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-object-super": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/plugin-transform-object-super/download/@babel/plugin-transform-object-super-7.27.1.tgz", "integrity": "sha1-HJMs0nvzh0xDpcrE9D6/lwyYcbU=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-replace-supers": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-optional-catch-binding": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/plugin-transform-optional-catch-binding/download/@babel/plugin-transform-optional-catch-binding-7.27.1.tgz", "integrity": "sha1-hMc0Hr3jXM02sTfp5FhmglByoww=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-optional-chaining": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/plugin-transform-optional-chaining/download/@babel/plugin-transform-optional-chaining-7.27.1.tgz", "integrity": "sha1-h0zjxPBrd4BZLpRgJut2oygwRU8=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-skip-transparent-expression-wrappers": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-parameters": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/plugin-transform-parameters/download/@babel/plugin-transform-parameters-7.27.1.tgz", "integrity": "sha1-gDNLVLmxrFJEFVoMgwShh6YY1ac=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-private-methods": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/plugin-transform-private-methods/download/@babel/plugin-transform-private-methods-7.27.1.tgz", "integrity": "sha1-/ay6scXtgexw39u4shPWXaFItq8=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-create-class-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-private-property-in-object": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/plugin-transform-private-property-in-object/download/@babel/plugin-transform-private-property-in-object-7.27.1.tgz", "integrity": "sha1-TbvvKDtbLwGiHoHimfduNfkA+xE=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.27.1", "@babel/helper-create-class-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-property-literals": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/plugin-transform-property-literals/download/@babel/plugin-transform-property-literals-7.27.1.tgz", "integrity": "sha1-B+r9YYgAWR6IBzoK8blA2aQsZCQ=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-regenerator": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/plugin-transform-regenerator/download/@babel/plugin-transform-regenerator-7.27.1.tgz", "integrity": "sha1-Ckcd+SE0FuRM1mv2cXa2b2V2hAE=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-regexp-modifiers": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/plugin-transform-regexp-modifiers/download/@babel/plugin-transform-regexp-modifiers-7.27.1.tgz", "integrity": "sha1-35ulV3yXTj8USYiLcLdhaZmKbQk=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/plugin-transform-reserved-words": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/plugin-transform-reserved-words/download/@babel/plugin-transform-reserved-words-7.27.1.tgz", "integrity": "sha1-QPukh4zL0cVmBaRHmjqJGsAnS7Q=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-runtime": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/plugin-transform-runtime/download/@babel/plugin-transform-runtime-7.27.1.tgz", "integrity": "sha1-+fv3GUmiCesms+YDdbHZVpN7i+k=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-module-imports": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1", "babel-plugin-polyfill-corejs2": "^0.4.10", "babel-plugin-polyfill-corejs3": "^0.11.0", "babel-plugin-polyfill-regenerator": "^0.6.1", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-shorthand-properties": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/plugin-transform-shorthand-properties/download/@babel/plugin-transform-shorthand-properties-7.27.1.tgz", "integrity": "sha1-Uyq9rN7Ie/7h4O+OL83uVD/jK5A=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-spread": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/plugin-transform-spread/download/@babel/plugin-transform-spread-7.27.1.tgz", "integrity": "sha1-GiZNX8EnUJGPUOP+PiTkNxeKuwg=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-skip-transparent-expression-wrappers": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-sticky-regex": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/plugin-transform-sticky-regex/download/@babel/plugin-transform-sticky-regex-7.27.1.tgz", "integrity": "sha1-GJhJNdnSKWhDpJHXigFJOffc0oA=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-template-literals": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/plugin-transform-template-literals/download/@babel/plugin-transform-template-literals-7.27.1.tgz", "integrity": "sha1-Gg6zXYuz5u/AbJ/UDrC871SDKLg=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-typeof-symbol": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/plugin-transform-typeof-symbol/download/@babel/plugin-transform-typeof-symbol-7.27.1.tgz", "integrity": "sha1-cOlmu0kuA1Cc836vptzDBR+EQ2k=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-unicode-escapes": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/plugin-transform-unicode-escapes/download/@babel/plugin-transform-unicode-escapes-7.27.1.tgz", "integrity": "sha1-PjFD+EOK74Qt4ogW7OWHgBkM+AY=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-unicode-property-regex": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/plugin-transform-unicode-property-regex/download/@babel/plugin-transform-unicode-property-regex-7.27.1.tgz", "integrity": "sha1-vf4tMXDHjFaRo8O+k0yMAIdSWVY=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-unicode-regex": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/plugin-transform-unicode-regex/download/@babel/plugin-transform-unicode-regex-7.27.1.tgz", "integrity": "sha1-JZSPXDldsV9gkCjjcGZ+2Lrpr5c=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-unicode-sets-regex": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/plugin-transform-unicode-sets-regex/download/@babel/plugin-transform-unicode-sets-regex-7.27.1.tgz", "integrity": "sha1-arcG0Q+AG1xy2ouyVIVh+gQZPNE=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/preset-env": {"version": "7.27.2", "resolved": "http://r.npm.sankuai.com/@babel/preset-env/download/@babel/preset-env-7.27.2.tgz", "integrity": "sha1-EG5r+tkrWRsfb3b9TPE7dyWnv5o=", "dev": true, "license": "MIT", "dependencies": {"@babel/compat-data": "^7.27.2", "@babel/helper-compilation-targets": "^7.27.2", "@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-validator-option": "^7.27.1", "@babel/plugin-bugfix-firefox-class-in-computed-class-key": "^7.27.1", "@babel/plugin-bugfix-safari-class-field-initializer-scope": "^7.27.1", "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression": "^7.27.1", "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining": "^7.27.1", "@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly": "^7.27.1", "@babel/plugin-proposal-private-property-in-object": "7.21.0-placeholder-for-preset-env.2", "@babel/plugin-syntax-import-assertions": "^7.27.1", "@babel/plugin-syntax-import-attributes": "^7.27.1", "@babel/plugin-syntax-unicode-sets-regex": "^7.18.6", "@babel/plugin-transform-arrow-functions": "^7.27.1", "@babel/plugin-transform-async-generator-functions": "^7.27.1", "@babel/plugin-transform-async-to-generator": "^7.27.1", "@babel/plugin-transform-block-scoped-functions": "^7.27.1", "@babel/plugin-transform-block-scoping": "^7.27.1", "@babel/plugin-transform-class-properties": "^7.27.1", "@babel/plugin-transform-class-static-block": "^7.27.1", "@babel/plugin-transform-classes": "^7.27.1", "@babel/plugin-transform-computed-properties": "^7.27.1", "@babel/plugin-transform-destructuring": "^7.27.1", "@babel/plugin-transform-dotall-regex": "^7.27.1", "@babel/plugin-transform-duplicate-keys": "^7.27.1", "@babel/plugin-transform-duplicate-named-capturing-groups-regex": "^7.27.1", "@babel/plugin-transform-dynamic-import": "^7.27.1", "@babel/plugin-transform-exponentiation-operator": "^7.27.1", "@babel/plugin-transform-export-namespace-from": "^7.27.1", "@babel/plugin-transform-for-of": "^7.27.1", "@babel/plugin-transform-function-name": "^7.27.1", "@babel/plugin-transform-json-strings": "^7.27.1", "@babel/plugin-transform-literals": "^7.27.1", "@babel/plugin-transform-logical-assignment-operators": "^7.27.1", "@babel/plugin-transform-member-expression-literals": "^7.27.1", "@babel/plugin-transform-modules-amd": "^7.27.1", "@babel/plugin-transform-modules-commonjs": "^7.27.1", "@babel/plugin-transform-modules-systemjs": "^7.27.1", "@babel/plugin-transform-modules-umd": "^7.27.1", "@babel/plugin-transform-named-capturing-groups-regex": "^7.27.1", "@babel/plugin-transform-new-target": "^7.27.1", "@babel/plugin-transform-nullish-coalescing-operator": "^7.27.1", "@babel/plugin-transform-numeric-separator": "^7.27.1", "@babel/plugin-transform-object-rest-spread": "^7.27.2", "@babel/plugin-transform-object-super": "^7.27.1", "@babel/plugin-transform-optional-catch-binding": "^7.27.1", "@babel/plugin-transform-optional-chaining": "^7.27.1", "@babel/plugin-transform-parameters": "^7.27.1", "@babel/plugin-transform-private-methods": "^7.27.1", "@babel/plugin-transform-private-property-in-object": "^7.27.1", "@babel/plugin-transform-property-literals": "^7.27.1", "@babel/plugin-transform-regenerator": "^7.27.1", "@babel/plugin-transform-regexp-modifiers": "^7.27.1", "@babel/plugin-transform-reserved-words": "^7.27.1", "@babel/plugin-transform-shorthand-properties": "^7.27.1", "@babel/plugin-transform-spread": "^7.27.1", "@babel/plugin-transform-sticky-regex": "^7.27.1", "@babel/plugin-transform-template-literals": "^7.27.1", "@babel/plugin-transform-typeof-symbol": "^7.27.1", "@babel/plugin-transform-unicode-escapes": "^7.27.1", "@babel/plugin-transform-unicode-property-regex": "^7.27.1", "@babel/plugin-transform-unicode-regex": "^7.27.1", "@babel/plugin-transform-unicode-sets-regex": "^7.27.1", "@babel/preset-modules": "0.1.6-no-external-plugins", "babel-plugin-polyfill-corejs2": "^0.4.10", "babel-plugin-polyfill-corejs3": "^0.11.0", "babel-plugin-polyfill-regenerator": "^0.6.1", "core-js-compat": "^3.40.0", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/preset-modules": {"version": "0.1.6-no-external-plugins", "resolved": "http://r.npm.sankuai.com/@babel/preset-modules/download/@babel/preset-modules-0.1.6-no-external-plugins.tgz", "integrity": "sha1-zLiKLEnIFyNoYf7ngmCAVzuKkjo=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/types": "^7.4.4", "esutils": "^2.0.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^8.0.0-0 <8.0.0"}}, "node_modules/@babel/runtime": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/runtime/download/@babel/runtime-7.27.1.tgz", "integrity": "sha1-n84xPRLJp3UH8mTedGJuh/0NxUE=", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/template": {"version": "7.27.2", "resolved": "http://r.npm.sankuai.com/@babel/template/download/@babel/template-7.27.2.tgz", "integrity": "sha1-+njO7TxOe2Pr9ss55YUvykX2gJ0=", "dev": true, "license": "MIT", "dependencies": {"@babel/code-frame": "^7.27.1", "@babel/parser": "^7.27.2", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/traverse": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/traverse/download/@babel/traverse-7.27.1.tgz", "integrity": "sha1-TbdykCsTO73dHE96fuR3YcG58pE=", "dev": true, "license": "MIT", "dependencies": {"@babel/code-frame": "^7.27.1", "@babel/generator": "^7.27.1", "@babel/parser": "^7.27.1", "@babel/template": "^7.27.1", "@babel/types": "^7.27.1", "debug": "^4.3.1", "globals": "^11.1.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/types": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/types/download/@babel/types-7.27.1.tgz", "integrity": "sha1-ne/FPBb8iZ5GlB/GkBqe6hydhWA=", "license": "MIT", "dependencies": {"@babel/helper-string-parser": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@discoveryjs/json-ext": {"version": "0.5.7", "resolved": "http://r.npm.sankuai.com/@discoveryjs/json-ext/download/@discoveryjs/json-ext-0.5.7.tgz", "integrity": "sha1-HVcr+74Ut3BOC6Dzm3SBW4SHDXA=", "dev": true, "license": "MIT", "engines": {"node": ">=10.0.0"}}, "node_modules/@eslint/eslintrc": {"version": "0.4.3", "resolved": "http://r.npm.sankuai.com/@eslint/eslintrc/download/@eslint/eslintrc-0.4.3.tgz", "integrity": "sha1-nkKYHvA1vrPdSa3ResuW6P9vOUw=", "dev": true, "license": "MIT", "dependencies": {"ajv": "^6.12.4", "debug": "^4.1.1", "espree": "^7.3.0", "globals": "^13.9.0", "ignore": "^4.0.6", "import-fresh": "^3.2.1", "js-yaml": "^3.13.1", "minimatch": "^3.0.4", "strip-json-comments": "^3.1.1"}, "engines": {"node": "^10.12.0 || >=12.0.0"}}, "node_modules/@eslint/eslintrc/node_modules/globals": {"version": "13.24.0", "resolved": "http://r.npm.sankuai.com/globals/download/globals-13.24.0.tgz", "integrity": "sha1-hDKhnXjODB6DOUnDats0VAC7EXE=", "dev": true, "license": "MIT", "dependencies": {"type-fest": "^0.20.2"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@hapi/hoek": {"version": "9.3.0", "resolved": "http://r.npm.sankuai.com/@hapi/hoek/download/@hapi/hoek-9.3.0.tgz", "integrity": "sha1-g2iGnctzW+Ln9ct2R9544WeiUfs=", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@hapi/topo": {"version": "5.1.0", "resolved": "http://r.npm.sankuai.com/@hapi/topo/download/@hapi/topo-5.1.0.tgz", "integrity": "sha1-3ESOMyxsbjek3AL9hLqNRLmvsBI=", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@hapi/hoek": "^9.0.0"}}, "node_modules/@humanwhocodes/config-array": {"version": "0.5.0", "resolved": "http://r.npm.sankuai.com/@humanwhocodes/config-array/download/@humanwhocodes/config-array-0.5.0.tgz", "integrity": "sha1-FAeWfUxu7Nc4j4Os8er00Mbljvk=", "dev": true, "license": "Apache-2.0", "dependencies": {"@humanwhocodes/object-schema": "^1.2.0", "debug": "^4.1.1", "minimatch": "^3.0.4"}, "engines": {"node": ">=10.10.0"}}, "node_modules/@humanwhocodes/object-schema": {"version": "1.2.1", "resolved": "http://r.npm.sankuai.com/@humanwhocodes/object-schema/download/@humanwhocodes/object-schema-1.2.1.tgz", "integrity": "sha1-tSBSnsIdjllFoYUd/Rwy6U45/0U=", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@jridgewell/gen-mapping": {"version": "0.3.8", "resolved": "http://r.npm.sankuai.com/@jridgewell/gen-mapping/download/@jridgewell/gen-mapping-0.3.8.tgz", "integrity": "sha1-Tw4GNi4BNi+CPTSPGHKwj2ZtgUI=", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/set-array": "^1.2.1", "@jridgewell/sourcemap-codec": "^1.4.10", "@jridgewell/trace-mapping": "^0.3.24"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/resolve-uri": {"version": "3.1.2", "resolved": "http://r.npm.sankuai.com/@jridgewell/resolve-uri/download/@jridgewell/resolve-uri-3.1.2.tgz", "integrity": "sha1-eg7mAfYPmaIMfHxf8MgDiMEYm9Y=", "dev": true, "license": "MIT", "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/set-array": {"version": "1.2.1", "resolved": "http://r.npm.sankuai.com/@jridgewell/set-array/download/@jridgewell/set-array-1.2.1.tgz", "integrity": "sha1-VY+2Ry7RakyFC4iVMOazZDjEkoA=", "dev": true, "license": "MIT", "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/source-map": {"version": "0.3.6", "resolved": "http://r.npm.sankuai.com/@jridgewell/source-map/download/@jridgewell/source-map-0.3.6.tgz", "integrity": "sha1-nXHKiG4yUC65NiyadKRnh8Nt+Bo=", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25"}}, "node_modules/@jridgewell/sourcemap-codec": {"version": "1.5.0", "resolved": "http://r.npm.sankuai.com/@jridgewell/sourcemap-codec/download/@jridgewell/sourcemap-codec-1.5.0.tgz", "integrity": "sha1-MYi8snOkFLDSFf0ipYVAuYm5QJo=", "dev": true, "license": "MIT"}, "node_modules/@jridgewell/trace-mapping": {"version": "0.3.25", "resolved": "http://r.npm.sankuai.com/@jridgewell/trace-mapping/download/@jridgewell/trace-mapping-0.3.25.tgz", "integrity": "sha1-FfGQ6YiV8/wjJ27hS8drZ1wuUPA=", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/resolve-uri": "^3.1.0", "@jridgewell/sourcemap-codec": "^1.4.14"}}, "node_modules/@leichtgewicht/ip-codec": {"version": "2.0.5", "resolved": "http://r.npm.sankuai.com/@leichtgewicht/ip-codec/download/@leichtgewicht/ip-codec-2.0.5.tgz", "integrity": "sha1-T8VsFcWAua233DwzOhNOVAtEv7E=", "dev": true, "license": "MIT"}, "node_modules/@nicolo-ribaudo/eslint-scope-5-internals": {"version": "5.1.1-v1", "resolved": "http://r.npm.sankuai.com/@nicolo-ribaudo/eslint-scope-5-internals/download/@nicolo-ribaudo/eslint-scope-5-internals-5.1.1-v1.tgz", "integrity": "sha1-2/czqWXKR7GXMXfcC7bIie3PsSk=", "dev": true, "license": "MIT", "dependencies": {"eslint-scope": "5.1.1"}}, "node_modules/@node-ipc/js-queue": {"version": "2.0.3", "resolved": "http://r.npm.sankuai.com/@node-ipc/js-queue/download/@node-ipc/js-queue-2.0.3.tgz", "integrity": "sha1-rH/jPXZvpT4jPvj+2vNEOgHFpM0=", "dev": true, "license": "MIT", "dependencies": {"easy-stack": "1.0.1"}, "engines": {"node": ">=1.0.0"}}, "node_modules/@nodelib/fs.scandir": {"version": "2.1.5", "resolved": "http://r.npm.sankuai.com/@nodelib/fs.scandir/download/@nodelib/fs.scandir-2.1.5.tgz", "integrity": "sha1-dhnC6yGyVIP20WdUi0z9WnSIw9U=", "dev": true, "license": "MIT", "dependencies": {"@nodelib/fs.stat": "2.0.5", "run-parallel": "^1.1.9"}, "engines": {"node": ">= 8"}}, "node_modules/@nodelib/fs.stat": {"version": "2.0.5", "resolved": "http://r.npm.sankuai.com/@nodelib/fs.stat/download/@nodelib/fs.stat-2.0.5.tgz", "integrity": "sha1-W9Jir5Tp0lvR5xsF3u1Eh2oiLos=", "dev": true, "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/@nodelib/fs.walk": {"version": "1.2.8", "resolved": "http://r.npm.sankuai.com/@nodelib/fs.walk/download/@nodelib/fs.walk-1.2.8.tgz", "integrity": "sha1-6Vc36LtnRt3t9pxVaVNJTxlv5po=", "dev": true, "license": "MIT", "dependencies": {"@nodelib/fs.scandir": "2.1.5", "fastq": "^1.6.0"}, "engines": {"node": ">= 8"}}, "node_modules/@polka/url": {"version": "1.0.0-next.29", "resolved": "http://r.npm.sankuai.com/@polka/url/download/@polka/url-1.0.0-next.29.tgz", "integrity": "sha1-WkAQmhq1+E1v2PySixnzZ8vn57E=", "dev": true, "license": "MIT"}, "node_modules/@sideway/address": {"version": "4.1.5", "resolved": "http://r.npm.sankuai.com/@sideway/address/download/@sideway/address-4.1.5.tgz", "integrity": "sha1-S8FJoAdmI87ZnKggi6eA1lqZudU=", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@hapi/hoek": "^9.0.0"}}, "node_modules/@sideway/formula": {"version": "3.0.1", "resolved": "http://r.npm.sankuai.com/@sideway/formula/download/@sideway/formula-3.0.1.tgz", "integrity": "sha1-gPy8uvfOAx4O8t0psb/Hw/WDYR8=", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@sideway/pinpoint": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/@sideway/pinpoint/download/@sideway/pinpoint-2.0.0.tgz", "integrity": "sha1-z/j/rcNyrSn9P3gneusp5jLMcN8=", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@soda/friendly-errors-webpack-plugin": {"version": "1.8.1", "resolved": "http://r.npm.sankuai.com/@soda/friendly-errors-webpack-plugin/download/@soda/friendly-errors-webpack-plugin-1.8.1.tgz", "integrity": "sha1-TU+7EQiZOqo2IRYkfD0YGIosbIU=", "dev": true, "license": "MIT", "dependencies": {"chalk": "^3.0.0", "error-stack-parser": "^2.0.6", "string-width": "^4.2.3", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8.0.0"}, "peerDependencies": {"webpack": "^4.0.0 || ^5.0.0"}}, "node_modules/@soda/friendly-errors-webpack-plugin/node_modules/chalk": {"version": "3.0.0", "resolved": "http://r.npm.sankuai.com/chalk/download/chalk-3.0.0.tgz", "integrity": "sha1-P3PCv1JlkfV0zEksUeJFY0n4ROQ=", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=8"}}, "node_modules/@soda/get-current-script": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/@soda/get-current-script/download/@soda/get-current-script-1.0.2.tgz", "integrity": "sha1-pTUV2yXYA4N0OBtzryC7Ty5QjYc=", "dev": true, "license": "MIT"}, "node_modules/@techstark/opencv-js": {"version": "4.10.0-release.1", "resolved": "http://r.npm.sankuai.com/@techstark/opencv-js/download/@techstark/opencv-js-4.10.0-release.1.tgz", "integrity": "sha1-U0THFcQEwiQZrIk8ai2Gcn4kFvw=", "license": "Apache-2.0"}, "node_modules/@trysound/sax": {"version": "0.2.0", "resolved": "http://r.npm.sankuai.com/@trysound/sax/download/@trysound/sax-0.2.0.tgz", "integrity": "sha1-zMqrdYr1Z2Hre/N69vA/Mm3XmK0=", "dev": true, "license": "ISC", "engines": {"node": ">=10.13.0"}}, "node_modules/@types/body-parser": {"version": "1.19.5", "resolved": "http://r.npm.sankuai.com/@types/body-parser/download/@types/body-parser-1.19.5.tgz", "integrity": "sha1-BM6aO2d9yL1oGhfaGrmDXcnT7eQ=", "dev": true, "license": "MIT", "dependencies": {"@types/connect": "*", "@types/node": "*"}}, "node_modules/@types/bonjour": {"version": "3.5.13", "resolved": "http://r.npm.sankuai.com/@types/bonjour/download/@types/bonjour-3.5.13.tgz", "integrity": "sha1-rfkM4aEF6B3R+cYf3Fr9ob+5KVY=", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/connect": {"version": "3.4.38", "resolved": "http://r.npm.sankuai.com/@types/connect/download/@types/connect-3.4.38.tgz", "integrity": "sha1-W6fzvE+73q/43e2VLl/yzFP42Fg=", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/connect-history-api-fallback": {"version": "1.5.4", "resolved": "http://r.npm.sankuai.com/@types/connect-history-api-fallback/download/@types/connect-history-api-fallback-1.5.4.tgz", "integrity": "sha1-fecWRaEDBWtIrDzgezUguBnB1bM=", "dev": true, "license": "MIT", "dependencies": {"@types/express-serve-static-core": "*", "@types/node": "*"}}, "node_modules/@types/eslint": {"version": "8.56.12", "resolved": "http://r.npm.sankuai.com/@types/eslint/download/@types/eslint-8.56.12.tgz", "integrity": "sha1-FlfIFP/rpNL4TA1LoPRMp+ocpTo=", "dev": true, "license": "MIT", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}}, "node_modules/@types/eslint-scope": {"version": "3.7.7", "resolved": "http://r.npm.sankuai.com/@types/eslint-scope/download/@types/eslint-scope-3.7.7.tgz", "integrity": "sha1-MQi9XxiwzbJ3yGez3UScntcHmsU=", "dev": true, "license": "MIT", "dependencies": {"@types/eslint": "*", "@types/estree": "*"}}, "node_modules/@types/eslint-scope/node_modules/@types/eslint": {"version": "9.6.1", "resolved": "http://r.npm.sankuai.com/@types/eslint/download/@types/eslint-9.6.1.tgz", "integrity": "sha1-1Xla1zLOgXFfJ/ddqRMASlZ1FYQ=", "dev": true, "license": "MIT", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}}, "node_modules/@types/estree": {"version": "1.0.7", "resolved": "http://r.npm.sankuai.com/@types/estree/download/@types/estree-1.0.7.tgz", "integrity": "sha1-QVjTEFJ2dz1bdpXNSDSxci5PN6g=", "dev": true, "license": "MIT"}, "node_modules/@types/express": {"version": "4.17.21", "resolved": "http://r.npm.sankuai.com/@types/express/download/@types/express-4.17.21.tgz", "integrity": "sha1-wm1KFR5g7+AISyPcM2nrxjHtGS0=", "dev": true, "license": "MIT", "dependencies": {"@types/body-parser": "*", "@types/express-serve-static-core": "^4.17.33", "@types/qs": "*", "@types/serve-static": "*"}}, "node_modules/@types/express-serve-static-core": {"version": "5.0.6", "resolved": "http://r.npm.sankuai.com/@types/express-serve-static-core/download/@types/express-serve-static-core-5.0.6.tgz", "integrity": "sha1-Qf7E6iDpx7IvAkq4ipXGuyiPUbg=", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*", "@types/qs": "*", "@types/range-parser": "*", "@types/send": "*"}}, "node_modules/@types/express/node_modules/@types/express-serve-static-core": {"version": "4.19.6", "resolved": "http://r.npm.sankuai.com/@types/express-serve-static-core/download/@types/express-serve-static-core-4.19.6.tgz", "integrity": "sha1-4BMkwqAk/zZ9ksZvSFU87Qq1Amc=", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*", "@types/qs": "*", "@types/range-parser": "*", "@types/send": "*"}}, "node_modules/@types/html-minifier-terser": {"version": "6.1.0", "resolved": "http://r.npm.sankuai.com/@types/html-minifier-terser/download/@types/html-minifier-terser-6.1.0.tgz", "integrity": "sha1-T8M6AMHQwWmHsaIM+S0gYUxVrDU=", "dev": true, "license": "MIT"}, "node_modules/@types/http-errors": {"version": "2.0.4", "resolved": "http://r.npm.sankuai.com/@types/http-errors/download/@types/http-errors-2.0.4.tgz", "integrity": "sha1-frR3JsORtzRabsNa1/TeRpz1uk8=", "dev": true, "license": "MIT"}, "node_modules/@types/http-proxy": {"version": "1.17.16", "resolved": "http://r.npm.sankuai.com/@types/http-proxy/download/@types/http-proxy-1.17.16.tgz", "integrity": "sha1-3uNgcHs1s8yFr83on/7r/31/kkA=", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/json-schema": {"version": "7.0.15", "resolved": "http://r.npm.sankuai.com/@types/json-schema/download/@types/json-schema-7.0.15.tgz", "integrity": "sha1-WWoXRyM2lNUPatinhp/Lb1bPWEE=", "dev": true, "license": "MIT"}, "node_modules/@types/mime": {"version": "1.3.5", "resolved": "http://r.npm.sankuai.com/@types/mime/download/@types/mime-1.3.5.tgz", "integrity": "sha1-HvMC4Bz30rWg+lJnkMkSO/HQZpA=", "dev": true, "license": "MIT"}, "node_modules/@types/minimist": {"version": "1.2.5", "resolved": "http://r.npm.sankuai.com/@types/minimist/download/@types/minimist-1.2.5.tgz", "integrity": "sha1-7BB1XocUl7zYPv6SfkPsRujAdH4=", "dev": true, "license": "MIT"}, "node_modules/@types/node": {"version": "22.15.18", "resolved": "http://r.npm.sankuai.com/@types/node/download/@types/node-22.15.18.tgz", "integrity": "sha1-L4JA9+ky9XHC1F9VW6C2w/enWWM=", "license": "MIT", "dependencies": {"undici-types": "~6.21.0"}}, "node_modules/@types/node-fetch": {"version": "2.6.12", "resolved": "http://r.npm.sankuai.com/@types/node-fetch/download/@types/node-fetch-2.6.12.tgz", "integrity": "sha1-irXD74Mw8TEAp0eeLNVtM4aDCgM=", "license": "MIT", "dependencies": {"@types/node": "*", "form-data": "^4.0.0"}}, "node_modules/@types/node-forge": {"version": "1.3.11", "resolved": "http://r.npm.sankuai.com/@types/node-forge/download/@types/node-forge-1.3.11.tgz", "integrity": "sha1-CXLqU43bD02cL6DsXbVyR3OmBNo=", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/normalize-package-data": {"version": "2.4.4", "resolved": "http://r.npm.sankuai.com/@types/normalize-package-data/download/@types/normalize-package-data-2.4.4.tgz", "integrity": "sha1-VuLMJsOXwDj6sOOpF6EtXFkJ6QE=", "dev": true, "license": "MIT"}, "node_modules/@types/parse-json": {"version": "4.0.2", "resolved": "http://r.npm.sankuai.com/@types/parse-json/download/@types/parse-json-4.0.2.tgz", "integrity": "sha1-WVDlCWB5MFWEXpVsQn/CsNcMUjk=", "dev": true, "license": "MIT"}, "node_modules/@types/qs": {"version": "6.9.18", "resolved": "http://r.npm.sankuai.com/@types/qs/download/@types/qs-6.9.18.tgz", "integrity": "sha1-h3KSyqkffBshMDKzRiZQW3RmJMI=", "dev": true, "license": "MIT"}, "node_modules/@types/range-parser": {"version": "1.2.7", "resolved": "http://r.npm.sankuai.com/@types/range-parser/download/@types/range-parser-1.2.7.tgz", "integrity": "sha1-UK5DU+qt3AQEQnmBL1LIxlhX28s=", "dev": true, "license": "MIT"}, "node_modules/@types/retry": {"version": "0.12.0", "resolved": "http://r.npm.sankuai.com/@types/retry/download/@types/retry-0.12.0.tgz", "integrity": "sha1-KzXsz87n04zXKtmSMvvVi/+zyE0=", "dev": true, "license": "MIT"}, "node_modules/@types/send": {"version": "0.17.4", "resolved": "http://r.npm.sankuai.com/@types/send/download/@types/send-0.17.4.tgz", "integrity": "sha1-ZhnNJOcnB5NwLk5qS5WKkBDPxXo=", "dev": true, "license": "MIT", "dependencies": {"@types/mime": "^1", "@types/node": "*"}}, "node_modules/@types/serve-index": {"version": "1.9.4", "resolved": "http://r.npm.sankuai.com/@types/serve-index/download/@types/serve-index-1.9.4.tgz", "integrity": "sha1-5q4T1QU8sG7TY5IRC0+aSaxOyJg=", "dev": true, "license": "MIT", "dependencies": {"@types/express": "*"}}, "node_modules/@types/serve-index/node_modules/@types/express": {"version": "5.0.1", "resolved": "http://r.npm.sankuai.com/@types/express/download/@types/express-5.0.1.tgz", "integrity": "sha1-E410HG5duMwnO+xShc1unQd5/J8=", "dev": true, "license": "MIT", "dependencies": {"@types/body-parser": "*", "@types/express-serve-static-core": "^5.0.0", "@types/serve-static": "*"}}, "node_modules/@types/serve-static": {"version": "1.15.7", "resolved": "http://r.npm.sankuai.com/@types/serve-static/download/@types/serve-static-1.15.7.tgz", "integrity": "sha1-IhdLvXT7l/4wMQlzjptcLzBk9xQ=", "dev": true, "license": "MIT", "dependencies": {"@types/http-errors": "*", "@types/node": "*", "@types/send": "*"}}, "node_modules/@types/sockjs": {"version": "0.3.36", "resolved": "http://r.npm.sankuai.com/@types/sockjs/download/@types/sockjs-0.3.36.tgz", "integrity": "sha1-zjIs8HvMEZ1Mv3+IlU86O9D2dTU=", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/ws": {"version": "8.18.1", "resolved": "http://r.npm.sankuai.com/@types/ws/download/@types/ws-8.18.1.tgz", "integrity": "sha1-SEZOS/Ld/RfbE9hFRn9gcP/qSqk=", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@vue/babel-helper-vue-jsx-merge-props": {"version": "1.4.0", "resolved": "http://r.npm.sankuai.com/@vue/babel-helper-vue-jsx-merge-props/download/@vue/babel-helper-vue-jsx-merge-props-1.4.0.tgz", "integrity": "sha1-jVOh4hNH247b5U0zmQJYMXbeCfI=", "dev": true, "license": "MIT"}, "node_modules/@vue/babel-helper-vue-transform-on": {"version": "1.4.0", "resolved": "http://r.npm.sankuai.com/@vue/babel-helper-vue-transform-on/download/@vue/babel-helper-vue-transform-on-1.4.0.tgz", "integrity": "sha1-YWAgSIaSqcQqYTKA1i7RtycEXZU=", "dev": true, "license": "MIT"}, "node_modules/@vue/babel-plugin-jsx": {"version": "1.4.0", "resolved": "http://r.npm.sankuai.com/@vue/babel-plugin-jsx/download/@vue/babel-plugin-jsx-1.4.0.tgz", "integrity": "sha1-wVXHlc6YDt9Gqm/s7tk5Ralcplg=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-module-imports": "^7.25.9", "@babel/helper-plugin-utils": "^7.26.5", "@babel/plugin-syntax-jsx": "^7.25.9", "@babel/template": "^7.26.9", "@babel/traverse": "^7.26.9", "@babel/types": "^7.26.9", "@vue/babel-helper-vue-transform-on": "1.4.0", "@vue/babel-plugin-resolve-type": "1.4.0", "@vue/shared": "^3.5.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "peerDependenciesMeta": {"@babel/core": {"optional": true}}}, "node_modules/@vue/babel-plugin-resolve-type": {"version": "1.4.0", "resolved": "http://r.npm.sankuai.com/@vue/babel-plugin-resolve-type/download/@vue/babel-plugin-resolve-type-1.4.0.tgz", "integrity": "sha1-TTV6gfsMycrQ6MgbEYEVvaLFFUM=", "dev": true, "license": "MIT", "dependencies": {"@babel/code-frame": "^7.26.2", "@babel/helper-module-imports": "^7.25.9", "@babel/helper-plugin-utils": "^7.26.5", "@babel/parser": "^7.26.9", "@vue/compiler-sfc": "^3.5.13"}, "funding": {"url": "https://github.com/sponsors/sxzz"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@vue/babel-plugin-resolve-type/node_modules/@vue/compiler-sfc": {"version": "3.5.14", "resolved": "http://r.npm.sankuai.com/@vue/compiler-sfc/download/@vue/compiler-sfc-3.5.14.tgz", "integrity": "sha1-/D2zChx0QTnUG7V7tFHXg0Ffzks=", "dev": true, "license": "MIT", "dependencies": {"@babel/parser": "^7.27.2", "@vue/compiler-core": "3.5.14", "@vue/compiler-dom": "3.5.14", "@vue/compiler-ssr": "3.5.14", "@vue/shared": "3.5.14", "estree-walker": "^2.0.2", "magic-string": "^0.30.17", "postcss": "^8.5.3", "source-map-js": "^1.2.1"}}, "node_modules/@vue/babel-plugin-transform-vue-jsx": {"version": "1.4.0", "resolved": "http://r.npm.sankuai.com/@vue/babel-plugin-transform-vue-jsx/download/@vue/babel-plugin-transform-vue-jsx-1.4.0.tgz", "integrity": "sha1-TUs9RqOepit0Z91uJs5H986vsv4=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-module-imports": "^7.0.0", "@babel/plugin-syntax-jsx": "^7.2.0", "@vue/babel-helper-vue-jsx-merge-props": "^1.4.0", "html-tags": "^2.0.0", "lodash.kebabcase": "^4.1.1", "svg-tags": "^1.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@vue/babel-preset-app": {"version": "5.0.8", "resolved": "http://r.npm.sankuai.com/@vue/babel-preset-app/download/@vue/babel-preset-app-5.0.8.tgz", "integrity": "sha1-zjj3YxT1Jl1iqJdW7yZMIfHTUaE=", "dev": true, "license": "MIT", "dependencies": {"@babel/core": "^7.12.16", "@babel/helper-compilation-targets": "^7.12.16", "@babel/helper-module-imports": "^7.12.13", "@babel/plugin-proposal-class-properties": "^7.12.13", "@babel/plugin-proposal-decorators": "^7.12.13", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-syntax-jsx": "^7.12.13", "@babel/plugin-transform-runtime": "^7.12.15", "@babel/preset-env": "^7.12.16", "@babel/runtime": "^7.12.13", "@vue/babel-plugin-jsx": "^1.0.3", "@vue/babel-preset-jsx": "^1.1.2", "babel-plugin-dynamic-import-node": "^2.3.3", "core-js": "^3.8.3", "core-js-compat": "^3.8.3", "semver": "^7.3.4"}, "peerDependencies": {"@babel/core": "*", "core-js": "^3", "vue": "^2 || ^3.2.13"}, "peerDependenciesMeta": {"core-js": {"optional": true}, "vue": {"optional": true}}}, "node_modules/@vue/babel-preset-app/node_modules/semver": {"version": "7.7.2", "resolved": "http://r.npm.sankuai.com/semver/download/semver-7.7.2.tgz", "integrity": "sha1-Z9mf3NNc7CHm+Lh6f9UVoz+YK1g=", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/@vue/babel-preset-jsx": {"version": "1.4.0", "resolved": "http://r.npm.sankuai.com/@vue/babel-preset-jsx/download/@vue/babel-preset-jsx-1.4.0.tgz", "integrity": "sha1-9JFLoxQjWrCXvENy7WdHPAeAv8w=", "dev": true, "license": "MIT", "dependencies": {"@vue/babel-helper-vue-jsx-merge-props": "^1.4.0", "@vue/babel-plugin-transform-vue-jsx": "^1.4.0", "@vue/babel-sugar-composition-api-inject-h": "^1.4.0", "@vue/babel-sugar-composition-api-render-instance": "^1.4.0", "@vue/babel-sugar-functional-vue": "^1.4.0", "@vue/babel-sugar-inject-h": "^1.4.0", "@vue/babel-sugar-v-model": "^1.4.0", "@vue/babel-sugar-v-on": "^1.4.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0", "vue": "*"}, "peerDependenciesMeta": {"vue": {"optional": true}}}, "node_modules/@vue/babel-sugar-composition-api-inject-h": {"version": "1.4.0", "resolved": "http://r.npm.sankuai.com/@vue/babel-sugar-composition-api-inject-h/download/@vue/babel-sugar-composition-api-inject-h-1.4.0.tgz", "integrity": "sha1-GH4TifiHHYns50O7UK7XE76dbIU=", "dev": true, "license": "MIT", "dependencies": {"@babel/plugin-syntax-jsx": "^7.2.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@vue/babel-sugar-composition-api-render-instance": {"version": "1.4.0", "resolved": "http://r.npm.sankuai.com/@vue/babel-sugar-composition-api-render-instance/download/@vue/babel-sugar-composition-api-render-instance-1.4.0.tgz", "integrity": "sha1-LBYHrm3/2rR+eFvAH6Rbp1bpksE=", "dev": true, "license": "MIT", "dependencies": {"@babel/plugin-syntax-jsx": "^7.2.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@vue/babel-sugar-functional-vue": {"version": "1.4.0", "resolved": "http://r.npm.sankuai.com/@vue/babel-sugar-functional-vue/download/@vue/babel-sugar-functional-vue-1.4.0.tgz", "integrity": "sha1-YNoxBoVnCCKHxzN8Zu9N8E4KECk=", "dev": true, "license": "MIT", "dependencies": {"@babel/plugin-syntax-jsx": "^7.2.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@vue/babel-sugar-inject-h": {"version": "1.4.0", "resolved": "http://r.npm.sankuai.com/@vue/babel-sugar-inject-h/download/@vue/babel-sugar-inject-h-1.4.0.tgz", "integrity": "sha1-vzmqZjH7HQOZscSbTFnhyImbQ2M=", "dev": true, "license": "MIT", "dependencies": {"@babel/plugin-syntax-jsx": "^7.2.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@vue/babel-sugar-v-model": {"version": "1.4.0", "resolved": "http://r.npm.sankuai.com/@vue/babel-sugar-v-model/download/@vue/babel-sugar-v-model-1.4.0.tgz", "integrity": "sha1-pR2YZgn0MMT3Cto6k8xWCilw9yA=", "dev": true, "license": "MIT", "dependencies": {"@babel/plugin-syntax-jsx": "^7.2.0", "@vue/babel-helper-vue-jsx-merge-props": "^1.4.0", "@vue/babel-plugin-transform-vue-jsx": "^1.4.0", "camelcase": "^5.0.0", "html-tags": "^2.0.0", "svg-tags": "^1.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@vue/babel-sugar-v-on": {"version": "1.4.0", "resolved": "http://r.npm.sankuai.com/@vue/babel-sugar-v-on/download/@vue/babel-sugar-v-on-1.4.0.tgz", "integrity": "sha1-Q7cQapZy2Mvu/A64r+HTdu3GFm4=", "dev": true, "license": "MIT", "dependencies": {"@babel/plugin-syntax-jsx": "^7.2.0", "@vue/babel-plugin-transform-vue-jsx": "^1.4.0", "camelcase": "^5.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@vue/cli-overlay": {"version": "5.0.8", "resolved": "http://r.npm.sankuai.com/@vue/cli-overlay/download/@vue/cli-overlay-5.0.8.tgz", "integrity": "sha1-thR3rNxDu9QvzmMm0ihHEgHs3N0=", "dev": true, "license": "MIT"}, "node_modules/@vue/cli-plugin-babel": {"version": "5.0.8", "resolved": "http://r.npm.sankuai.com/@vue/cli-plugin-babel/download/@vue/cli-plugin-babel-5.0.8.tgz", "integrity": "sha1-VPmgeQDym6/1SAPc+pFsYCiU/rc=", "dev": true, "license": "MIT", "dependencies": {"@babel/core": "^7.12.16", "@vue/babel-preset-app": "^5.0.8", "@vue/cli-shared-utils": "^5.0.8", "babel-loader": "^8.2.2", "thread-loader": "^3.0.0", "webpack": "^5.54.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0 || ^5.0.0-0"}}, "node_modules/@vue/cli-plugin-eslint": {"version": "5.0.8", "resolved": "http://r.npm.sankuai.com/@vue/cli-plugin-eslint/download/@vue/cli-plugin-eslint-5.0.8.tgz", "integrity": "sha1-dUk5JlwsW3Rvo2x9dwWokTjhk78=", "dev": true, "license": "MIT", "dependencies": {"@vue/cli-shared-utils": "^5.0.8", "eslint-webpack-plugin": "^3.1.0", "globby": "^11.0.2", "webpack": "^5.54.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0 || ^5.0.0-0", "eslint": ">=7.5.0"}}, "node_modules/@vue/cli-plugin-router": {"version": "5.0.8", "resolved": "http://r.npm.sankuai.com/@vue/cli-plugin-router/download/@vue/cli-plugin-router-5.0.8.tgz", "integrity": "sha1-oRPsYm89QhbSBJbELTVTO86eiJ8=", "dev": true, "license": "MIT", "dependencies": {"@vue/cli-shared-utils": "^5.0.8"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0 || ^5.0.0-0"}}, "node_modules/@vue/cli-plugin-vuex": {"version": "5.0.8", "resolved": "http://r.npm.sankuai.com/@vue/cli-plugin-vuex/download/@vue/cli-plugin-vuex-5.0.8.tgz", "integrity": "sha1-DUyzAg+RAr6pKI11Bynd4XbGbM0=", "dev": true, "license": "MIT", "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0 || ^5.0.0-0"}}, "node_modules/@vue/cli-service": {"version": "5.0.8", "resolved": "http://r.npm.sankuai.com/@vue/cli-service/download/@vue/cli-service-5.0.8.tgz", "integrity": "sha1-zz9vG3vw+6nNq4a2vsT5iX+YLaw=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-compilation-targets": "^7.12.16", "@soda/friendly-errors-webpack-plugin": "^1.8.0", "@soda/get-current-script": "^1.0.2", "@types/minimist": "^1.2.0", "@vue/cli-overlay": "^5.0.8", "@vue/cli-plugin-router": "^5.0.8", "@vue/cli-plugin-vuex": "^5.0.8", "@vue/cli-shared-utils": "^5.0.8", "@vue/component-compiler-utils": "^3.3.0", "@vue/vue-loader-v15": "npm:vue-loader@^15.9.7", "@vue/web-component-wrapper": "^1.3.0", "acorn": "^8.0.5", "acorn-walk": "^8.0.2", "address": "^1.1.2", "autoprefixer": "^10.2.4", "browserslist": "^4.16.3", "case-sensitive-paths-webpack-plugin": "^2.3.0", "cli-highlight": "^2.1.10", "clipboardy": "^2.3.0", "cliui": "^7.0.4", "copy-webpack-plugin": "^9.0.1", "css-loader": "^6.5.0", "css-minimizer-webpack-plugin": "^3.0.2", "cssnano": "^5.0.0", "debug": "^4.1.1", "default-gateway": "^6.0.3", "dotenv": "^10.0.0", "dotenv-expand": "^5.1.0", "fs-extra": "^9.1.0", "globby": "^11.0.2", "hash-sum": "^2.0.0", "html-webpack-plugin": "^5.1.0", "is-file-esm": "^1.0.0", "launch-editor-middleware": "^2.2.1", "lodash.defaultsdeep": "^4.6.1", "lodash.mapvalues": "^4.6.0", "mini-css-extract-plugin": "^2.5.3", "minimist": "^1.2.5", "module-alias": "^2.2.2", "portfinder": "^1.0.26", "postcss": "^8.2.6", "postcss-loader": "^6.1.1", "progress-webpack-plugin": "^1.0.12", "ssri": "^8.0.1", "terser-webpack-plugin": "^5.1.1", "thread-loader": "^3.0.0", "vue-loader": "^17.0.0", "vue-style-loader": "^4.1.3", "webpack": "^5.54.0", "webpack-bundle-analyzer": "^4.4.0", "webpack-chain": "^6.5.1", "webpack-dev-server": "^4.7.3", "webpack-merge": "^5.7.3", "webpack-virtual-modules": "^0.4.2", "whatwg-fetch": "^3.6.2"}, "bin": {"vue-cli-service": "bin/vue-cli-service.js"}, "engines": {"node": "^12.0.0 || >= 14.0.0"}, "peerDependencies": {"vue-template-compiler": "^2.0.0", "webpack-sources": "*"}, "peerDependenciesMeta": {"cache-loader": {"optional": true}, "less-loader": {"optional": true}, "pug-plain-loader": {"optional": true}, "raw-loader": {"optional": true}, "sass-loader": {"optional": true}, "stylus-loader": {"optional": true}, "vue-template-compiler": {"optional": true}, "webpack-sources": {"optional": true}}}, "node_modules/@vue/cli-service/node_modules/hash-sum": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/hash-sum/download/hash-sum-2.0.0.tgz", "integrity": "sha1-gdAbtd6OpKIUrV1urRtSNGCwtFo=", "dev": true, "license": "MIT"}, "node_modules/@vue/cli-shared-utils": {"version": "5.0.8", "resolved": "http://r.npm.sankuai.com/@vue/cli-shared-utils/download/@vue/cli-shared-utils-5.0.8.tgz", "integrity": "sha1-dfyWUo66Kxx+M8t+mJqYTd75nIo=", "dev": true, "license": "MIT", "dependencies": {"@achrinza/node-ipc": "^9.2.5", "chalk": "^4.1.2", "execa": "^1.0.0", "joi": "^17.4.0", "launch-editor": "^2.2.1", "lru-cache": "^6.0.0", "node-fetch": "^2.6.7", "open": "^8.0.2", "ora": "^5.3.0", "read-pkg": "^5.1.1", "semver": "^7.3.4", "strip-ansi": "^6.0.0"}}, "node_modules/@vue/cli-shared-utils/node_modules/lru-cache": {"version": "6.0.0", "resolved": "http://r.npm.sankuai.com/lru-cache/download/lru-cache-6.0.0.tgz", "integrity": "sha1-bW/mVw69lqr5D8rR2vo7JWbbOpQ=", "dev": true, "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/@vue/cli-shared-utils/node_modules/semver": {"version": "7.7.2", "resolved": "http://r.npm.sankuai.com/semver/download/semver-7.7.2.tgz", "integrity": "sha1-Z9mf3NNc7CHm+Lh6f9UVoz+YK1g=", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/@vue/compiler-core": {"version": "3.5.14", "resolved": "http://r.npm.sankuai.com/@vue/compiler-core/download/@vue/compiler-core-3.5.14.tgz", "integrity": "sha1-NnZoXATEiltKVRWzsoQumDQsVVw=", "dev": true, "license": "MIT", "dependencies": {"@babel/parser": "^7.27.2", "@vue/shared": "3.5.14", "entities": "^4.5.0", "estree-walker": "^2.0.2", "source-map-js": "^1.2.1"}}, "node_modules/@vue/compiler-core/node_modules/entities": {"version": "4.5.0", "resolved": "http://r.npm.sankuai.com/entities/download/entities-4.5.0.tgz", "integrity": "sha1-XSaOpecRPsdMTQM7eepaNaSI+0g=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=0.12"}, "funding": {"url": "https://github.com/fb55/entities?sponsor=1"}}, "node_modules/@vue/compiler-dom": {"version": "3.5.14", "resolved": "http://r.npm.sankuai.com/@vue/compiler-dom/download/@vue/compiler-dom-3.5.14.tgz", "integrity": "sha1-u/J0IfgPe4hzAA7c7s2BfEq/Q4o=", "dev": true, "license": "MIT", "dependencies": {"@vue/compiler-core": "3.5.14", "@vue/shared": "3.5.14"}}, "node_modules/@vue/compiler-sfc": {"version": "2.7.16", "resolved": "http://r.npm.sankuai.com/@vue/compiler-sfc/download/@vue/compiler-sfc-2.7.16.tgz", "integrity": "sha1-/4FxGg+snGhoPYuwC2P4V9533IM=", "dependencies": {"@babel/parser": "^7.23.5", "postcss": "^8.4.14", "source-map": "^0.6.1"}, "optionalDependencies": {"prettier": "^1.18.2 || ^2.0.0"}}, "node_modules/@vue/compiler-ssr": {"version": "3.5.14", "resolved": "http://r.npm.sankuai.com/@vue/compiler-ssr/download/@vue/compiler-ssr-3.5.14.tgz", "integrity": "sha1-ATF07mu/PuKRpt8kej/rbrQ9gIs=", "dev": true, "license": "MIT", "dependencies": {"@vue/compiler-dom": "3.5.14", "@vue/shared": "3.5.14"}}, "node_modules/@vue/component-compiler-utils": {"version": "3.3.0", "resolved": "http://r.npm.sankuai.com/@vue/component-compiler-utils/download/@vue/component-compiler-utils-3.3.0.tgz", "integrity": "sha1-+fX7U0ZLDDeyyNLz+/5E32D2Hck=", "dev": true, "license": "MIT", "dependencies": {"consolidate": "^0.15.1", "hash-sum": "^1.0.2", "lru-cache": "^4.1.2", "merge-source-map": "^1.1.0", "postcss": "^7.0.36", "postcss-selector-parser": "^6.0.2", "source-map": "~0.6.1", "vue-template-es2015-compiler": "^1.9.0"}, "optionalDependencies": {"prettier": "^1.18.2 || ^2.0.0"}}, "node_modules/@vue/component-compiler-utils/node_modules/picocolors": {"version": "0.2.1", "resolved": "http://r.npm.sankuai.com/picocolors/download/picocolors-0.2.1.tgz", "integrity": "sha1-VwZw95NkaFHRuhNZlpYqutWHhZ8=", "dev": true, "license": "ISC"}, "node_modules/@vue/component-compiler-utils/node_modules/postcss": {"version": "7.0.39", "resolved": "http://r.npm.sankuai.com/postcss/download/postcss-7.0.39.tgz", "integrity": "sha1-liQ3XZZWMOLh8sAqk1yCpZy0gwk=", "dev": true, "license": "MIT", "dependencies": {"picocolors": "^0.2.1", "source-map": "^0.6.1"}, "engines": {"node": ">=6.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/postcss/"}}, "node_modules/@vue/shared": {"version": "3.5.14", "resolved": "http://r.npm.sankuai.com/@vue/shared/download/@vue/shared-3.5.14.tgz", "integrity": "sha1-j83GxpZhoRY8FzyvthKcP4rQESI=", "dev": true, "license": "MIT"}, "node_modules/@vue/vue-loader-v15": {"name": "vue-loader", "version": "15.11.1", "resolved": "http://r.npm.sankuai.com/vue-loader/download/vue-loader-15.11.1.tgz", "integrity": "sha1-3ukRaSESdu1DxXFcrviKVrH0l7A=", "dev": true, "license": "MIT", "dependencies": {"@vue/component-compiler-utils": "^3.1.0", "hash-sum": "^1.0.2", "loader-utils": "^1.1.0", "vue-hot-reload-api": "^2.3.0", "vue-style-loader": "^4.1.0"}, "peerDependencies": {"css-loader": "*", "webpack": "^3.0.0 || ^4.1.0 || ^5.0.0-0"}, "peerDependenciesMeta": {"cache-loader": {"optional": true}, "prettier": {"optional": true}, "vue-template-compiler": {"optional": true}}}, "node_modules/@vue/web-component-wrapper": {"version": "1.3.0", "resolved": "http://r.npm.sankuai.com/@vue/web-component-wrapper/download/@vue/web-component-wrapper-1.3.0.tgz", "integrity": "sha1-trQKdiVCnSvXwigd26YB7QXcfxo=", "dev": true, "license": "MIT"}, "node_modules/@webassemblyjs/ast": {"version": "1.14.1", "resolved": "http://r.npm.sankuai.com/@webassemblyjs/ast/download/@webassemblyjs/ast-1.14.1.tgz", "integrity": "sha1-qfagfysDyVyNOMRTah/ftSH/VbY=", "dev": true, "license": "MIT", "dependencies": {"@webassemblyjs/helper-numbers": "1.13.2", "@webassemblyjs/helper-wasm-bytecode": "1.13.2"}}, "node_modules/@webassemblyjs/floating-point-hex-parser": {"version": "1.13.2", "resolved": "http://r.npm.sankuai.com/@webassemblyjs/floating-point-hex-parser/download/@webassemblyjs/floating-point-hex-parser-1.13.2.tgz", "integrity": "sha1-/Moe7dscxOe27tT8eVbWgTshufs=", "dev": true, "license": "MIT"}, "node_modules/@webassemblyjs/helper-api-error": {"version": "1.13.2", "resolved": "http://r.npm.sankuai.com/@webassemblyjs/helper-api-error/download/@webassemblyjs/helper-api-error-1.13.2.tgz", "integrity": "sha1-4KFhUiSLw42u523X4h8Vxe86sec=", "dev": true, "license": "MIT"}, "node_modules/@webassemblyjs/helper-buffer": {"version": "1.14.1", "resolved": "http://r.npm.sankuai.com/@webassemblyjs/helper-buffer/download/@webassemblyjs/helper-buffer-1.14.1.tgz", "integrity": "sha1-giqbxgMWZTH31d+E5ntb+ZtyuWs=", "dev": true, "license": "MIT"}, "node_modules/@webassemblyjs/helper-numbers": {"version": "1.13.2", "resolved": "http://r.npm.sankuai.com/@webassemblyjs/helper-numbers/download/@webassemblyjs/helper-numbers-1.13.2.tgz", "integrity": "sha1-29kyVI5xGfS4p4d/1ajSDmNJCy0=", "dev": true, "license": "MIT", "dependencies": {"@webassemblyjs/floating-point-hex-parser": "1.13.2", "@webassemblyjs/helper-api-error": "1.13.2", "@xtuc/long": "4.2.2"}}, "node_modules/@webassemblyjs/helper-wasm-bytecode": {"version": "1.13.2", "resolved": "http://r.npm.sankuai.com/@webassemblyjs/helper-wasm-bytecode/download/@webassemblyjs/helper-wasm-bytecode-1.13.2.tgz", "integrity": "sha1-5VYQh1j0SKroTIUOWTzhig6zHgs=", "dev": true, "license": "MIT"}, "node_modules/@webassemblyjs/helper-wasm-section": {"version": "1.14.1", "resolved": "http://r.npm.sankuai.com/@webassemblyjs/helper-wasm-section/download/@webassemblyjs/helper-wasm-section-1.14.1.tgz", "integrity": "sha1-lindqcRDDqtUtZEFPW3G87oFA0g=", "dev": true, "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.14.1", "@webassemblyjs/helper-buffer": "1.14.1", "@webassemblyjs/helper-wasm-bytecode": "1.13.2", "@webassemblyjs/wasm-gen": "1.14.1"}}, "node_modules/@webassemblyjs/ieee754": {"version": "1.13.2", "resolved": "http://r.npm.sankuai.com/@webassemblyjs/ieee754/download/@webassemblyjs/ieee754-1.13.2.tgz", "integrity": "sha1-HF6qzh1gatosf9cEXqk1bFnuDbo=", "dev": true, "license": "MIT", "dependencies": {"@xtuc/ieee754": "^1.2.0"}}, "node_modules/@webassemblyjs/leb128": {"version": "1.13.2", "resolved": "http://r.npm.sankuai.com/@webassemblyjs/leb128/download/@webassemblyjs/leb128-1.13.2.tgz", "integrity": "sha1-V8XD3rAQXQLOJfo/109OvJ/Qu7A=", "dev": true, "license": "Apache-2.0", "dependencies": {"@xtuc/long": "4.2.2"}}, "node_modules/@webassemblyjs/utf8": {"version": "1.13.2", "resolved": "http://r.npm.sankuai.com/@webassemblyjs/utf8/download/@webassemblyjs/utf8-1.13.2.tgz", "integrity": "sha1-kXog6T9xrVYClmwtaFrgxsIfYPE=", "dev": true, "license": "MIT"}, "node_modules/@webassemblyjs/wasm-edit": {"version": "1.14.1", "resolved": "http://r.npm.sankuai.com/@webassemblyjs/wasm-edit/download/@webassemblyjs/wasm-edit-1.14.1.tgz", "integrity": "sha1-rGaJ9QIhm1kZjd7ELc1JaxAE1Zc=", "dev": true, "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.14.1", "@webassemblyjs/helper-buffer": "1.14.1", "@webassemblyjs/helper-wasm-bytecode": "1.13.2", "@webassemblyjs/helper-wasm-section": "1.14.1", "@webassemblyjs/wasm-gen": "1.14.1", "@webassemblyjs/wasm-opt": "1.14.1", "@webassemblyjs/wasm-parser": "1.14.1", "@webassemblyjs/wast-printer": "1.14.1"}}, "node_modules/@webassemblyjs/wasm-gen": {"version": "1.14.1", "resolved": "http://r.npm.sankuai.com/@webassemblyjs/wasm-gen/download/@webassemblyjs/wasm-gen-1.14.1.tgz", "integrity": "sha1-mR5/DAkMsLtiu6yIIHbj0hnalXA=", "dev": true, "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.14.1", "@webassemblyjs/helper-wasm-bytecode": "1.13.2", "@webassemblyjs/ieee754": "1.13.2", "@webassemblyjs/leb128": "1.13.2", "@webassemblyjs/utf8": "1.13.2"}}, "node_modules/@webassemblyjs/wasm-opt": {"version": "1.14.1", "resolved": "http://r.npm.sankuai.com/@webassemblyjs/wasm-opt/download/@webassemblyjs/wasm-opt-1.14.1.tgz", "integrity": "sha1-5vce18yuRngcIGAX08FMUO+oEGs=", "dev": true, "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.14.1", "@webassemblyjs/helper-buffer": "1.14.1", "@webassemblyjs/wasm-gen": "1.14.1", "@webassemblyjs/wasm-parser": "1.14.1"}}, "node_modules/@webassemblyjs/wasm-parser": {"version": "1.14.1", "resolved": "http://r.npm.sankuai.com/@webassemblyjs/wasm-parser/download/@webassemblyjs/wasm-parser-1.14.1.tgz", "integrity": "sha1-s+E/GJNgXKeLUsaOVM9qhl+Qufs=", "dev": true, "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.14.1", "@webassemblyjs/helper-api-error": "1.13.2", "@webassemblyjs/helper-wasm-bytecode": "1.13.2", "@webassemblyjs/ieee754": "1.13.2", "@webassemblyjs/leb128": "1.13.2", "@webassemblyjs/utf8": "1.13.2"}}, "node_modules/@webassemblyjs/wast-printer": {"version": "1.14.1", "resolved": "http://r.npm.sankuai.com/@webassemblyjs/wast-printer/download/@webassemblyjs/wast-printer-1.14.1.tgz", "integrity": "sha1-O7PpY4qK5f2vlhDnoGtNn5qm/gc=", "dev": true, "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.14.1", "@xtuc/long": "4.2.2"}}, "node_modules/@xtuc/ieee754": {"version": "1.2.0", "resolved": "http://r.npm.sankuai.com/@xtuc/ieee754/download/@xtuc/ieee754-1.2.0.tgz", "integrity": "sha1-7vAUoxRa5Hehy8AM0eVSM23Ot5A=", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@xtuc/long": {"version": "4.2.2", "resolved": "http://r.npm.sankuai.com/@xtuc/long/download/@xtuc/long-4.2.2.tgz", "integrity": "sha1-0pHGpOl5ibXGHZrPOWrk/hM6cY0=", "dev": true, "license": "Apache-2.0"}, "node_modules/abort-controller": {"version": "3.0.0", "resolved": "http://r.npm.sankuai.com/abort-controller/download/abort-controller-3.0.0.tgz", "integrity": "sha1-6vVNU7YrrkE46AnKIlyEOabvs5I=", "license": "MIT", "dependencies": {"event-target-shim": "^5.0.0"}, "engines": {"node": ">=6.5"}}, "node_modules/accepts": {"version": "1.3.8", "resolved": "http://r.npm.sankuai.com/accepts/download/accepts-1.3.8.tgz", "integrity": "sha1-C/C+EltnAUrcsLCSHmLbe//hay4=", "dev": true, "license": "MIT", "dependencies": {"mime-types": "~2.1.34", "negotiator": "0.6.3"}, "engines": {"node": ">= 0.6"}}, "node_modules/accepts/node_modules/negotiator": {"version": "0.6.3", "resolved": "http://r.npm.sankuai.com/negotiator/download/negotiator-0.6.3.tgz", "integrity": "sha1-WOMjpy/twNb5zU0x/kn1FHlZDM0=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/acorn": {"version": "8.14.1", "resolved": "http://r.npm.sankuai.com/acorn/download/acorn-8.14.1.tgz", "integrity": "sha1-ch1dwQ99W1YJqJF3PUdzF5aTXfs=", "dev": true, "license": "MIT", "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/acorn-jsx": {"version": "5.3.2", "resolved": "http://r.npm.sankuai.com/acorn-jsx/download/acorn-jsx-5.3.2.tgz", "integrity": "sha1-ftW7VZCLOy8bxVxq8WU7rafweTc=", "dev": true, "license": "MIT", "peerDependencies": {"acorn": "^6.0.0 || ^7.0.0 || ^8.0.0"}}, "node_modules/acorn-walk": {"version": "8.3.4", "resolved": "http://r.npm.sankuai.com/acorn-walk/download/acorn-walk-8.3.4.tgz", "integrity": "sha1-eU3RacOXft9LpOpHWDWHxYZiNrc=", "dev": true, "license": "MIT", "dependencies": {"acorn": "^8.11.0"}, "engines": {"node": ">=0.4.0"}}, "node_modules/address": {"version": "1.2.2", "resolved": "http://r.npm.sankuai.com/address/download/address-1.2.2.tgz", "integrity": "sha1-K1JI2sVIWmOQUyxqUX/aLj+qyJ4=", "dev": true, "license": "MIT", "engines": {"node": ">= 10.0.0"}}, "node_modules/agentkeepalive": {"version": "4.6.0", "resolved": "http://r.npm.sankuai.com/agentkeepalive/download/agentkeepalive-4.6.0.tgz", "integrity": "sha1-Nfc+lLP0C/ZfEFIZxiOtGcE26mo=", "license": "MIT", "dependencies": {"humanize-ms": "^1.2.1"}, "engines": {"node": ">= 8.0.0"}}, "node_modules/ajv": {"version": "6.12.6", "resolved": "http://r.npm.sankuai.com/ajv/download/ajv-6.12.6.tgz", "integrity": "sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=", "dev": true, "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/ajv-keywords": {"version": "3.5.2", "resolved": "http://r.npm.sankuai.com/ajv-keywords/download/ajv-keywords-3.5.2.tgz", "integrity": "sha1-MfKdpatuANHC0yms97WSlhTVAU0=", "dev": true, "license": "MIT", "peerDependencies": {"ajv": "^6.9.1"}}, "node_modules/ansi-colors": {"version": "4.1.3", "resolved": "http://r.npm.sankuai.com/ansi-colors/download/ansi-colors-4.1.3.tgz", "integrity": "sha1-N2ETQOsiQ+cMxgTK011jJw1IeBs=", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/ansi-escapes": {"version": "3.2.0", "resolved": "http://r.npm.sankuai.com/ansi-escapes/download/ansi-escapes-3.2.0.tgz", "integrity": "sha1-h4C5j/nb9WOBUtHx/lwde0RCl2s=", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/ansi-html-community": {"version": "0.0.8", "resolved": "http://r.npm.sankuai.com/ansi-html-community/download/ansi-html-community-0.0.8.tgz", "integrity": "sha1-afvE1sy+OD+XNpNK40w/gpDxv0E=", "dev": true, "engines": ["node >= 0.8.0"], "license": "Apache-2.0", "bin": {"ansi-html": "bin/ansi-html"}}, "node_modules/ansi-regex": {"version": "5.0.1", "resolved": "http://r.npm.sankuai.com/ansi-regex/download/ansi-regex-5.0.1.tgz", "integrity": "sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/ansi-styles": {"version": "4.3.0", "resolved": "http://r.npm.sankuai.com/ansi-styles/download/ansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/any-promise": {"version": "1.3.0", "resolved": "http://r.npm.sankuai.com/any-promise/download/any-promise-1.3.0.tgz", "integrity": "sha1-q8av7tzqUugJzcA3au0845Y10X8=", "dev": true, "license": "MIT"}, "node_modules/anymatch": {"version": "3.1.3", "resolved": "http://r.npm.sankuai.com/anymatch/download/anymatch-3.1.3.tgz", "integrity": "sha1-eQxYsZuhcgqEIFtXxhjVrYUklz4=", "dev": true, "license": "ISC", "dependencies": {"normalize-path": "^3.0.0", "picomatch": "^2.0.4"}, "engines": {"node": ">= 8"}}, "node_modules/arch": {"version": "2.2.0", "resolved": "http://r.npm.sankuai.com/arch/download/arch-2.2.0.tgz", "integrity": "sha1-G8R4GPMFdk8jqzMGsL/AhsWinRE=", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/argparse": {"version": "1.0.10", "resolved": "http://r.npm.sankuai.com/argparse/download/argparse-1.0.10.tgz", "integrity": "sha1-vNZ5HqWuCXJeF+WtmIE0zUCz2RE=", "dev": true, "license": "MIT", "dependencies": {"sprintf-js": "~1.0.2"}}, "node_modules/array-flatten": {"version": "1.1.1", "resolved": "http://r.npm.sankuai.com/array-flatten/download/array-flatten-1.1.1.tgz", "integrity": "sha1-ml9pkFGx5wczKPKgCJaLZOopVdI=", "dev": true, "license": "MIT"}, "node_modules/array-union": {"version": "2.1.0", "resolved": "http://r.npm.sankuai.com/array-union/download/array-union-2.1.0.tgz", "integrity": "sha1-t5hCCtvrHego2ErNii4j0+/oXo0=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/asn1.js": {"version": "4.10.1", "resolved": "http://r.npm.sankuai.com/asn1.js/download/asn1.js-4.10.1.tgz", "integrity": "sha1-ucK/WAXx5kqt7tbfOiv6+1pz9aA=", "dev": true, "license": "MIT", "dependencies": {"bn.js": "^4.0.0", "inherits": "^2.0.1", "minimalistic-assert": "^1.0.0"}}, "node_modules/astral-regex": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/astral-regex/download/astral-regex-2.0.0.tgz", "integrity": "sha1-SDFDxWeu7UeFdZwIZXhtx319LjE=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/async": {"version": "3.2.6", "resolved": "http://r.npm.sankuai.com/async/download/async-3.2.6.tgz", "integrity": "sha1-Gwco4Ukp1RuFtEm38G4nwRReOM4=", "dev": true, "license": "MIT"}, "node_modules/async-validator": {"version": "1.8.5", "resolved": "http://r.npm.sankuai.com/async-validator/download/async-validator-1.8.5.tgz", "integrity": "sha1-3D4I7B/Q3dtn5ghC8CwM0c7G1/A=", "dependencies": {"babel-runtime": "6.x"}}, "node_modules/asynckit": {"version": "0.4.0", "resolved": "http://r.npm.sankuai.com/asynckit/download/asynckit-0.4.0.tgz", "integrity": "sha1-x57Zf380y48robyXkLzDZkdLS3k=", "license": "MIT"}, "node_modules/at-least-node": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/at-least-node/download/at-least-node-1.0.0.tgz", "integrity": "sha1-YCzUtG6EStTv/JKoARo8RuAjjcI=", "dev": true, "license": "ISC", "engines": {"node": ">= 4.0.0"}}, "node_modules/autoprefixer": {"version": "10.4.21", "resolved": "http://r.npm.sankuai.com/autoprefixer/download/autoprefixer-10.4.21.tgz", "integrity": "sha1-dxiUaOeorR2aN/vAjvyfSAzwqV0=", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/autoprefixer"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"browserslist": "^4.24.4", "caniuse-lite": "^1.0.30001702", "fraction.js": "^4.3.7", "normalize-range": "^0.1.2", "picocolors": "^1.1.1", "postcss-value-parser": "^4.2.0"}, "bin": {"autoprefixer": "bin/autoprefixer"}, "engines": {"node": "^10 || ^12 || >=14"}, "peerDependencies": {"postcss": "^8.1.0"}}, "node_modules/available-typed-arrays": {"version": "1.0.7", "resolved": "http://r.npm.sankuai.com/available-typed-arrays/download/available-typed-arrays-1.0.7.tgz", "integrity": "sha1-pcw3XWoDwu/IelU/PgsVIt7xSEY=", "dev": true, "license": "MIT", "dependencies": {"possible-typed-array-names": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/axios": {"version": "1.9.0", "resolved": "http://r.npm.sankuai.com/axios/download/axios-1.9.0.tgz", "integrity": "sha1-JVNOO3K1RUAHfTMEb3fjuNcIGQE=", "license": "MIT", "dependencies": {"follow-redirects": "^1.15.6", "form-data": "^4.0.0", "proxy-from-env": "^1.1.0"}}, "node_modules/babel-helper-vue-jsx-merge-props": {"version": "2.0.3", "resolved": "http://r.npm.sankuai.com/babel-helper-vue-jsx-merge-props/download/babel-helper-vue-jsx-merge-props-2.0.3.tgz", "integrity": "sha1-Iq69OzOQIyjlEyk6jkmSs4T58bY=", "license": "MIT"}, "node_modules/babel-loader": {"version": "8.4.1", "resolved": "http://r.npm.sankuai.com/babel-loader/download/babel-loader-8.4.1.tgz", "integrity": "sha1-bMt1xm5iw7FE4cXy6uxbj2wIxnU=", "dev": true, "license": "MIT", "dependencies": {"find-cache-dir": "^3.3.1", "loader-utils": "^2.0.4", "make-dir": "^3.1.0", "schema-utils": "^2.6.5"}, "engines": {"node": ">= 8.9"}, "peerDependencies": {"@babel/core": "^7.0.0", "webpack": ">=2"}}, "node_modules/babel-loader/node_modules/loader-utils": {"version": "2.0.4", "resolved": "http://r.npm.sankuai.com/loader-utils/download/loader-utils-2.0.4.tgz", "integrity": "sha1-i1yzi1w0qaAY7h/A5qBm0d/MUow=", "dev": true, "license": "MIT", "dependencies": {"big.js": "^5.2.2", "emojis-list": "^3.0.0", "json5": "^2.1.2"}, "engines": {"node": ">=8.9.0"}}, "node_modules/babel-loader/node_modules/schema-utils": {"version": "2.7.1", "resolved": "http://r.npm.sankuai.com/schema-utils/download/schema-utils-2.7.1.tgz", "integrity": "sha1-HKTzLRskxZDCA7jnpQvw6kzTlNc=", "dev": true, "license": "MIT", "dependencies": {"@types/json-schema": "^7.0.5", "ajv": "^6.12.4", "ajv-keywords": "^3.5.2"}, "engines": {"node": ">= 8.9.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}}, "node_modules/babel-plugin-dynamic-import-node": {"version": "2.3.3", "resolved": "http://r.npm.sankuai.com/babel-plugin-dynamic-import-node/download/babel-plugin-dynamic-import-node-2.3.3.tgz", "integrity": "sha1-hP2hnJduxcbe/vV/lCez3vZuF6M=", "dev": true, "license": "MIT", "dependencies": {"object.assign": "^4.1.0"}}, "node_modules/babel-plugin-polyfill-corejs2": {"version": "0.4.13", "resolved": "http://r.npm.sankuai.com/babel-plugin-polyfill-corejs2/download/babel-plugin-polyfill-corejs2-0.4.13.tgz", "integrity": "sha1-fURfDgYH68j7awHX6PsCBpuR3Ys=", "dev": true, "license": "MIT", "dependencies": {"@babel/compat-data": "^7.22.6", "@babel/helper-define-polyfill-provider": "^0.6.4", "semver": "^6.3.1"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}}, "node_modules/babel-plugin-polyfill-corejs3": {"version": "0.11.1", "resolved": "http://r.npm.sankuai.com/babel-plugin-polyfill-corejs3/download/babel-plugin-polyfill-corejs3-0.11.1.tgz", "integrity": "sha1-Tk4YLxuzfHumLir4HY3QnfMTRPY=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-define-polyfill-provider": "^0.6.3", "core-js-compat": "^3.40.0"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}}, "node_modules/babel-plugin-polyfill-regenerator": {"version": "0.6.4", "resolved": "http://r.npm.sankuai.com/babel-plugin-polyfill-regenerator/download/babel-plugin-polyfill-regenerator-0.6.4.tgz", "integrity": "sha1-QoxhXTwXcpKiK0+T7ZnjWNeQaps=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-define-polyfill-provider": "^0.6.4"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}}, "node_modules/babel-runtime": {"version": "6.26.0", "resolved": "http://r.npm.sankuai.com/babel-runtime/download/babel-runtime-6.26.0.tgz", "integrity": "sha1-llxwWGaOgrVde/4E/yM3vItWR/4=", "license": "MIT", "dependencies": {"core-js": "^2.4.0", "regenerator-runtime": "^0.11.0"}}, "node_modules/babel-runtime/node_modules/core-js": {"version": "2.6.12", "resolved": "http://r.npm.sankuai.com/core-js/download/core-js-2.6.12.tgz", "integrity": "sha1-2TM9+nsGXjR8xWgiGdb2kIWcwuw=", "hasInstallScript": true, "license": "MIT"}, "node_modules/babel-runtime/node_modules/regenerator-runtime": {"version": "0.11.1", "resolved": "http://r.npm.sankuai.com/regenerator-runtime/download/regenerator-runtime-0.11.1.tgz", "integrity": "sha1-vgWtf5v30i4Fb5cmzuUBf78Z4uk=", "license": "MIT"}, "node_modules/balanced-match": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/balanced-match/download/balanced-match-1.0.2.tgz", "integrity": "sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4=", "dev": true, "license": "MIT"}, "node_modules/base64-js": {"version": "1.5.1", "resolved": "http://r.npm.sankuai.com/base64-js/download/base64-js-1.5.1.tgz", "integrity": "sha1-GxtEAWClv3rUC2UPCVljSBkDkwo=", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/batch": {"version": "0.6.1", "resolved": "http://r.npm.sankuai.com/batch/download/batch-0.6.1.tgz", "integrity": "sha1-3DQxT05nkxgJP8dgJyUl+UvyXBY=", "dev": true, "license": "MIT"}, "node_modules/big.js": {"version": "5.2.2", "resolved": "http://r.npm.sankuai.com/big.js/download/big.js-5.2.2.tgz", "integrity": "sha1-ZfCvOC9Xi83HQr2cKB6cstd2gyg=", "dev": true, "license": "MIT", "engines": {"node": "*"}}, "node_modules/binary-extensions": {"version": "2.3.0", "resolved": "http://r.npm.sankuai.com/binary-extensions/download/binary-extensions-2.3.0.tgz", "integrity": "sha1-9uFKl4WNMnJSIAJC1Mz+UixEVSI=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/bl": {"version": "4.1.0", "resolved": "http://r.npm.sankuai.com/bl/download/bl-4.1.0.tgz", "integrity": "sha1-RRU1JkGCvsL7vIOmKrmM8R2fezo=", "dev": true, "license": "MIT", "dependencies": {"buffer": "^5.5.0", "inherits": "^2.0.4", "readable-stream": "^3.4.0"}}, "node_modules/bl/node_modules/buffer": {"version": "5.7.1", "resolved": "http://r.npm.sankuai.com/buffer/download/buffer-5.7.1.tgz", "integrity": "sha1-umLnwTEzBTWCGXFghRqPZI6Z7tA=", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"base64-js": "^1.3.1", "ieee754": "^1.1.13"}}, "node_modules/bluebird": {"version": "3.7.2", "resolved": "http://r.npm.sankuai.com/bluebird/download/bluebird-3.7.2.tgz", "integrity": "sha1-nyKcFb4nJFT/qXOs4NvueaGww28=", "dev": true, "license": "MIT"}, "node_modules/bmp-js": {"version": "0.1.0", "resolved": "http://r.npm.sankuai.com/bmp-js/download/bmp-js-0.1.0.tgz", "integrity": "sha1-4Fpj95amwf8l9Hcex62twUjAcjM=", "license": "MIT"}, "node_modules/bn.js": {"version": "4.12.2", "resolved": "http://r.npm.sankuai.com/bn.js/download/bn.js-4.12.2.tgz", "integrity": "sha1-PY/tZ5bCThd3N/fMUXLuBO857Jk=", "dev": true, "license": "MIT"}, "node_modules/body-parser": {"version": "1.20.3", "resolved": "http://r.npm.sankuai.com/body-parser/download/body-parser-1.20.3.tgz", "integrity": "sha1-GVNDEiHG+1zWPEs21T+rCSjlSMY=", "dev": true, "license": "MIT", "dependencies": {"bytes": "3.1.2", "content-type": "~1.0.5", "debug": "2.6.9", "depd": "2.0.0", "destroy": "1.2.0", "http-errors": "2.0.0", "iconv-lite": "0.4.24", "on-finished": "2.4.1", "qs": "6.13.0", "raw-body": "2.5.2", "type-is": "~1.6.18", "unpipe": "1.0.0"}, "engines": {"node": ">= 0.8", "npm": "1.2.8000 || >= 1.4.16"}}, "node_modules/body-parser/node_modules/debug": {"version": "2.6.9", "resolved": "http://r.npm.sankuai.com/debug/download/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "dev": true, "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/body-parser/node_modules/ms": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/ms/download/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true, "license": "MIT"}, "node_modules/bonjour-service": {"version": "1.3.0", "resolved": "http://r.npm.sankuai.com/bonjour-service/download/bonjour-service-1.3.0.tgz", "integrity": "sha1-gNhnQwtaDaZOgqgEf8HjVb23FyI=", "dev": true, "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.3", "multicast-dns": "^7.2.5"}}, "node_modules/boolbase": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/boolbase/download/boolbase-1.0.0.tgz", "integrity": "sha1-aN/1++YMUes3cl6p4+0xDcwed24=", "dev": true, "license": "ISC"}, "node_modules/brace-expansion": {"version": "1.1.11", "resolved": "http://r.npm.sankuai.com/brace-expansion/download/brace-expansion-1.1.11.tgz", "integrity": "sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/braces": {"version": "3.0.3", "resolved": "http://r.npm.sankuai.com/braces/download/braces-3.0.3.tgz", "integrity": "sha1-SQMy9AkZRSJy1VqEgK3AxEE1h4k=", "dev": true, "license": "MIT", "dependencies": {"fill-range": "^7.1.1"}, "engines": {"node": ">=8"}}, "node_modules/brorand": {"version": "1.1.0", "resolved": "http://r.npm.sankuai.com/brorand/download/brorand-1.1.0.tgz", "integrity": "sha1-EsJe/kCkXjwyPrhnWgoM5XsiNx8=", "dev": true, "license": "MIT"}, "node_modules/browserify-aes": {"version": "1.2.0", "resolved": "http://r.npm.sankuai.com/browserify-aes/download/browserify-aes-1.2.0.tgz", "integrity": "sha1-Mmc0ZC9APavDADIJhTu3CtQo70g=", "dev": true, "license": "MIT", "dependencies": {"buffer-xor": "^1.0.3", "cipher-base": "^1.0.0", "create-hash": "^1.1.0", "evp_bytestokey": "^1.0.3", "inherits": "^2.0.1", "safe-buffer": "^5.0.1"}}, "node_modules/browserify-cipher": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/browserify-cipher/download/browserify-cipher-1.0.1.tgz", "integrity": "sha1-jWR0wbhwv9q807z8wZNKEOlPFfA=", "dev": true, "license": "MIT", "dependencies": {"browserify-aes": "^1.0.4", "browserify-des": "^1.0.0", "evp_bytestokey": "^1.0.0"}}, "node_modules/browserify-des": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/browserify-des/download/browserify-des-1.0.2.tgz", "integrity": "sha1-OvTx9Zg5QDVy8cZiBDdfen9wPpw=", "dev": true, "license": "MIT", "dependencies": {"cipher-base": "^1.0.1", "des.js": "^1.0.0", "inherits": "^2.0.1", "safe-buffer": "^5.1.2"}}, "node_modules/browserify-rsa": {"version": "4.1.1", "resolved": "http://r.npm.sankuai.com/browserify-rsa/download/browserify-rsa-4.1.1.tgz", "integrity": "sha1-BuUwkH/ilJ3CH8PC4jAuELFDcjg=", "dev": true, "license": "MIT", "dependencies": {"bn.js": "^5.2.1", "randombytes": "^2.1.0", "safe-buffer": "^5.2.1"}, "engines": {"node": ">= 0.10"}}, "node_modules/browserify-rsa/node_modules/bn.js": {"version": "5.2.2", "resolved": "http://r.npm.sankuai.com/bn.js/download/bn.js-5.2.2.tgz", "integrity": "sha1-gsCfnruxcQfNcst/05vR+dCqpWY=", "dev": true, "license": "MIT"}, "node_modules/browserify-sign": {"version": "4.2.3", "resolved": "http://r.npm.sankuai.com/browserify-sign/download/browserify-sign-4.2.3.tgz", "integrity": "sha1-ev5MAex+5ZqJpVikt1vYWuYtQgg=", "dev": true, "license": "ISC", "dependencies": {"bn.js": "^5.2.1", "browserify-rsa": "^4.1.0", "create-hash": "^1.2.0", "create-hmac": "^1.1.7", "elliptic": "^6.5.5", "hash-base": "~3.0", "inherits": "^2.0.4", "parse-asn1": "^5.1.7", "readable-stream": "^2.3.8", "safe-buffer": "^5.2.1"}, "engines": {"node": ">= 0.12"}}, "node_modules/browserify-sign/node_modules/bn.js": {"version": "5.2.2", "resolved": "http://r.npm.sankuai.com/bn.js/download/bn.js-5.2.2.tgz", "integrity": "sha1-gsCfnruxcQfNcst/05vR+dCqpWY=", "dev": true, "license": "MIT"}, "node_modules/browserify-sign/node_modules/readable-stream": {"version": "2.3.8", "resolved": "http://r.npm.sankuai.com/readable-stream/download/readable-stream-2.3.8.tgz", "integrity": "sha1-kRJegEK7obmIf0k0X2J3Anzovps=", "dev": true, "license": "MIT", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "node_modules/browserify-sign/node_modules/readable-stream/node_modules/safe-buffer": {"version": "5.1.2", "resolved": "http://r.npm.sankuai.com/safe-buffer/download/safe-buffer-5.1.2.tgz", "integrity": "sha1-mR7GnSluAxN0fVm9/St0XDX4go0=", "dev": true, "license": "MIT"}, "node_modules/browserify-sign/node_modules/string_decoder": {"version": "1.1.1", "resolved": "http://r.npm.sankuai.com/string_decoder/download/string_decoder-1.1.1.tgz", "integrity": "sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=", "dev": true, "license": "MIT", "dependencies": {"safe-buffer": "~5.1.0"}}, "node_modules/browserify-sign/node_modules/string_decoder/node_modules/safe-buffer": {"version": "5.1.2", "resolved": "http://r.npm.sankuai.com/safe-buffer/download/safe-buffer-5.1.2.tgz", "integrity": "sha1-mR7GnSluAxN0fVm9/St0XDX4go0=", "dev": true, "license": "MIT"}, "node_modules/browserslist": {"version": "4.24.5", "resolved": "http://r.npm.sankuai.com/browserslist/download/browserslist-4.24.5.tgz", "integrity": "sha1-qg9bhWD+gf3oTG3LOPdZuvug4Rs=", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"caniuse-lite": "^1.0.30001716", "electron-to-chromium": "^1.5.149", "node-releases": "^2.0.19", "update-browserslist-db": "^1.1.3"}, "bin": {"browserslist": "cli.js"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}}, "node_modules/buffer": {"version": "6.0.3", "resolved": "http://r.npm.sankuai.com/buffer/download/buffer-6.0.3.tgz", "integrity": "sha1-Ks5XhFnMj74qcKqo9S7mO2p0xsY=", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"base64-js": "^1.3.1", "ieee754": "^1.2.1"}}, "node_modules/buffer-from": {"version": "1.1.2", "resolved": "http://r.npm.sankuai.com/buffer-from/download/buffer-from-1.1.2.tgz", "integrity": "sha1-KxRqb9cugLT1XSVfNe1Zo6mkG9U=", "dev": true, "license": "MIT"}, "node_modules/buffer-xor": {"version": "1.0.3", "resolved": "http://r.npm.sankuai.com/buffer-xor/download/buffer-xor-1.0.3.tgz", "integrity": "sha1-JuYe0UIvtw3ULm42cp7VHYVf6Nk=", "dev": true, "license": "MIT"}, "node_modules/bytes": {"version": "3.1.2", "resolved": "http://r.npm.sankuai.com/bytes/download/bytes-3.1.2.tgz", "integrity": "sha1-iwvuuYYFrfGxKPpDhkA8AJ4CIaU=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/call-bind": {"version": "1.0.8", "resolved": "http://r.npm.sankuai.com/call-bind/download/call-bind-1.0.8.tgz", "integrity": "sha1-BzapZg9TfjOIgm9EDV7EX3ROqkw=", "dev": true, "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.0", "es-define-property": "^1.0.0", "get-intrinsic": "^1.2.4", "set-function-length": "^1.2.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/call-bind-apply-helpers": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/call-bind-apply-helpers/download/call-bind-apply-helpers-1.0.2.tgz", "integrity": "sha1-S1QowiK+mF15w9gmV0edvgtZstY=", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/call-bound": {"version": "1.0.4", "resolved": "http://r.npm.sankuai.com/call-bound/download/call-bound-1.0.4.tgz", "integrity": "sha1-I43pNdKippKSjFOMfM+pEGf9Bio=", "dev": true, "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.2", "get-intrinsic": "^1.3.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/callsites": {"version": "3.1.0", "resolved": "http://r.npm.sankuai.com/callsites/download/callsites-3.1.0.tgz", "integrity": "sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/camel-case": {"version": "4.1.2", "resolved": "http://r.npm.sankuai.com/camel-case/download/camel-case-4.1.2.tgz", "integrity": "sha1-lygHKpVPgFIoIlpt7qazhGHhvVo=", "dev": true, "license": "MIT", "dependencies": {"pascal-case": "^3.1.2", "tslib": "^2.0.3"}}, "node_modules/camelcase": {"version": "5.3.1", "resolved": "http://r.npm.sankuai.com/camelcase/download/camelcase-5.3.1.tgz", "integrity": "sha1-48mzFWnhBoEd8kL3FXJaH0xJQyA=", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/caniuse-api": {"version": "3.0.0", "resolved": "http://r.npm.sankuai.com/caniuse-api/download/caniuse-api-3.0.0.tgz", "integrity": "sha1-Xk2Q4idJYdRikZl99Znj7QCO5MA=", "dev": true, "license": "MIT", "dependencies": {"browserslist": "^4.0.0", "caniuse-lite": "^1.0.0", "lodash.memoize": "^4.1.2", "lodash.uniq": "^4.5.0"}}, "node_modules/caniuse-lite": {"version": "1.0.30001718", "resolved": "http://r.npm.sankuai.com/caniuse-lite/download/caniuse-lite-1.0.30001718.tgz", "integrity": "sha1-2uE6nIDVF8MMYZdRWpYTHBlNj4I=", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/caniuse-lite"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "CC-BY-4.0"}, "node_modules/case-sensitive-paths-webpack-plugin": {"version": "2.4.0", "resolved": "http://r.npm.sankuai.com/case-sensitive-paths-webpack-plugin/download/case-sensitive-paths-webpack-plugin-2.4.0.tgz", "integrity": "sha1-22QGbGQi7tLgjMFLmGykN5bbxtQ=", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/chalk": {"version": "4.1.2", "resolved": "http://r.npm.sankuai.com/chalk/download/chalk-4.1.2.tgz", "integrity": "sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/chokidar": {"version": "3.6.0", "resolved": "http://r.npm.sankuai.com/chokidar/download/chokidar-3.6.0.tgz", "integrity": "sha1-GXxsxmnvKo3F57TZfuTgksPrDVs=", "dev": true, "license": "MIT", "dependencies": {"anymatch": "~3.1.2", "braces": "~3.0.2", "glob-parent": "~5.1.2", "is-binary-path": "~2.1.0", "is-glob": "~4.0.1", "normalize-path": "~3.0.0", "readdirp": "~3.6.0"}, "engines": {"node": ">= 8.10.0"}, "funding": {"url": "https://paulmillr.com/funding/"}, "optionalDependencies": {"fsevents": "~2.3.2"}}, "node_modules/chrome-trace-event": {"version": "1.0.4", "resolved": "http://r.npm.sankuai.com/chrome-trace-event/download/chrome-trace-event-1.0.4.tgz", "integrity": "sha1-Bb/9f/koRlCTMUcIyTvfqb0fD1s=", "dev": true, "license": "MIT", "engines": {"node": ">=6.0"}}, "node_modules/ci-info": {"version": "1.6.0", "resolved": "http://r.npm.sankuai.com/ci-info/download/ci-info-1.6.0.tgz", "integrity": "sha1-LKINu5zrMtRSSmgzAzE/AwSx5Jc=", "dev": true, "license": "MIT"}, "node_modules/cipher-base": {"version": "1.0.6", "resolved": "http://r.npm.sankuai.com/cipher-base/download/cipher-base-1.0.6.tgz", "integrity": "sha1-j+ZyQ30BzWxFYa9TNODMUP8ZVfc=", "dev": true, "license": "MIT", "dependencies": {"inherits": "^2.0.4", "safe-buffer": "^5.2.1"}, "engines": {"node": ">= 0.10"}}, "node_modules/clean-css": {"version": "5.3.3", "resolved": "http://r.npm.sankuai.com/clean-css/download/clean-css-5.3.3.tgz", "integrity": "sha1-szBlPNO9a3UAnMJccUyue5M1HM0=", "dev": true, "license": "MIT", "dependencies": {"source-map": "~0.6.0"}, "engines": {"node": ">= 10.0"}}, "node_modules/cli-cursor": {"version": "2.1.0", "resolved": "http://r.npm.sankuai.com/cli-cursor/download/cli-cursor-2.1.0.tgz", "integrity": "sha1-s12sN2R5+sw+lHR9QdDQ9SOP/LU=", "dev": true, "license": "MIT", "dependencies": {"restore-cursor": "^2.0.0"}, "engines": {"node": ">=4"}}, "node_modules/cli-highlight": {"version": "2.1.11", "resolved": "http://r.npm.sankuai.com/cli-highlight/download/cli-highlight-2.1.11.tgz", "integrity": "sha1-SXNvpFLwqvT65YDjCssmgo0twb8=", "dev": true, "license": "ISC", "dependencies": {"chalk": "^4.0.0", "highlight.js": "^10.7.1", "mz": "^2.4.0", "parse5": "^5.1.1", "parse5-htmlparser2-tree-adapter": "^6.0.0", "yargs": "^16.0.0"}, "bin": {"highlight": "bin/highlight"}, "engines": {"node": ">=8.0.0", "npm": ">=5.0.0"}}, "node_modules/cli-spinners": {"version": "2.9.2", "resolved": "http://r.npm.sankuai.com/cli-spinners/download/cli-spinners-2.9.2.tgz", "integrity": "sha1-F3Oo9LnE1qwxVj31Oz/B15Ri/kE=", "dev": true, "license": "MIT", "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/clipboardy": {"version": "2.3.0", "resolved": "http://r.npm.sankuai.com/clipboardy/download/clipboardy-2.3.0.tgz", "integrity": "sha1-PCkDZQxo5GqRs4iYW8J3QofbopA=", "dev": true, "license": "MIT", "dependencies": {"arch": "^2.1.1", "execa": "^1.0.0", "is-wsl": "^2.1.1"}, "engines": {"node": ">=8"}}, "node_modules/cliui": {"version": "7.0.4", "resolved": "http://r.npm.sankuai.com/cliui/download/cliui-7.0.4.tgz", "integrity": "sha1-oCZe5lVHb8gHrqnfPfjfd4OAi08=", "dev": true, "license": "ISC", "dependencies": {"string-width": "^4.2.0", "strip-ansi": "^6.0.0", "wrap-ansi": "^7.0.0"}}, "node_modules/clone": {"version": "1.0.4", "resolved": "http://r.npm.sankuai.com/clone/download/clone-1.0.4.tgz", "integrity": "sha1-2jCcwmPfFZlMaIypAheco8fNfH4=", "dev": true, "license": "MIT", "engines": {"node": ">=0.8"}}, "node_modules/clone-deep": {"version": "4.0.1", "resolved": "http://r.npm.sankuai.com/clone-deep/download/clone-deep-4.0.1.tgz", "integrity": "sha1-wZ/Zvbv4WUK0/ZechNz31fB8I4c=", "dev": true, "license": "MIT", "dependencies": {"is-plain-object": "^2.0.4", "kind-of": "^6.0.2", "shallow-clone": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/color-convert": {"version": "2.0.1", "resolved": "http://r.npm.sankuai.com/color-convert/download/color-convert-2.0.1.tgz", "integrity": "sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=", "dev": true, "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/color-name": {"version": "1.1.4", "resolved": "http://r.npm.sankuai.com/color-name/download/color-name-1.1.4.tgz", "integrity": "sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=", "dev": true, "license": "MIT"}, "node_modules/colord": {"version": "2.9.3", "resolved": "http://r.npm.sankuai.com/colord/download/colord-2.9.3.tgz", "integrity": "sha1-T4zpGd5Fbx1cHDaMMH/iDz5Z+0M=", "dev": true, "license": "MIT"}, "node_modules/colorette": {"version": "2.0.20", "resolved": "http://r.npm.sankuai.com/colorette/download/colorette-2.0.20.tgz", "integrity": "sha1-nreT5oMwZ/cjWQL807CZF6AAqVo=", "dev": true, "license": "MIT"}, "node_modules/combined-stream": {"version": "1.0.8", "resolved": "http://r.npm.sankuai.com/combined-stream/download/combined-stream-1.0.8.tgz", "integrity": "sha1-w9RaizT9cwYxoRCoolIGgrMdWn8=", "license": "MIT", "dependencies": {"delayed-stream": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/commander": {"version": "7.2.0", "resolved": "http://r.npm.sankuai.com/commander/download/commander-7.2.0.tgz", "integrity": "sha1-o2y1fQtQHOEI5NIFWaFQo5HZerc=", "dev": true, "license": "MIT", "engines": {"node": ">= 10"}}, "node_modules/commondir": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/commondir/download/commondir-1.0.1.tgz", "integrity": "sha1-3dgA2gxmEnOTzKWVDqloo6rxJTs=", "dev": true, "license": "MIT"}, "node_modules/compressible": {"version": "2.0.18", "resolved": "http://r.npm.sankuai.com/compressible/download/compressible-2.0.18.tgz", "integrity": "sha1-r1PMprBw1MPAdQ+9dyhqbXzEb7o=", "dev": true, "license": "MIT", "dependencies": {"mime-db": ">= 1.43.0 < 2"}, "engines": {"node": ">= 0.6"}}, "node_modules/compressible/node_modules/mime-db": {"version": "1.54.0", "resolved": "http://r.npm.sankuai.com/mime-db/download/mime-db-1.54.0.tgz", "integrity": "sha1-zds+5PnGRTDf9kAjZmHULLajFPU=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/compression": {"version": "1.8.0", "resolved": "http://r.npm.sankuai.com/compression/download/compression-1.8.0.tgz", "integrity": "sha1-CUIO/JbhGg9E86VY3lnjITZBgPc=", "dev": true, "license": "MIT", "dependencies": {"bytes": "3.1.2", "compressible": "~2.0.18", "debug": "2.6.9", "negotiator": "~0.6.4", "on-headers": "~1.0.2", "safe-buffer": "5.2.1", "vary": "~1.1.2"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/compression/node_modules/debug": {"version": "2.6.9", "resolved": "http://r.npm.sankuai.com/debug/download/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "dev": true, "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/compression/node_modules/ms": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/ms/download/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true, "license": "MIT"}, "node_modules/concat-map": {"version": "0.0.1", "resolved": "http://r.npm.sankuai.com/concat-map/download/concat-map-0.0.1.tgz", "integrity": "sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=", "dev": true, "license": "MIT"}, "node_modules/connect-history-api-fallback": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/connect-history-api-fallback/download/connect-history-api-fallback-2.0.0.tgz", "integrity": "sha1-ZHJkhFJRoNryW5fOh4NMrOD18cg=", "dev": true, "license": "MIT", "engines": {"node": ">=0.8"}}, "node_modules/consolidate": {"version": "0.15.1", "resolved": "http://r.npm.sankuai.com/consolidate/download/consolidate-0.15.1.tgz", "integrity": "sha1-IasEMjXHGgfUXZqtmFk7DbpWurc=", "dev": true, "license": "MIT", "dependencies": {"bluebird": "^3.1.1"}, "engines": {"node": ">= 0.10.0"}}, "node_modules/content-disposition": {"version": "0.5.4", "resolved": "http://r.npm.sankuai.com/content-disposition/download/content-disposition-0.5.4.tgz", "integrity": "sha1-i4K076yCUSoCuwsdzsnSxejrW/4=", "dev": true, "license": "MIT", "dependencies": {"safe-buffer": "5.2.1"}, "engines": {"node": ">= 0.6"}}, "node_modules/content-type": {"version": "1.0.5", "resolved": "http://r.npm.sankuai.com/content-type/download/content-type-1.0.5.tgz", "integrity": "sha1-i3cxYmVtHRCGeEyPI6VM5tc9eRg=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/convert-source-map": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/convert-source-map/download/convert-source-map-2.0.0.tgz", "integrity": "sha1-S1YPZJ/E6RjdCrdc9JYei8iC2Co=", "dev": true, "license": "MIT"}, "node_modules/cookie": {"version": "0.7.1", "resolved": "http://r.npm.sankuai.com/cookie/download/cookie-0.7.1.tgz", "integrity": "sha1-L3PEIULV1c9xMQp0/ErmFnDl28k=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/cookie-signature": {"version": "1.0.6", "resolved": "http://r.npm.sankuai.com/cookie-signature/download/cookie-signature-1.0.6.tgz", "integrity": "sha1-4wOogrNCzD7oylE6eZmXNNqzriw=", "dev": true, "license": "MIT"}, "node_modules/copy-webpack-plugin": {"version": "9.1.0", "resolved": "http://r.npm.sankuai.com/copy-webpack-plugin/download/copy-webpack-plugin-9.1.0.tgz", "integrity": "sha1-LSxGDExGlewKWK+ygBoSBSVsTms=", "dev": true, "license": "MIT", "dependencies": {"fast-glob": "^3.2.7", "glob-parent": "^6.0.1", "globby": "^11.0.3", "normalize-path": "^3.0.0", "schema-utils": "^3.1.1", "serialize-javascript": "^6.0.0"}, "engines": {"node": ">= 12.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"webpack": "^5.1.0"}}, "node_modules/copy-webpack-plugin/node_modules/glob-parent": {"version": "6.0.2", "resolved": "http://r.npm.sankuai.com/glob-parent/download/glob-parent-6.0.2.tgz", "integrity": "sha1-bSN9mQg5UMeSkPJMdkKj3poo+eM=", "dev": true, "license": "ISC", "dependencies": {"is-glob": "^4.0.3"}, "engines": {"node": ">=10.13.0"}}, "node_modules/copy-webpack-plugin/node_modules/schema-utils": {"version": "3.3.0", "resolved": "http://r.npm.sankuai.com/schema-utils/download/schema-utils-3.3.0.tgz", "integrity": "sha1-9QqIh3w8AWUqFbYirp6Xld96YP4=", "dev": true, "license": "MIT", "dependencies": {"@types/json-schema": "^7.0.8", "ajv": "^6.12.5", "ajv-keywords": "^3.5.2"}, "engines": {"node": ">= 10.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}}, "node_modules/core-js": {"version": "3.42.0", "resolved": "http://r.npm.sankuai.com/core-js/download/core-js-3.42.0.tgz", "integrity": "sha1-7b6R94rIz7bfjZl+dNNopoCC/jc=", "hasInstallScript": true, "license": "MIT", "funding": {"type": "opencollective", "url": "https://opencollective.com/core-js"}}, "node_modules/core-js-compat": {"version": "3.42.0", "resolved": "http://r.npm.sankuai.com/core-js-compat/download/core-js-compat-3.42.0.tgz", "integrity": "sha1-zhnClwbuWAbibTyzxULUz8DtUbs=", "dev": true, "license": "MIT", "dependencies": {"browserslist": "^4.24.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/core-js"}}, "node_modules/core-util-is": {"version": "1.0.3", "resolved": "http://r.npm.sankuai.com/core-util-is/download/core-util-is-1.0.3.tgz", "integrity": "sha1-pgQtNjTCsn6TKPg3uWX6yDgI24U=", "dev": true, "license": "MIT"}, "node_modules/cosmiconfig": {"version": "7.1.0", "resolved": "http://r.npm.sankuai.com/cosmiconfig/download/cosmiconfig-7.1.0.tgz", "integrity": "sha1-FEO5r6WWtnAILqRsvY9qYrhGNfY=", "dev": true, "license": "MIT", "dependencies": {"@types/parse-json": "^4.0.0", "import-fresh": "^3.2.1", "parse-json": "^5.0.0", "path-type": "^4.0.0", "yaml": "^1.10.0"}, "engines": {"node": ">=10"}}, "node_modules/create-ecdh": {"version": "4.0.4", "resolved": "http://r.npm.sankuai.com/create-ecdh/download/create-ecdh-4.0.4.tgz", "integrity": "sha1-1uf0v/pmc2CFoHYv06YyaE2rzE4=", "dev": true, "license": "MIT", "dependencies": {"bn.js": "^4.1.0", "elliptic": "^6.5.3"}}, "node_modules/create-hash": {"version": "1.2.0", "resolved": "http://r.npm.sankuai.com/create-hash/download/create-hash-1.2.0.tgz", "integrity": "sha1-iJB4rxGmN1a8+1m9IhmWvjqe8ZY=", "dev": true, "license": "MIT", "dependencies": {"cipher-base": "^1.0.1", "inherits": "^2.0.1", "md5.js": "^1.3.4", "ripemd160": "^2.0.1", "sha.js": "^2.4.0"}}, "node_modules/create-hmac": {"version": "1.1.7", "resolved": "http://r.npm.sankuai.com/create-hmac/download/create-hmac-1.1.7.tgz", "integrity": "sha1-aRcMeLOrlXFHsriwRXLkfq0iQ/8=", "dev": true, "license": "MIT", "dependencies": {"cipher-base": "^1.0.3", "create-hash": "^1.1.0", "inherits": "^2.0.1", "ripemd160": "^2.0.0", "safe-buffer": "^5.0.1", "sha.js": "^2.4.8"}}, "node_modules/cross-spawn": {"version": "7.0.6", "resolved": "http://r.npm.sankuai.com/cross-spawn/download/cross-spawn-7.0.6.tgz", "integrity": "sha1-ilj+ePANzXDDcEUXWd+/rwPo7p8=", "dev": true, "license": "MIT", "dependencies": {"path-key": "^3.1.0", "shebang-command": "^2.0.0", "which": "^2.0.1"}, "engines": {"node": ">= 8"}}, "node_modules/cross-spawn/node_modules/path-key": {"version": "3.1.1", "resolved": "http://r.npm.sankuai.com/path-key/download/path-key-3.1.1.tgz", "integrity": "sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/cross-spawn/node_modules/shebang-command": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/shebang-command/download/shebang-command-2.0.0.tgz", "integrity": "sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo=", "dev": true, "license": "MIT", "dependencies": {"shebang-regex": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/cross-spawn/node_modules/shebang-regex": {"version": "3.0.0", "resolved": "http://r.npm.sankuai.com/shebang-regex/download/shebang-regex-3.0.0.tgz", "integrity": "sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/cross-spawn/node_modules/which": {"version": "2.0.2", "resolved": "http://r.npm.sankuai.com/which/download/which-2.0.2.tgz", "integrity": "sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE=", "dev": true, "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"node-which": "bin/node-which"}, "engines": {"node": ">= 8"}}, "node_modules/crypto-browserify": {"version": "3.12.1", "resolved": "http://r.npm.sankuai.com/crypto-browserify/download/crypto-browserify-3.12.1.tgz", "integrity": "sha1-u4khvsmsyBYzN5qo9S1psLaeDaw=", "dev": true, "license": "MIT", "dependencies": {"browserify-cipher": "^1.0.1", "browserify-sign": "^4.2.3", "create-ecdh": "^4.0.4", "create-hash": "^1.2.0", "create-hmac": "^1.1.7", "diffie-hellman": "^5.0.3", "hash-base": "~3.0.4", "inherits": "^2.0.4", "pbkdf2": "^3.1.2", "public-encrypt": "^4.0.3", "randombytes": "^2.1.0", "randomfill": "^1.0.4"}, "engines": {"node": ">= 0.10"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/css-declaration-sorter": {"version": "6.4.1", "resolved": "http://r.npm.sankuai.com/css-declaration-sorter/download/css-declaration-sorter-6.4.1.tgz", "integrity": "sha1-KL6sfCC61/F3W+OnEp1+rkCaOnE=", "dev": true, "license": "ISC", "engines": {"node": "^10 || ^12 || >=14"}, "peerDependencies": {"postcss": "^8.0.9"}}, "node_modules/css-loader": {"version": "6.11.0", "resolved": "http://r.npm.sankuai.com/css-loader/download/css-loader-6.11.0.tgz", "integrity": "sha1-M7rjv2Nj0KfCz5AxyWx0T/VNhbo=", "dev": true, "license": "MIT", "dependencies": {"icss-utils": "^5.1.0", "postcss": "^8.4.33", "postcss-modules-extract-imports": "^3.1.0", "postcss-modules-local-by-default": "^4.0.5", "postcss-modules-scope": "^3.2.0", "postcss-modules-values": "^4.0.0", "postcss-value-parser": "^4.2.0", "semver": "^7.5.4"}, "engines": {"node": ">= 12.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"@rspack/core": "0.x || 1.x", "webpack": "^5.0.0"}, "peerDependenciesMeta": {"@rspack/core": {"optional": true}, "webpack": {"optional": true}}}, "node_modules/css-loader/node_modules/semver": {"version": "7.7.2", "resolved": "http://r.npm.sankuai.com/semver/download/semver-7.7.2.tgz", "integrity": "sha1-Z9mf3NNc7CHm+Lh6f9UVoz+YK1g=", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/css-minimizer-webpack-plugin": {"version": "3.4.1", "resolved": "http://r.npm.sankuai.com/css-minimizer-webpack-plugin/download/css-minimizer-webpack-plugin-3.4.1.tgz", "integrity": "sha1-q3j3gc7ZGBmS/ntuTzQi52Qph48=", "dev": true, "license": "MIT", "dependencies": {"cssnano": "^5.0.6", "jest-worker": "^27.0.2", "postcss": "^8.3.5", "schema-utils": "^4.0.0", "serialize-javascript": "^6.0.0", "source-map": "^0.6.1"}, "engines": {"node": ">= 12.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"webpack": "^5.0.0"}, "peerDependenciesMeta": {"@parcel/css": {"optional": true}, "clean-css": {"optional": true}, "csso": {"optional": true}, "esbuild": {"optional": true}}}, "node_modules/css-select": {"version": "4.3.0", "resolved": "http://r.npm.sankuai.com/css-select/download/css-select-4.3.0.tgz", "integrity": "sha1-23EpsoRmYv2GKM/ElquytZ5BUps=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"boolbase": "^1.0.0", "css-what": "^6.0.1", "domhandler": "^4.3.1", "domutils": "^2.8.0", "nth-check": "^2.0.1"}, "funding": {"url": "https://github.com/sponsors/fb55"}}, "node_modules/css-tree": {"version": "1.1.3", "resolved": "http://r.npm.sankuai.com/css-tree/download/css-tree-1.1.3.tgz", "integrity": "sha1-60hw+2/XcHMn7JXC/yqwm16NuR0=", "dev": true, "license": "MIT", "dependencies": {"mdn-data": "2.0.14", "source-map": "^0.6.1"}, "engines": {"node": ">=8.0.0"}}, "node_modules/css-what": {"version": "6.1.0", "resolved": "http://r.npm.sankuai.com/css-what/download/css-what-6.1.0.tgz", "integrity": "sha1-+17/z3bx3eosgb36pN5E55uscPQ=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">= 6"}, "funding": {"url": "https://github.com/sponsors/fb55"}}, "node_modules/cssesc": {"version": "3.0.0", "resolved": "http://r.npm.sankuai.com/cssesc/download/cssesc-3.0.0.tgz", "integrity": "sha1-N3QZGZA7hoVl4cCep0dEXNGJg+4=", "dev": true, "license": "MIT", "bin": {"cssesc": "bin/cssesc"}, "engines": {"node": ">=4"}}, "node_modules/cssnano": {"version": "5.1.15", "resolved": "http://r.npm.sankuai.com/cssnano/download/cssnano-5.1.15.tgz", "integrity": "sha1-3tZrVIDVEn/LRNrBLqWpg3VRNr8=", "dev": true, "license": "MIT", "dependencies": {"cssnano-preset-default": "^5.2.14", "lilconfig": "^2.0.3", "yaml": "^1.10.2"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/cssnano"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/cssnano-preset-default": {"version": "5.2.14", "resolved": "http://r.npm.sankuai.com/cssnano-preset-default/download/cssnano-preset-default-5.2.14.tgz", "integrity": "sha1-MJ3vT3t+FtcaskOAUgkzMNmrRdg=", "dev": true, "license": "MIT", "dependencies": {"css-declaration-sorter": "^6.3.1", "cssnano-utils": "^3.1.0", "postcss-calc": "^8.2.3", "postcss-colormin": "^5.3.1", "postcss-convert-values": "^5.1.3", "postcss-discard-comments": "^5.1.2", "postcss-discard-duplicates": "^5.1.0", "postcss-discard-empty": "^5.1.1", "postcss-discard-overridden": "^5.1.0", "postcss-merge-longhand": "^5.1.7", "postcss-merge-rules": "^5.1.4", "postcss-minify-font-values": "^5.1.0", "postcss-minify-gradients": "^5.1.1", "postcss-minify-params": "^5.1.4", "postcss-minify-selectors": "^5.2.1", "postcss-normalize-charset": "^5.1.0", "postcss-normalize-display-values": "^5.1.0", "postcss-normalize-positions": "^5.1.1", "postcss-normalize-repeat-style": "^5.1.1", "postcss-normalize-string": "^5.1.0", "postcss-normalize-timing-functions": "^5.1.0", "postcss-normalize-unicode": "^5.1.1", "postcss-normalize-url": "^5.1.0", "postcss-normalize-whitespace": "^5.1.1", "postcss-ordered-values": "^5.1.3", "postcss-reduce-initial": "^5.1.2", "postcss-reduce-transforms": "^5.1.0", "postcss-svgo": "^5.1.0", "postcss-unique-selectors": "^5.1.1"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/cssnano-utils": {"version": "3.1.0", "resolved": "http://r.npm.sankuai.com/cssnano-utils/download/cssnano-utils-3.1.0.tgz", "integrity": "sha1-lWhNCMkVEe38cNJjYzjKN+86aGE=", "dev": true, "license": "MIT", "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/csso": {"version": "4.2.0", "resolved": "http://r.npm.sankuai.com/csso/download/csso-4.2.0.tgz", "integrity": "sha1-6jpWE0bo3J9UbW/r7dUBh884lSk=", "dev": true, "license": "MIT", "dependencies": {"css-tree": "^1.1.2"}, "engines": {"node": ">=8.0.0"}}, "node_modules/csstype": {"version": "3.1.3", "resolved": "http://r.npm.sankuai.com/csstype/download/csstype-3.1.3.tgz", "integrity": "sha1-2A/ylNEU+w5qxQD7+FtgE31+/4E=", "license": "MIT"}, "node_modules/de-indent": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/de-indent/download/de-indent-1.0.2.tgz", "integrity": "sha1-sgOOhG3DO6pXlhKNCAS0VbjB4h0=", "dev": true, "license": "MIT"}, "node_modules/debounce": {"version": "1.2.1", "resolved": "http://r.npm.sankuai.com/debounce/download/debounce-1.2.1.tgz", "integrity": "sha1-OIgdj0FmpcWEgCDBGCe4NLyz4KU=", "dev": true, "license": "MIT"}, "node_modules/debug": {"version": "4.4.1", "resolved": "http://r.npm.sankuai.com/debug/download/debug-4.4.1.tgz", "integrity": "sha1-5ai8bLxMbNPmQwiwaTo9T6VQGJs=", "dev": true, "license": "MIT", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/deep-is": {"version": "0.1.4", "resolved": "http://r.npm.sankuai.com/deep-is/download/deep-is-0.1.4.tgz", "integrity": "sha1-pvLc5hL63S7x9Rm3NVHxfoUZmDE=", "dev": true, "license": "MIT"}, "node_modules/deepmerge": {"version": "1.5.2", "resolved": "http://r.npm.sankuai.com/deepmerge/download/deepmerge-1.5.2.tgz", "integrity": "sha1-EEmdhohEza1P7ghC34x/bwyVp1M=", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/default-gateway": {"version": "6.0.3", "resolved": "http://r.npm.sankuai.com/default-gateway/download/default-gateway-6.0.3.tgz", "integrity": "sha1-gZSUyIgFO9t0PtvzQ9bN9/KUOnE=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"execa": "^5.0.0"}, "engines": {"node": ">= 10"}}, "node_modules/default-gateway/node_modules/execa": {"version": "5.1.1", "resolved": "http://r.npm.sankuai.com/execa/download/execa-5.1.1.tgz", "integrity": "sha1-+ArZy/Qpj3vR1MlVXCHpN0HEEd0=", "dev": true, "license": "MIT", "dependencies": {"cross-spawn": "^7.0.3", "get-stream": "^6.0.0", "human-signals": "^2.1.0", "is-stream": "^2.0.0", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.1", "onetime": "^5.1.2", "signal-exit": "^3.0.3", "strip-final-newline": "^2.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sindresorhus/execa?sponsor=1"}}, "node_modules/default-gateway/node_modules/get-stream": {"version": "6.0.1", "resolved": "http://r.npm.sankuai.com/get-stream/download/get-stream-6.0.1.tgz", "integrity": "sha1-omLY7vZ6ztV8KFKtYWdSakPL97c=", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/default-gateway/node_modules/is-stream": {"version": "2.0.1", "resolved": "http://r.npm.sankuai.com/is-stream/download/is-stream-2.0.1.tgz", "integrity": "sha1-+sHj1TuXrVqdCunO8jifWBClwHc=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/default-gateway/node_modules/npm-run-path": {"version": "4.0.1", "resolved": "http://r.npm.sankuai.com/npm-run-path/download/npm-run-path-4.0.1.tgz", "integrity": "sha1-t+zR5e1T2o43pV4cImnguX7XSOo=", "dev": true, "license": "MIT", "dependencies": {"path-key": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/default-gateway/node_modules/path-key": {"version": "3.1.1", "resolved": "http://r.npm.sankuai.com/path-key/download/path-key-3.1.1.tgz", "integrity": "sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/defaults": {"version": "1.0.4", "resolved": "http://r.npm.sankuai.com/defaults/download/defaults-1.0.4.tgz", "integrity": "sha1-sLAgYsHiqmL/XZUo8PmLqpCXjXo=", "dev": true, "license": "MIT", "dependencies": {"clone": "^1.0.2"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/define-data-property": {"version": "1.1.4", "resolved": "http://r.npm.sankuai.com/define-data-property/download/define-data-property-1.1.4.tgz", "integrity": "sha1-iU3BQbt9MGCuQ2b2oBB+aPvkjF4=", "dev": true, "license": "MIT", "dependencies": {"es-define-property": "^1.0.0", "es-errors": "^1.3.0", "gopd": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/define-lazy-prop": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/define-lazy-prop/download/define-lazy-prop-2.0.0.tgz", "integrity": "sha1-P3rkIRKbyqrJvHSQXJigAJ7J7n8=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/define-properties": {"version": "1.2.1", "resolved": "http://r.npm.sankuai.com/define-properties/download/define-properties-1.2.1.tgz", "integrity": "sha1-EHgcxhbrlRqAoDS6/Kpzd/avK2w=", "dev": true, "license": "MIT", "dependencies": {"define-data-property": "^1.0.1", "has-property-descriptors": "^1.0.0", "object-keys": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/delayed-stream": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/delayed-stream/download/delayed-stream-1.0.0.tgz", "integrity": "sha1-3zrhmayt+31ECqrgsp4icrJOxhk=", "license": "MIT", "engines": {"node": ">=0.4.0"}}, "node_modules/depd": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/depd/download/depd-2.0.0.tgz", "integrity": "sha1-tpYWPMdXVg0JzyLMj60Vcbeedt8=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/des.js": {"version": "1.1.0", "resolved": "http://r.npm.sankuai.com/des.js/download/des.js-1.1.0.tgz", "integrity": "sha1-HTf1dm87v/Tuljjocah2jBc7gdo=", "dev": true, "license": "MIT", "dependencies": {"inherits": "^2.0.1", "minimalistic-assert": "^1.0.0"}}, "node_modules/destroy": {"version": "1.2.0", "resolved": "http://r.npm.sankuai.com/destroy/download/destroy-1.2.0.tgz", "integrity": "sha1-SANzVQmti+VSk0xn32FPlOZvoBU=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8", "npm": "1.2.8000 || >= 1.4.16"}}, "node_modules/detect-node": {"version": "2.1.0", "resolved": "http://r.npm.sankuai.com/detect-node/download/detect-node-2.1.0.tgz", "integrity": "sha1-yccHdaScPQO8LAbZpzvlUPl4+LE=", "dev": true, "license": "MIT"}, "node_modules/diffie-hellman": {"version": "5.0.3", "resolved": "http://r.npm.sankuai.com/diffie-hellman/download/diffie-hellman-5.0.3.tgz", "integrity": "sha1-QOjumPVaIUlgcUaSHGPhrl89KHU=", "dev": true, "license": "MIT", "dependencies": {"bn.js": "^4.1.0", "miller-rabin": "^4.0.0", "randombytes": "^2.0.0"}}, "node_modules/dir-glob": {"version": "3.0.1", "resolved": "http://r.npm.sankuai.com/dir-glob/download/dir-glob-3.0.1.tgz", "integrity": "sha1-Vtv3PZkqSpO6FYT0U0Bj/S5BcX8=", "dev": true, "license": "MIT", "dependencies": {"path-type": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/dns-packet": {"version": "5.6.1", "resolved": "http://r.npm.sankuai.com/dns-packet/download/dns-packet-5.6.1.tgz", "integrity": "sha1-roiK1CWp0UeKBnQlarhm3hASzy8=", "dev": true, "license": "MIT", "dependencies": {"@leichtgewicht/ip-codec": "^2.0.1"}, "engines": {"node": ">=6"}}, "node_modules/doctrine": {"version": "3.0.0", "resolved": "http://r.npm.sankuai.com/doctrine/download/doctrine-3.0.0.tgz", "integrity": "sha1-rd6+rXKmV023g2OdyHoSF3OXOWE=", "dev": true, "license": "Apache-2.0", "dependencies": {"esutils": "^2.0.2"}, "engines": {"node": ">=6.0.0"}}, "node_modules/dom-converter": {"version": "0.2.0", "resolved": "http://r.npm.sankuai.com/dom-converter/download/dom-converter-0.2.0.tgz", "integrity": "sha1-ZyGp2u4uKTaClVtq/kFncWJ7t2g=", "dev": true, "license": "MIT", "dependencies": {"utila": "~0.4"}}, "node_modules/dom-serializer": {"version": "1.4.1", "resolved": "http://r.npm.sankuai.com/dom-serializer/download/dom-serializer-1.4.1.tgz", "integrity": "sha1-3l1Bsa6ikCFdxFptrorc8dMuLTA=", "dev": true, "license": "MIT", "dependencies": {"domelementtype": "^2.0.1", "domhandler": "^4.2.0", "entities": "^2.0.0"}, "funding": {"url": "https://github.com/cheeriojs/dom-serializer?sponsor=1"}}, "node_modules/domelementtype": {"version": "2.3.0", "resolved": "http://r.npm.sankuai.com/domelementtype/download/domelementtype-2.3.0.tgz", "integrity": "sha1-XEXo6GmVJiYzHXqrMm0B2vZdWJ0=", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/fb55"}], "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/domhandler": {"version": "4.3.1", "resolved": "http://r.npm.sankuai.com/domhandler/download/domhandler-4.3.1.tgz", "integrity": "sha1-jXkgM0FvWdaLwDpap7AYwcqJJ5w=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"domelementtype": "^2.2.0"}, "engines": {"node": ">= 4"}, "funding": {"url": "https://github.com/fb55/domhandler?sponsor=1"}}, "node_modules/domutils": {"version": "2.8.0", "resolved": "http://r.npm.sankuai.com/domutils/download/domutils-2.8.0.tgz", "integrity": "sha1-RDfe9dtuLR9dbuhZvZXKfQIEgTU=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"dom-serializer": "^1.0.1", "domelementtype": "^2.2.0", "domhandler": "^4.2.0"}, "funding": {"url": "https://github.com/fb55/domutils?sponsor=1"}}, "node_modules/dot-case": {"version": "3.0.4", "resolved": "http://r.npm.sankuai.com/dot-case/download/dot-case-3.0.4.tgz", "integrity": "sha1-mytnDQCkMWZ6inW6Kc0bmICc51E=", "dev": true, "license": "MIT", "dependencies": {"no-case": "^3.0.4", "tslib": "^2.0.3"}}, "node_modules/dotenv": {"version": "10.0.0", "resolved": "http://r.npm.sankuai.com/dotenv/download/dotenv-10.0.0.tgz", "integrity": "sha1-PUInuPuV+BCWzdK2ZlP7LHCFuoE=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=10"}}, "node_modules/dotenv-expand": {"version": "5.1.0", "resolved": "http://r.npm.sankuai.com/dotenv-expand/download/dotenv-expand-5.1.0.tgz", "integrity": "sha1-P7rwIL/XlIhAcuomsel5HUWmKfA=", "dev": true, "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/dunder-proto": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/dunder-proto/download/dunder-proto-1.0.1.tgz", "integrity": "sha1-165mfh3INIL4tw/Q9u78UNow9Yo=", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.1", "es-errors": "^1.3.0", "gopd": "^1.2.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/duplexer": {"version": "0.1.2", "resolved": "http://r.npm.sankuai.com/duplexer/download/duplexer-0.1.2.tgz", "integrity": "sha1-Or5DrvODX4rgd9E23c4PJ2sEAOY=", "dev": true, "license": "MIT"}, "node_modules/easy-stack": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/easy-stack/download/easy-stack-1.0.1.tgz", "integrity": "sha1-iv5CZGJpiMq7EfPHBMzQyDVBEGY=", "dev": true, "license": "MIT", "engines": {"node": ">=6.0.0"}}, "node_modules/ee-first": {"version": "1.1.1", "resolved": "http://r.npm.sankuai.com/ee-first/download/ee-first-1.1.1.tgz", "integrity": "sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0=", "dev": true, "license": "MIT"}, "node_modules/electron-to-chromium": {"version": "1.5.155", "resolved": "http://r.npm.sankuai.com/electron-to-chromium/download/electron-to-chromium-1.5.155.tgz", "integrity": "sha1-gJ3Qrprh24fDWODAwXwJov/EMtE=", "dev": true, "license": "ISC"}, "node_modules/element-ui": {"version": "2.15.14", "resolved": "http://r.npm.sankuai.com/element-ui/download/element-ui-2.15.14.tgz", "integrity": "sha1-PDTfeUZ2NlkoEtcg0uZ4Tnpuwuo=", "license": "MIT", "dependencies": {"async-validator": "~1.8.1", "babel-helper-vue-jsx-merge-props": "^2.0.0", "deepmerge": "^1.2.0", "normalize-wheel": "^1.0.1", "resize-observer-polyfill": "^1.5.0", "throttle-debounce": "^1.0.1"}, "peerDependencies": {"vue": "^2.5.17"}}, "node_modules/elliptic": {"version": "6.6.1", "resolved": "http://r.npm.sankuai.com/elliptic/download/elliptic-6.6.1.tgz", "integrity": "sha1-O4/7AmcL9p44LH9lv1JMl8VAXAY=", "dev": true, "license": "MIT", "dependencies": {"bn.js": "^4.11.9", "brorand": "^1.1.0", "hash.js": "^1.0.0", "hmac-drbg": "^1.0.1", "inherits": "^2.0.4", "minimalistic-assert": "^1.0.1", "minimalistic-crypto-utils": "^1.0.1"}}, "node_modules/emoji-regex": {"version": "8.0.0", "resolved": "http://r.npm.sankuai.com/emoji-regex/download/emoji-regex-8.0.0.tgz", "integrity": "sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=", "dev": true, "license": "MIT"}, "node_modules/emojis-list": {"version": "3.0.0", "resolved": "http://r.npm.sankuai.com/emojis-list/download/emojis-list-3.0.0.tgz", "integrity": "sha1-VXBmIEatKeLpFucariYKvf9Pang=", "dev": true, "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/encodeurl": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/encodeurl/download/encodeurl-2.0.0.tgz", "integrity": "sha1-e46omAd9fkCdOsRUdOo46vCFelg=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/end-of-stream": {"version": "1.4.4", "resolved": "http://r.npm.sankuai.com/end-of-stream/download/end-of-stream-1.4.4.tgz", "integrity": "sha1-WuZKX0UFe682JuwU2gyl5LJDHrA=", "dev": true, "license": "MIT", "dependencies": {"once": "^1.4.0"}}, "node_modules/enhanced-resolve": {"version": "5.18.1", "resolved": "http://r.npm.sankuai.com/enhanced-resolve/download/enhanced-resolve-5.18.1.tgz", "integrity": "sha1-coqwgvi3toNt5R8WN6q107lWj68=", "dev": true, "license": "MIT", "dependencies": {"graceful-fs": "^4.2.4", "tapable": "^2.2.0"}, "engines": {"node": ">=10.13.0"}}, "node_modules/enquirer": {"version": "2.4.1", "resolved": "http://r.npm.sankuai.com/enquirer/download/enquirer-2.4.1.tgz", "integrity": "sha1-kzNLP710/HCXsiSrSo+35Av0rlY=", "dev": true, "license": "MIT", "dependencies": {"ansi-colors": "^4.1.1", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8.6"}}, "node_modules/entities": {"version": "2.2.0", "resolved": "http://r.npm.sankuai.com/entities/download/entities-2.2.0.tgz", "integrity": "sha1-CY3JDruD2N/6CJ1VJWs1HTTE2lU=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "funding": {"url": "https://github.com/fb55/entities?sponsor=1"}}, "node_modules/error-ex": {"version": "1.3.2", "resolved": "http://r.npm.sankuai.com/error-ex/download/error-ex-1.3.2.tgz", "integrity": "sha1-tKxAZIEH/c3PriQvQovqihTU8b8=", "dev": true, "license": "MIT", "dependencies": {"is-arrayish": "^0.2.1"}}, "node_modules/error-stack-parser": {"version": "2.1.4", "resolved": "http://r.npm.sankuai.com/error-stack-parser/download/error-stack-parser-2.1.4.tgz", "integrity": "sha1-IpywHNv6hEQL+pGHYoW5RoAYgoY=", "dev": true, "license": "MIT", "dependencies": {"stackframe": "^1.3.4"}}, "node_modules/es-define-property": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/es-define-property/download/es-define-property-1.0.1.tgz", "integrity": "sha1-mD6y+aZyTpMD9hrd8BHHLgngsPo=", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/es-errors": {"version": "1.3.0", "resolved": "http://r.npm.sankuai.com/es-errors/download/es-errors-1.3.0.tgz", "integrity": "sha1-BfdaJdq5jk+x3NXhRywFRtUFfI8=", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/es-module-lexer": {"version": "1.7.0", "resolved": "http://r.npm.sankuai.com/es-module-lexer/download/es-module-lexer-1.7.0.tgz", "integrity": "sha1-kVlgFWGICoXyc0VgqQmbLDHlNyo=", "dev": true, "license": "MIT"}, "node_modules/es-object-atoms": {"version": "1.1.1", "resolved": "http://r.npm.sankuai.com/es-object-atoms/download/es-object-atoms-1.1.1.tgz", "integrity": "sha1-HE8sSDcydZfOadLKGQp/3RcjOME=", "license": "MIT", "dependencies": {"es-errors": "^1.3.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/es-set-tostringtag": {"version": "2.1.0", "resolved": "http://r.npm.sankuai.com/es-set-tostringtag/download/es-set-tostringtag-2.1.0.tgz", "integrity": "sha1-8x274MGDsAptJutjJcgQwP0YvU0=", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "get-intrinsic": "^1.2.6", "has-tostringtag": "^1.0.2", "hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/escalade": {"version": "3.2.0", "resolved": "http://r.npm.sankuai.com/escalade/download/escalade-3.2.0.tgz", "integrity": "sha1-ARo/aYVroYnf+n3I/M6Z0qh5A+U=", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/escape-html": {"version": "1.0.3", "resolved": "http://r.npm.sankuai.com/escape-html/download/escape-html-1.0.3.tgz", "integrity": "sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg=", "dev": true, "license": "MIT"}, "node_modules/escape-string-regexp": {"version": "1.0.5", "resolved": "http://r.npm.sankuai.com/escape-string-regexp/download/escape-string-regexp-1.0.5.tgz", "integrity": "sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=", "dev": true, "license": "MIT", "engines": {"node": ">=0.8.0"}}, "node_modules/eslint": {"version": "7.32.0", "resolved": "http://r.npm.sankuai.com/eslint/download/eslint-7.32.0.tgz", "integrity": "sha1-xtMooUvj+wjI0dIeEsAv23oqgS0=", "dev": true, "license": "MIT", "dependencies": {"@babel/code-frame": "7.12.11", "@eslint/eslintrc": "^0.4.3", "@humanwhocodes/config-array": "^0.5.0", "ajv": "^6.10.0", "chalk": "^4.0.0", "cross-spawn": "^7.0.2", "debug": "^4.0.1", "doctrine": "^3.0.0", "enquirer": "^2.3.5", "escape-string-regexp": "^4.0.0", "eslint-scope": "^5.1.1", "eslint-utils": "^2.1.0", "eslint-visitor-keys": "^2.0.0", "espree": "^7.3.1", "esquery": "^1.4.0", "esutils": "^2.0.2", "fast-deep-equal": "^3.1.3", "file-entry-cache": "^6.0.1", "functional-red-black-tree": "^1.0.1", "glob-parent": "^5.1.2", "globals": "^13.6.0", "ignore": "^4.0.6", "import-fresh": "^3.0.0", "imurmurhash": "^0.1.4", "is-glob": "^4.0.0", "js-yaml": "^3.13.1", "json-stable-stringify-without-jsonify": "^1.0.1", "levn": "^0.4.1", "lodash.merge": "^4.6.2", "minimatch": "^3.0.4", "natural-compare": "^1.4.0", "optionator": "^0.9.1", "progress": "^2.0.0", "regexpp": "^3.1.0", "semver": "^7.2.1", "strip-ansi": "^6.0.0", "strip-json-comments": "^3.1.0", "table": "^6.0.9", "text-table": "^0.2.0", "v8-compile-cache": "^2.0.3"}, "bin": {"eslint": "bin/eslint.js"}, "engines": {"node": "^10.12.0 || >=12.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/eslint-plugin-vue": {"version": "8.7.1", "resolved": "http://r.npm.sankuai.com/eslint-plugin-vue/download/eslint-plugin-vue-8.7.1.tgz", "integrity": "sha1-8TxTVHoMnWRYimdcxezGzK9jcD8=", "dev": true, "license": "MIT", "dependencies": {"eslint-utils": "^3.0.0", "natural-compare": "^1.4.0", "nth-check": "^2.0.1", "postcss-selector-parser": "^6.0.9", "semver": "^7.3.5", "vue-eslint-parser": "^8.0.1"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "peerDependencies": {"eslint": "^6.2.0 || ^7.0.0 || ^8.0.0"}}, "node_modules/eslint-plugin-vue/node_modules/eslint-utils": {"version": "3.0.0", "resolved": "http://r.npm.sankuai.com/eslint-utils/download/eslint-utils-3.0.0.tgz", "integrity": "sha1-iuuvrOc0W7M1WdsKHxOh0tSMNnI=", "dev": true, "license": "MIT", "dependencies": {"eslint-visitor-keys": "^2.0.0"}, "engines": {"node": "^10.0.0 || ^12.0.0 || >= 14.0.0"}, "funding": {"url": "https://github.com/sponsors/mysticatea"}, "peerDependencies": {"eslint": ">=5"}}, "node_modules/eslint-plugin-vue/node_modules/semver": {"version": "7.7.2", "resolved": "http://r.npm.sankuai.com/semver/download/semver-7.7.2.tgz", "integrity": "sha1-Z9mf3NNc7CHm+Lh6f9UVoz+YK1g=", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/eslint-scope": {"version": "5.1.1", "resolved": "http://r.npm.sankuai.com/eslint-scope/download/eslint-scope-5.1.1.tgz", "integrity": "sha1-54blmmbLkrP2wfsNUIqrF0hI9Iw=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^4.1.1"}, "engines": {"node": ">=8.0.0"}}, "node_modules/eslint-scope/node_modules/estraverse": {"version": "4.3.0", "resolved": "http://r.npm.sankuai.com/estraverse/download/estraverse-4.3.0.tgz", "integrity": "sha1-OYrT88WiSUi+dyXoPRGn3ijNvR0=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=4.0"}}, "node_modules/eslint-utils": {"version": "2.1.0", "resolved": "http://r.npm.sankuai.com/eslint-utils/download/eslint-utils-2.1.0.tgz", "integrity": "sha1-0t5eA0JOcH3BDHQGjd7a5wh0Gyc=", "dev": true, "license": "MIT", "dependencies": {"eslint-visitor-keys": "^1.1.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/mysticatea"}}, "node_modules/eslint-utils/node_modules/eslint-visitor-keys": {"version": "1.3.0", "resolved": "http://r.npm.sankuai.com/eslint-visitor-keys/download/eslint-visitor-keys-1.3.0.tgz", "integrity": "sha1-MOvR73wv3/AcOk8VEESvJfqwUj4=", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=4"}}, "node_modules/eslint-visitor-keys": {"version": "2.1.0", "resolved": "http://r.npm.sankuai.com/eslint-visitor-keys/download/eslint-visitor-keys-2.1.0.tgz", "integrity": "sha1-9lMoJZMFknOSyTjtROsKXJsr0wM=", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=10"}}, "node_modules/eslint-webpack-plugin": {"version": "3.2.0", "resolved": "http://r.npm.sankuai.com/eslint-webpack-plugin/download/eslint-webpack-plugin-3.2.0.tgz", "integrity": "sha1-GXjNue3EYeSwGVog2pUM9XmINHw=", "dev": true, "license": "MIT", "dependencies": {"@types/eslint": "^7.29.0 || ^8.4.1", "jest-worker": "^28.0.2", "micromatch": "^4.0.5", "normalize-path": "^3.0.0", "schema-utils": "^4.0.0"}, "engines": {"node": ">= 12.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"eslint": "^7.0.0 || ^8.0.0", "webpack": "^5.0.0"}}, "node_modules/eslint-webpack-plugin/node_modules/jest-worker": {"version": "28.1.3", "resolved": "http://r.npm.sankuai.com/jest-worker/download/jest-worker-28.1.3.tgz", "integrity": "sha1-fjxM4/oj0btqzLFp5/OW+Y7Uu5g=", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}}, "node_modules/eslint-webpack-plugin/node_modules/supports-color": {"version": "8.1.1", "resolved": "http://r.npm.sankuai.com/supports-color/download/supports-color-8.1.1.tgz", "integrity": "sha1-zW/BfihQDP9WwbhsCn/UpUpzAFw=", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/supports-color?sponsor=1"}}, "node_modules/eslint/node_modules/@babel/code-frame": {"version": "7.12.11", "resolved": "http://r.npm.sankuai.com/@babel/code-frame/download/@babel/code-frame-7.12.11.tgz", "integrity": "sha1-9K1DWqJj25NbjxDyxVLSP7cWpj8=", "dev": true, "license": "MIT", "dependencies": {"@babel/highlight": "^7.10.4"}}, "node_modules/eslint/node_modules/escape-string-regexp": {"version": "4.0.0", "resolved": "http://r.npm.sankuai.com/escape-string-regexp/download/escape-string-regexp-4.0.0.tgz", "integrity": "sha1-FLqDpdNz49MR5a/KKc9b+tllvzQ=", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/eslint/node_modules/globals": {"version": "13.24.0", "resolved": "http://r.npm.sankuai.com/globals/download/globals-13.24.0.tgz", "integrity": "sha1-hDKhnXjODB6DOUnDats0VAC7EXE=", "dev": true, "license": "MIT", "dependencies": {"type-fest": "^0.20.2"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/eslint/node_modules/semver": {"version": "7.7.2", "resolved": "http://r.npm.sankuai.com/semver/download/semver-7.7.2.tgz", "integrity": "sha1-Z9mf3NNc7CHm+Lh6f9UVoz+YK1g=", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/espree": {"version": "7.3.1", "resolved": "http://r.npm.sankuai.com/espree/download/espree-7.3.1.tgz", "integrity": "sha1-8t8zC3Usb1UBn4vYm3ZgA5wbu7Y=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"acorn": "^7.4.0", "acorn-jsx": "^5.3.1", "eslint-visitor-keys": "^1.3.0"}, "engines": {"node": "^10.12.0 || >=12.0.0"}}, "node_modules/espree/node_modules/acorn": {"version": "7.4.1", "resolved": "http://r.npm.sankuai.com/acorn/download/acorn-7.4.1.tgz", "integrity": "sha1-/q7SVZc9LndVW4PbwIhRpsY1IPo=", "dev": true, "license": "MIT", "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/espree/node_modules/eslint-visitor-keys": {"version": "1.3.0", "resolved": "http://r.npm.sankuai.com/eslint-visitor-keys/download/eslint-visitor-keys-1.3.0.tgz", "integrity": "sha1-MOvR73wv3/AcOk8VEESvJfqwUj4=", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=4"}}, "node_modules/esprima": {"version": "4.0.1", "resolved": "http://r.npm.sankuai.com/esprima/download/esprima-4.0.1.tgz", "integrity": "sha1-E7BM2z5sXRnfkatph6hpVhmwqnE=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "bin": {"esparse": "bin/esparse.js", "esvalidate": "bin/esvalidate.js"}, "engines": {"node": ">=4"}}, "node_modules/esquery": {"version": "1.6.0", "resolved": "http://r.npm.sankuai.com/esquery/download/esquery-1.6.0.tgz", "integrity": "sha1-kUGSNPgE2FKoLc7sPhbNwiz52uc=", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"estraverse": "^5.1.0"}, "engines": {"node": ">=0.10"}}, "node_modules/esrecurse": {"version": "4.3.0", "resolved": "http://r.npm.sankuai.com/esrecurse/download/esrecurse-4.3.0.tgz", "integrity": "sha1-eteWTWeauyi+5yzsY3WLHF0smSE=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"estraverse": "^5.2.0"}, "engines": {"node": ">=4.0"}}, "node_modules/estraverse": {"version": "5.3.0", "resolved": "http://r.npm.sankuai.com/estraverse/download/estraverse-5.3.0.tgz", "integrity": "sha1-LupSkHAvJquP5TcDcP+GyWXSESM=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=4.0"}}, "node_modules/estree-walker": {"version": "2.0.2", "resolved": "http://r.npm.sankuai.com/estree-walker/download/estree-walker-2.0.2.tgz", "integrity": "sha1-UvAQF4wqTBF6d1fP6UKtt9LaTKw=", "dev": true, "license": "MIT"}, "node_modules/esutils": {"version": "2.0.3", "resolved": "http://r.npm.sankuai.com/esutils/download/esutils-2.0.3.tgz", "integrity": "sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/etag": {"version": "1.8.1", "resolved": "http://r.npm.sankuai.com/etag/download/etag-1.8.1.tgz", "integrity": "sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/event-pubsub": {"version": "4.3.0", "resolved": "http://r.npm.sankuai.com/event-pubsub/download/event-pubsub-4.3.0.tgz", "integrity": "sha1-9o2Ba8KfHsAsU53FjI3UDOcss24=", "dev": true, "license": "Unlicense", "engines": {"node": ">=4.0.0"}}, "node_modules/event-target-shim": {"version": "5.0.1", "resolved": "http://r.npm.sankuai.com/event-target-shim/download/event-target-shim-5.0.1.tgz", "integrity": "sha1-XU0+vflYPWOlMzzi3rdICrKwV4k=", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/eventemitter3": {"version": "4.0.7", "resolved": "http://r.npm.sankuai.com/eventemitter3/download/eventemitter3-4.0.7.tgz", "integrity": "sha1-Lem2j2Uo1WRO9cWVJqG0oHMGFp8=", "dev": true, "license": "MIT"}, "node_modules/events": {"version": "3.3.0", "resolved": "http://r.npm.sankuai.com/events/download/events-3.3.0.tgz", "integrity": "sha1-Mala0Kkk4tLEGagTrrLE6HjqdAA=", "dev": true, "license": "MIT", "engines": {"node": ">=0.8.x"}}, "node_modules/evp_bytestokey": {"version": "1.0.3", "resolved": "http://r.npm.sankuai.com/evp_bytestokey/download/evp_bytestokey-1.0.3.tgz", "integrity": "sha1-f8vbGY3HGVlDLv4ThCaE4FJaywI=", "dev": true, "license": "MIT", "dependencies": {"md5.js": "^1.3.4", "safe-buffer": "^5.1.1"}}, "node_modules/execa": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/execa/download/execa-1.0.0.tgz", "integrity": "sha1-xiNqW7TfbW8V6I5/AXeYIWdJ3dg=", "dev": true, "license": "MIT", "dependencies": {"cross-spawn": "^6.0.0", "get-stream": "^4.0.0", "is-stream": "^1.1.0", "npm-run-path": "^2.0.0", "p-finally": "^1.0.0", "signal-exit": "^3.0.0", "strip-eof": "^1.0.0"}, "engines": {"node": ">=6"}}, "node_modules/execa/node_modules/cross-spawn": {"version": "6.0.6", "resolved": "http://r.npm.sankuai.com/cross-spawn/download/cross-spawn-6.0.6.tgz", "integrity": "sha1-MNDvoHEt2361p24ehyG/+vprXVc=", "dev": true, "license": "MIT", "dependencies": {"nice-try": "^1.0.4", "path-key": "^2.0.1", "semver": "^5.5.0", "shebang-command": "^1.2.0", "which": "^1.2.9"}, "engines": {"node": ">=4.8"}}, "node_modules/execa/node_modules/semver": {"version": "5.7.2", "resolved": "http://r.npm.sankuai.com/semver/download/semver-5.7.2.tgz", "integrity": "sha1-SNVdtzfDKHzUg14X+hP+rOHEHvg=", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver"}}, "node_modules/express": {"version": "4.21.2", "resolved": "http://r.npm.sankuai.com/express/download/express-4.21.2.tgz", "integrity": "sha1-zyUOSDYhdOrWzqSlZqvvAWLB7DI=", "dev": true, "license": "MIT", "dependencies": {"accepts": "~1.3.8", "array-flatten": "1.1.1", "body-parser": "1.20.3", "content-disposition": "0.5.4", "content-type": "~1.0.4", "cookie": "0.7.1", "cookie-signature": "1.0.6", "debug": "2.6.9", "depd": "2.0.0", "encodeurl": "~2.0.0", "escape-html": "~1.0.3", "etag": "~1.8.1", "finalhandler": "1.3.1", "fresh": "0.5.2", "http-errors": "2.0.0", "merge-descriptors": "1.0.3", "methods": "~1.1.2", "on-finished": "2.4.1", "parseurl": "~1.3.3", "path-to-regexp": "0.1.12", "proxy-addr": "~2.0.7", "qs": "6.13.0", "range-parser": "~1.2.1", "safe-buffer": "5.2.1", "send": "0.19.0", "serve-static": "1.16.2", "setprototypeof": "1.2.0", "statuses": "2.0.1", "type-is": "~1.6.18", "utils-merge": "1.0.1", "vary": "~1.1.2"}, "engines": {"node": ">= 0.10.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/express"}}, "node_modules/express/node_modules/debug": {"version": "2.6.9", "resolved": "http://r.npm.sankuai.com/debug/download/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "dev": true, "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/express/node_modules/ms": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/ms/download/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true, "license": "MIT"}, "node_modules/fast-deep-equal": {"version": "3.1.3", "resolved": "http://r.npm.sankuai.com/fast-deep-equal/download/fast-deep-equal-3.1.3.tgz", "integrity": "sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=", "dev": true, "license": "MIT"}, "node_modules/fast-glob": {"version": "3.3.3", "resolved": "http://r.npm.sankuai.com/fast-glob/download/fast-glob-3.3.3.tgz", "integrity": "sha1-0G1YXOjbqQoWsFBcVDw8z7OuuBg=", "dev": true, "license": "MIT", "dependencies": {"@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3", "glob-parent": "^5.1.2", "merge2": "^1.3.0", "micromatch": "^4.0.8"}, "engines": {"node": ">=8.6.0"}}, "node_modules/fast-json-stable-stringify": {"version": "2.1.0", "resolved": "http://r.npm.sankuai.com/fast-json-stable-stringify/download/fast-json-stable-stringify-2.1.0.tgz", "integrity": "sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=", "dev": true, "license": "MIT"}, "node_modules/fast-levenshtein": {"version": "2.0.6", "resolved": "http://r.npm.sankuai.com/fast-levenshtein/download/fast-levenshtein-2.0.6.tgz", "integrity": "sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=", "dev": true, "license": "MIT"}, "node_modules/fast-uri": {"version": "3.0.6", "resolved": "http://r.npm.sankuai.com/fast-uri/download/fast-uri-3.0.6.tgz", "integrity": "sha1-iPEwt3z66iN41Wv5cN6iElemh0g=", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/fastify"}, {"type": "opencollective", "url": "https://opencollective.com/fastify"}], "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/fastq": {"version": "1.19.1", "resolved": "http://r.npm.sankuai.com/fastq/download/fastq-1.19.1.tgz", "integrity": "sha1-1Q6rqAPIhGqIPBZJKCHrzSzaVfU=", "dev": true, "license": "ISC", "dependencies": {"reusify": "^1.0.4"}}, "node_modules/faye-websocket": {"version": "0.11.4", "resolved": "http://r.npm.sankuai.com/faye-websocket/download/faye-websocket-0.11.4.tgz", "integrity": "sha1-fw2Sdc/dhqHJY9yLZfzEUe3Lsdo=", "dev": true, "license": "Apache-2.0", "dependencies": {"websocket-driver": ">=0.5.1"}, "engines": {"node": ">=0.8.0"}}, "node_modules/figures": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/figures/download/figures-2.0.0.tgz", "integrity": "sha1-OrGi0qYsi/tDGgyUy3l6L84nyWI=", "dev": true, "license": "MIT", "dependencies": {"escape-string-regexp": "^1.0.5"}, "engines": {"node": ">=4"}}, "node_modules/file-entry-cache": {"version": "6.0.1", "resolved": "http://r.npm.sankuai.com/file-entry-cache/download/file-entry-cache-6.0.1.tgz", "integrity": "sha1-IRst2WWcsDlLBz5zI6w8kz1SICc=", "dev": true, "license": "MIT", "dependencies": {"flat-cache": "^3.0.4"}, "engines": {"node": "^10.12.0 || >=12.0.0"}}, "node_modules/fill-range": {"version": "7.1.1", "resolved": "http://r.npm.sankuai.com/fill-range/download/fill-range-7.1.1.tgz", "integrity": "sha1-RCZdPKwH4+p9wkdRY4BkN1SgUpI=", "dev": true, "license": "MIT", "dependencies": {"to-regex-range": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/finalhandler": {"version": "1.3.1", "resolved": "http://r.npm.sankuai.com/finalhandler/download/finalhandler-1.3.1.tgz", "integrity": "sha1-DFdfHR0yTd0do1rX7OPffRkIgBk=", "dev": true, "license": "MIT", "dependencies": {"debug": "2.6.9", "encodeurl": "~2.0.0", "escape-html": "~1.0.3", "on-finished": "2.4.1", "parseurl": "~1.3.3", "statuses": "2.0.1", "unpipe": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/finalhandler/node_modules/debug": {"version": "2.6.9", "resolved": "http://r.npm.sankuai.com/debug/download/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "dev": true, "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/finalhandler/node_modules/ms": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/ms/download/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true, "license": "MIT"}, "node_modules/find-cache-dir": {"version": "3.3.2", "resolved": "http://r.npm.sankuai.com/find-cache-dir/download/find-cache-dir-3.3.2.tgz", "integrity": "sha1-swxbbv8HMHMa6pu9nb7L2AJW1ks=", "dev": true, "license": "MIT", "dependencies": {"commondir": "^1.0.1", "make-dir": "^3.0.2", "pkg-dir": "^4.1.0"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/avajs/find-cache-dir?sponsor=1"}}, "node_modules/find-up": {"version": "4.1.0", "resolved": "http://r.npm.sankuai.com/find-up/download/find-up-4.1.0.tgz", "integrity": "sha1-l6/n1s3AvFkoWEt8jXsW6KmqXRk=", "dev": true, "license": "MIT", "dependencies": {"locate-path": "^5.0.0", "path-exists": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/flat": {"version": "5.0.2", "resolved": "http://r.npm.sankuai.com/flat/download/flat-5.0.2.tgz", "integrity": "sha1-jKb+MyBp/6nTJMMnGYxZglnOskE=", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "bin": {"flat": "cli.js"}}, "node_modules/flat-cache": {"version": "3.2.0", "resolved": "http://r.npm.sankuai.com/flat-cache/download/flat-cache-3.2.0.tgz", "integrity": "sha1-LAwtUEDJmxYydxqdEFclwBFTY+4=", "dev": true, "license": "MIT", "dependencies": {"flatted": "^3.2.9", "keyv": "^4.5.3", "rimraf": "^3.0.2"}, "engines": {"node": "^10.12.0 || >=12.0.0"}}, "node_modules/flatted": {"version": "3.3.3", "resolved": "http://r.npm.sankuai.com/flatted/download/flatted-3.3.3.tgz", "integrity": "sha1-Z8j62VRUp8er6/dLt47nSkQCM1g=", "dev": true, "license": "ISC"}, "node_modules/follow-redirects": {"version": "1.15.9", "resolved": "http://r.npm.sankuai.com/follow-redirects/download/follow-redirects-1.15.9.tgz", "integrity": "sha1-pgT6EORDv5jKlCKNnuvMLoosjuE=", "funding": [{"type": "individual", "url": "https://github.com/sponsors/Ruben<PERSON>"}], "license": "MIT", "engines": {"node": ">=4.0"}, "peerDependenciesMeta": {"debug": {"optional": true}}}, "node_modules/for-each": {"version": "0.3.5", "resolved": "http://r.npm.sankuai.com/for-each/download/for-each-0.3.5.tgz", "integrity": "sha1-1lBogCeCaSD+6wr3R+57lCGkHUc=", "dev": true, "license": "MIT", "dependencies": {"is-callable": "^1.2.7"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/form-data": {"version": "4.0.2", "resolved": "http://r.npm.sankuai.com/form-data/download/form-data-4.0.2.tgz", "integrity": "sha1-Ncq73TDDznPessQtPI0+2cpReUw=", "license": "MIT", "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "es-set-tostringtag": "^2.1.0", "mime-types": "^2.1.12"}, "engines": {"node": ">= 6"}}, "node_modules/form-data-encoder": {"version": "1.7.2", "resolved": "http://r.npm.sankuai.com/form-data-encoder/download/form-data-encoder-1.7.2.tgz", "integrity": "sha1-Hxrj3M9Y7UaQuG2H5PV8ZU+6sEA=", "license": "MIT"}, "node_modules/formdata-node": {"version": "4.4.1", "resolved": "http://r.npm.sankuai.com/formdata-node/download/formdata-node-4.4.1.tgz", "integrity": "sha1-I/aly5y1UxWRLL7E/3sPWbvRkeI=", "license": "MIT", "dependencies": {"node-domexception": "1.0.0", "web-streams-polyfill": "4.0.0-beta.3"}, "engines": {"node": ">= 12.20"}}, "node_modules/forwarded": {"version": "0.2.0", "resolved": "http://r.npm.sankuai.com/forwarded/download/forwarded-0.2.0.tgz", "integrity": "sha1-ImmTZCiq1MFcfr6XeahL8LKoGBE=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/fraction.js": {"version": "4.3.7", "resolved": "http://r.npm.sankuai.com/fraction.js/download/fraction.js-4.3.7.tgz", "integrity": "sha1-BsoAhRV+Qv2n+ecm55/vxAaIQPc=", "dev": true, "license": "MIT", "engines": {"node": "*"}, "funding": {"type": "patreon", "url": "https://github.com/sponsors/rawify"}}, "node_modules/fresh": {"version": "0.5.2", "resolved": "http://r.npm.sankuai.com/fresh/download/fresh-0.5.2.tgz", "integrity": "sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/fs-extra": {"version": "9.1.0", "resolved": "http://r.npm.sankuai.com/fs-extra/download/fs-extra-9.1.0.tgz", "integrity": "sha1-WVRGDHZKjaIJS6NVS/g55rmnyG0=", "dev": true, "license": "MIT", "dependencies": {"at-least-node": "^1.0.0", "graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=10"}}, "node_modules/fs-monkey": {"version": "1.0.6", "resolved": "http://r.npm.sankuai.com/fs-monkey/download/fs-monkey-1.0.6.tgz", "integrity": "sha1-jq0IKVPojZks8/+ET6qQeyZ1baI=", "dev": true, "license": "Unlicense"}, "node_modules/fs.realpath": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/fs.realpath/download/fs.realpath-1.0.0.tgz", "integrity": "sha1-FQStJSMVjKpA20onh8sBQRmU6k8=", "dev": true, "license": "ISC"}, "node_modules/fsevents": {"version": "2.3.3", "resolved": "http://r.npm.sankuai.com/fsevents/download/fsevents-2.3.3.tgz", "integrity": "sha1-ysZAd4XQNnWipeGlMFxpezR9kNY=", "dev": true, "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": "^8.16.0 || ^10.6.0 || >=11.0.0"}}, "node_modules/function-bind": {"version": "1.1.2", "resolved": "http://r.npm.sankuai.com/function-bind/download/function-bind-1.1.2.tgz", "integrity": "sha1-LALYZNl/PqbIgwxGTL0Rq26rehw=", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/functional-red-black-tree": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/functional-red-black-tree/download/functional-red-black-tree-1.0.1.tgz", "integrity": "sha1-GwqzvVU7Kg1jmdKcDj6gslIHgyc=", "dev": true, "license": "MIT"}, "node_modules/gensync": {"version": "1.0.0-beta.2", "resolved": "http://r.npm.sankuai.com/gensync/download/gensync-1.0.0-beta.2.tgz", "integrity": "sha1-MqbudsPX9S1GsrGuXZP+qFgKJeA=", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/get-caller-file": {"version": "2.0.5", "resolved": "http://r.npm.sankuai.com/get-caller-file/download/get-caller-file-2.0.5.tgz", "integrity": "sha1-T5RBKoLbMvNuOwuXQfipf+sDH34=", "dev": true, "license": "ISC", "engines": {"node": "6.* || 8.* || >= 10.*"}}, "node_modules/get-intrinsic": {"version": "1.3.0", "resolved": "http://r.npm.sankuai.com/get-intrinsic/download/get-intrinsic-1.3.0.tgz", "integrity": "sha1-dD8OO2lkqTpUke0b/6rgVNf5jQE=", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.2", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "function-bind": "^1.1.2", "get-proto": "^1.0.1", "gopd": "^1.2.0", "has-symbols": "^1.1.0", "hasown": "^2.0.2", "math-intrinsics": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-proto": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/get-proto/download/get-proto-1.0.1.tgz", "integrity": "sha1-FQs/J0OGnvPoUewMSdFbHRTQDuE=", "license": "MIT", "dependencies": {"dunder-proto": "^1.0.1", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/get-stream": {"version": "4.1.0", "resolved": "http://r.npm.sankuai.com/get-stream/download/get-stream-4.1.0.tgz", "integrity": "sha1-wbJVV189wh1Zv8ec09K0axw6VLU=", "dev": true, "license": "MIT", "dependencies": {"pump": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/glob": {"version": "7.2.3", "resolved": "http://r.npm.sankuai.com/glob/download/glob-7.2.3.tgz", "integrity": "sha1-uN8PuAK7+o6JvR2Ti04WV47UTys=", "dev": true, "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.1.1", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/glob-parent": {"version": "5.1.2", "resolved": "http://r.npm.sankuai.com/glob-parent/download/glob-parent-5.1.2.tgz", "integrity": "sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=", "dev": true, "license": "ISC", "dependencies": {"is-glob": "^4.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/glob-to-regexp": {"version": "0.4.1", "resolved": "http://r.npm.sankuai.com/glob-to-regexp/download/glob-to-regexp-0.4.1.tgz", "integrity": "sha1-x1KXCHyFG5pXi9IX3VmpL1n+VG4=", "dev": true, "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/globals": {"version": "11.12.0", "resolved": "http://r.npm.sankuai.com/globals/download/globals-11.12.0.tgz", "integrity": "sha1-q4eVM4hooLq9hSV1gBjCp+uVxC4=", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/globby": {"version": "11.1.0", "resolved": "http://r.npm.sankuai.com/globby/download/globby-11.1.0.tgz", "integrity": "sha1-vUvpi7BC+D15b344EZkfvoKg00s=", "dev": true, "license": "MIT", "dependencies": {"array-union": "^2.1.0", "dir-glob": "^3.0.1", "fast-glob": "^3.2.9", "ignore": "^5.2.0", "merge2": "^1.4.1", "slash": "^3.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/globby/node_modules/ignore": {"version": "5.3.2", "resolved": "http://r.npm.sankuai.com/ignore/download/ignore-5.3.2.tgz", "integrity": "sha1-PNQOcp82Q/2HywTlC/DrcivFlvU=", "dev": true, "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/gopd": {"version": "1.2.0", "resolved": "http://r.npm.sankuai.com/gopd/download/gopd-1.2.0.tgz", "integrity": "sha1-ifVrghe9vIgCvSmd9tfxCB1+UaE=", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/graceful-fs": {"version": "4.2.11", "resolved": "http://r.npm.sankuai.com/graceful-fs/download/graceful-fs-4.2.11.tgz", "integrity": "sha1-QYPk6L8Iu24Fu7L30uDI9xLKQOM=", "dev": true, "license": "ISC"}, "node_modules/gzip-size": {"version": "6.0.0", "resolved": "http://r.npm.sankuai.com/gzip-size/download/gzip-size-6.0.0.tgz", "integrity": "sha1-BlNn/VDCOcBnHLy61b4+LusQ5GI=", "dev": true, "license": "MIT", "dependencies": {"duplexer": "^0.1.2"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/handle-thing": {"version": "2.0.1", "resolved": "http://r.npm.sankuai.com/handle-thing/download/handle-thing-2.0.1.tgz", "integrity": "sha1-hX95zjWVgMNA1DCBzGSJcNC7I04=", "dev": true, "license": "MIT"}, "node_modules/has-flag": {"version": "4.0.0", "resolved": "http://r.npm.sankuai.com/has-flag/download/has-flag-4.0.0.tgz", "integrity": "sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/has-property-descriptors": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/has-property-descriptors/download/has-property-descriptors-1.0.2.tgz", "integrity": "sha1-lj7X0HHce/XwhMW/vg0bYiJYaFQ=", "dev": true, "license": "MIT", "dependencies": {"es-define-property": "^1.0.0"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-symbols": {"version": "1.1.0", "resolved": "http://r.npm.sankuai.com/has-symbols/download/has-symbols-1.1.0.tgz", "integrity": "sha1-/JxqeDoISVHQuXH+EBjegTcHozg=", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-tostringtag": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/has-tostringtag/download/has-tostringtag-1.0.2.tgz", "integrity": "sha1-LNxC1AvvLltO6rfAGnPFTOerWrw=", "license": "MIT", "dependencies": {"has-symbols": "^1.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/hash-base": {"version": "3.0.5", "resolved": "http://r.npm.sankuai.com/hash-base/download/hash-base-3.0.5.tgz", "integrity": "sha1-UkgOKFOVz3+6F9xMnkes3H8kioo=", "dev": true, "license": "MIT", "dependencies": {"inherits": "^2.0.4", "safe-buffer": "^5.2.1"}, "engines": {"node": ">= 0.10"}}, "node_modules/hash-sum": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/hash-sum/download/hash-sum-1.0.2.tgz", "integrity": "sha1-M7QHd3VMZDJXPBIMw4CLvRDUfwQ=", "dev": true, "license": "MIT"}, "node_modules/hash.js": {"version": "1.1.7", "resolved": "http://r.npm.sankuai.com/hash.js/download/hash.js-1.1.7.tgz", "integrity": "sha1-C6vKU46NTuSg+JiNaIZlN6ADz0I=", "dev": true, "license": "MIT", "dependencies": {"inherits": "^2.0.3", "minimalistic-assert": "^1.0.1"}}, "node_modules/hasown": {"version": "2.0.2", "resolved": "http://r.npm.sankuai.com/hasown/download/hasown-2.0.2.tgz", "integrity": "sha1-AD6vkb563DcuhOxZ3DclLO24AAM=", "license": "MIT", "dependencies": {"function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/he": {"version": "1.2.0", "resolved": "http://r.npm.sankuai.com/he/download/he-1.2.0.tgz", "integrity": "sha1-hK5l+n6vsWX922FWauFLrwVmTw8=", "dev": true, "license": "MIT", "bin": {"he": "bin/he"}}, "node_modules/highlight.js": {"version": "10.7.3", "resolved": "http://r.npm.sankuai.com/highlight.js/download/highlight.js-10.7.3.tgz", "integrity": "sha1-aXJy45kTVuQMPKxWanTu9oF1ZTE=", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": "*"}}, "node_modules/hmac-drbg": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/hmac-drbg/download/hmac-drbg-1.0.1.tgz", "integrity": "sha1-0nRXAQJabHdabFRXk+1QL8DGSaE=", "dev": true, "license": "MIT", "dependencies": {"hash.js": "^1.0.3", "minimalistic-assert": "^1.0.0", "minimalistic-crypto-utils": "^1.0.1"}}, "node_modules/hosted-git-info": {"version": "2.8.9", "resolved": "http://r.npm.sankuai.com/hosted-git-info/download/hosted-git-info-2.8.9.tgz", "integrity": "sha1-3/wL+aIcAiCQkPKqaUKeFBTa8/k=", "dev": true, "license": "ISC"}, "node_modules/hpack.js": {"version": "2.1.6", "resolved": "http://r.npm.sankuai.com/hpack.js/download/hpack.js-2.1.6.tgz", "integrity": "sha1-h3dMCUnlE/QuhFdbPEVoH63ioLI=", "dev": true, "license": "MIT", "dependencies": {"inherits": "^2.0.1", "obuf": "^1.0.0", "readable-stream": "^2.0.1", "wbuf": "^1.1.0"}}, "node_modules/hpack.js/node_modules/readable-stream": {"version": "2.3.8", "resolved": "http://r.npm.sankuai.com/readable-stream/download/readable-stream-2.3.8.tgz", "integrity": "sha1-kRJegEK7obmIf0k0X2J3Anzovps=", "dev": true, "license": "MIT", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "node_modules/hpack.js/node_modules/safe-buffer": {"version": "5.1.2", "resolved": "http://r.npm.sankuai.com/safe-buffer/download/safe-buffer-5.1.2.tgz", "integrity": "sha1-mR7GnSluAxN0fVm9/St0XDX4go0=", "dev": true, "license": "MIT"}, "node_modules/hpack.js/node_modules/string_decoder": {"version": "1.1.1", "resolved": "http://r.npm.sankuai.com/string_decoder/download/string_decoder-1.1.1.tgz", "integrity": "sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=", "dev": true, "license": "MIT", "dependencies": {"safe-buffer": "~5.1.0"}}, "node_modules/html-entities": {"version": "2.6.0", "resolved": "http://r.npm.sankuai.com/html-entities/download/html-entities-2.6.0.tgz", "integrity": "sha1-fGTx6js2gYzK49P7SLaXQgjphPg=", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/mdevils"}, {"type": "patreon", "url": "https://patreon.com/mdevils"}], "license": "MIT"}, "node_modules/html-escaper": {"version": "2.0.2", "resolved": "http://r.npm.sankuai.com/html-escaper/download/html-escaper-2.0.2.tgz", "integrity": "sha1-39YAJ9o2o238viNiYsAKWCJoFFM=", "dev": true, "license": "MIT"}, "node_modules/html-minifier-terser": {"version": "6.1.0", "resolved": "http://r.npm.sankuai.com/html-minifier-terser/download/html-minifier-terser-6.1.0.tgz", "integrity": "sha1-v8gYk0zAeRj2s2afV3Ts39SPMqs=", "dev": true, "license": "MIT", "dependencies": {"camel-case": "^4.1.2", "clean-css": "^5.2.2", "commander": "^8.3.0", "he": "^1.2.0", "param-case": "^3.0.4", "relateurl": "^0.2.7", "terser": "^5.10.0"}, "bin": {"html-minifier-terser": "cli.js"}, "engines": {"node": ">=12"}}, "node_modules/html-minifier-terser/node_modules/commander": {"version": "8.3.0", "resolved": "http://r.npm.sankuai.com/commander/download/commander-8.3.0.tgz", "integrity": "sha1-SDfqGy2me5xhamevuw+v7lZ7ymY=", "dev": true, "license": "MIT", "engines": {"node": ">= 12"}}, "node_modules/html-tags": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/html-tags/download/html-tags-2.0.0.tgz", "integrity": "sha1-ELMKOGCF9Dzt41PMj6fLDe7qZos=", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/html-webpack-plugin": {"version": "5.6.3", "resolved": "http://r.npm.sankuai.com/html-webpack-plugin/download/html-webpack-plugin-5.6.3.tgz", "integrity": "sha1-oxFF8P7kGE1Tp5T5UTFH3x5lNoU=", "dev": true, "license": "MIT", "dependencies": {"@types/html-minifier-terser": "^6.0.0", "html-minifier-terser": "^6.0.2", "lodash": "^4.17.21", "pretty-error": "^4.0.0", "tapable": "^2.0.0"}, "engines": {"node": ">=10.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/html-webpack-plugin"}, "peerDependencies": {"@rspack/core": "0.x || 1.x", "webpack": "^5.20.0"}, "peerDependenciesMeta": {"@rspack/core": {"optional": true}, "webpack": {"optional": true}}}, "node_modules/htmlparser2": {"version": "6.1.0", "resolved": "http://r.npm.sankuai.com/htmlparser2/download/htmlparser2-6.1.0.tgz", "integrity": "sha1-xNditsM3GgXb5l6UrkOp+EX7j7c=", "dev": true, "funding": ["https://github.com/fb55/htmlparser2?sponsor=1", {"type": "github", "url": "https://github.com/sponsors/fb55"}], "license": "MIT", "dependencies": {"domelementtype": "^2.0.1", "domhandler": "^4.0.0", "domutils": "^2.5.2", "entities": "^2.0.0"}}, "node_modules/http-deceiver": {"version": "1.2.7", "resolved": "http://r.npm.sankuai.com/http-deceiver/download/http-deceiver-1.2.7.tgz", "integrity": "sha1-+nFolEq5pRnTN8sL7HKE3D5yPYc=", "dev": true, "license": "MIT"}, "node_modules/http-errors": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/http-errors/download/http-errors-2.0.0.tgz", "integrity": "sha1-t3dKFIbvc892Z6ya4IWMASxXudM=", "dev": true, "license": "MIT", "dependencies": {"depd": "2.0.0", "inherits": "2.0.4", "setprototypeof": "1.2.0", "statuses": "2.0.1", "toidentifier": "1.0.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/http-parser-js": {"version": "0.5.10", "resolved": "http://r.npm.sankuai.com/http-parser-js/download/http-parser-js-0.5.10.tgz", "integrity": "sha1-syd71tftVYjiDqc79yT8vkRgkHU=", "dev": true, "license": "MIT"}, "node_modules/http-proxy": {"version": "1.18.1", "resolved": "http://r.npm.sankuai.com/http-proxy/download/http-proxy-1.18.1.tgz", "integrity": "sha1-QBVB8FNIhLv5UmAzTnL4juOXZUk=", "dev": true, "license": "MIT", "dependencies": {"eventemitter3": "^4.0.0", "follow-redirects": "^1.0.0", "requires-port": "^1.0.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/http-proxy-middleware": {"version": "2.0.9", "resolved": "http://r.npm.sankuai.com/http-proxy-middleware/download/http-proxy-middleware-2.0.9.tgz", "integrity": "sha1-6eY9aK+qTu49FH85FJq4TAwoFe8=", "dev": true, "license": "MIT", "dependencies": {"@types/http-proxy": "^1.17.8", "http-proxy": "^1.18.1", "is-glob": "^4.0.1", "is-plain-obj": "^3.0.0", "micromatch": "^4.0.2"}, "engines": {"node": ">=12.0.0"}, "peerDependencies": {"@types/express": "^4.17.13"}, "peerDependenciesMeta": {"@types/express": {"optional": true}}}, "node_modules/human-signals": {"version": "2.1.0", "resolved": "http://r.npm.sankuai.com/human-signals/download/human-signals-2.1.0.tgz", "integrity": "sha1-3JH8ukLk0G5Kuu0zs+ejwC9RTqA=", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=10.17.0"}}, "node_modules/humanize-ms": {"version": "1.2.1", "resolved": "http://r.npm.sankuai.com/humanize-ms/download/humanize-ms-1.2.1.tgz", "integrity": "sha1-xG4xWaKT9riW2ikxbYtv6Lt5u+0=", "license": "MIT", "dependencies": {"ms": "^2.0.0"}}, "node_modules/iconv-lite": {"version": "0.4.24", "resolved": "http://r.npm.sankuai.com/iconv-lite/download/iconv-lite-0.4.24.tgz", "integrity": "sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=", "dev": true, "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3"}, "engines": {"node": ">=0.10.0"}}, "node_modules/icss-utils": {"version": "5.1.0", "resolved": "http://r.npm.sankuai.com/icss-utils/download/icss-utils-5.1.0.tgz", "integrity": "sha1-xr5oWKvQE9do6YNmrkfiXViHsa4=", "dev": true, "license": "ISC", "engines": {"node": "^10 || ^12 || >= 14"}, "peerDependencies": {"postcss": "^8.1.0"}}, "node_modules/idb-keyval": {"version": "6.2.2", "resolved": "http://r.npm.sankuai.com/idb-keyval/download/idb-keyval-6.2.2.tgz", "integrity": "sha1-sBcbX3OUSFSjKRpc26jhJ2jEhUo=", "license": "Apache-2.0"}, "node_modules/ieee754": {"version": "1.2.1", "resolved": "http://r.npm.sankuai.com/ieee754/download/ieee754-1.2.1.tgz", "integrity": "sha1-jrehCmP/8l0VpXsAFYbRd9Gw01I=", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/ignore": {"version": "4.0.6", "resolved": "http://r.npm.sankuai.com/ignore/download/ignore-4.0.6.tgz", "integrity": "sha1-dQ49tYYgh7RzfrrIIH/9HvJ7Jfw=", "dev": true, "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/import-fresh": {"version": "3.3.1", "resolved": "http://r.npm.sankuai.com/import-fresh/download/import-fresh-3.3.1.tgz", "integrity": "sha1-nOy1ZQPAraHydB271lRuSxO1fM8=", "dev": true, "license": "MIT", "dependencies": {"parent-module": "^1.0.0", "resolve-from": "^4.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/imurmurhash": {"version": "0.1.4", "resolved": "http://r.npm.sankuai.com/imurmurhash/download/imurmurhash-0.1.4.tgz", "integrity": "sha1-khi5srkoojixPcT7a21XbyMUU+o=", "dev": true, "license": "MIT", "engines": {"node": ">=0.8.19"}}, "node_modules/inflight": {"version": "1.0.6", "resolved": "http://r.npm.sankuai.com/inflight/download/inflight-1.0.6.tgz", "integrity": "sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=", "dev": true, "license": "ISC", "dependencies": {"once": "^1.3.0", "wrappy": "1"}}, "node_modules/inherits": {"version": "2.0.4", "resolved": "http://r.npm.sankuai.com/inherits/download/inherits-2.0.4.tgz", "integrity": "sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=", "dev": true, "license": "ISC"}, "node_modules/ipaddr.js": {"version": "2.2.0", "resolved": "http://r.npm.sankuai.com/ipaddr.js/download/ipaddr.js-2.2.0.tgz", "integrity": "sha1-0z+nusKE9N56+UljjJ1oFXxrkug=", "dev": true, "license": "MIT", "engines": {"node": ">= 10"}}, "node_modules/is-arguments": {"version": "1.2.0", "resolved": "http://r.npm.sankuai.com/is-arguments/download/is-arguments-1.2.0.tgz", "integrity": "sha1-rVjGrs9WO3jvK/BN9UDaj119jhs=", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "has-tostringtag": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-arrayish": {"version": "0.2.1", "resolved": "http://r.npm.sankuai.com/is-arrayish/download/is-arrayish-0.2.1.tgz", "integrity": "sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=", "dev": true, "license": "MIT"}, "node_modules/is-binary-path": {"version": "2.1.0", "resolved": "http://r.npm.sankuai.com/is-binary-path/download/is-binary-path-2.1.0.tgz", "integrity": "sha1-6h9/O4DwZCNug0cPhsCcJU+0Wwk=", "dev": true, "license": "MIT", "dependencies": {"binary-extensions": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/is-callable": {"version": "1.2.7", "resolved": "http://r.npm.sankuai.com/is-callable/download/is-callable-1.2.7.tgz", "integrity": "sha1-O8KoXqdC2eNiBdys3XLKH9xRsFU=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-ci": {"version": "1.2.1", "resolved": "http://r.npm.sankuai.com/is-ci/download/is-ci-1.2.1.tgz", "integrity": "sha1-43ecjuF/zPQoSI9uKBGH8uYyhBw=", "dev": true, "license": "MIT", "dependencies": {"ci-info": "^1.5.0"}, "bin": {"is-ci": "bin.js"}}, "node_modules/is-core-module": {"version": "2.16.1", "resolved": "http://r.npm.sankuai.com/is-core-module/download/is-core-module-2.16.1.tgz", "integrity": "sha1-KpiAGoSfQ+Kt1kT7trxiKbGaTvQ=", "dev": true, "license": "MIT", "dependencies": {"hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-docker": {"version": "2.2.1", "resolved": "http://r.npm.sankuai.com/is-docker/download/is-docker-2.2.1.tgz", "integrity": "sha1-M+6r4jz+hvFL3kQIoCwM+4U6zao=", "dev": true, "license": "MIT", "bin": {"is-docker": "cli.js"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-extglob": {"version": "2.1.1", "resolved": "http://r.npm.sankuai.com/is-extglob/download/is-extglob-2.1.1.tgz", "integrity": "sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-file-esm": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/is-file-esm/download/is-file-esm-1.0.0.tgz", "integrity": "sha1-mHCGsPWlMYF56dMPTy+NNzIeG18=", "dev": true, "license": "MIT", "dependencies": {"read-pkg-up": "^7.0.1"}}, "node_modules/is-fullwidth-code-point": {"version": "3.0.0", "resolved": "http://r.npm.sankuai.com/is-fullwidth-code-point/download/is-fullwidth-code-point-3.0.0.tgz", "integrity": "sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/is-generator-function": {"version": "1.1.0", "resolved": "http://r.npm.sankuai.com/is-generator-function/download/is-generator-function-1.1.0.tgz", "integrity": "sha1-vz7tqTEgE5T1e126KAD5GiODCco=", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "get-proto": "^1.0.0", "has-tostringtag": "^1.0.2", "safe-regex-test": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-glob": {"version": "4.0.3", "resolved": "http://r.npm.sankuai.com/is-glob/download/is-glob-4.0.3.tgz", "integrity": "sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ=", "dev": true, "license": "MIT", "dependencies": {"is-extglob": "^2.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-interactive": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/is-interactive/download/is-interactive-1.0.0.tgz", "integrity": "sha1-zqbmrlyHCnsKAAQHC3tYfgJSkS4=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/is-number": {"version": "7.0.0", "resolved": "http://r.npm.sankuai.com/is-number/download/is-number-7.0.0.tgz", "integrity": "sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=", "dev": true, "license": "MIT", "engines": {"node": ">=0.12.0"}}, "node_modules/is-plain-obj": {"version": "3.0.0", "resolved": "http://r.npm.sankuai.com/is-plain-obj/download/is-plain-obj-3.0.0.tgz", "integrity": "sha1-r28uoUrFpkYYOlu9tbqrvBVq2dc=", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-plain-object": {"version": "2.0.4", "resolved": "http://r.npm.sankuai.com/is-plain-object/download/is-plain-object-2.0.4.tgz", "integrity": "sha1-LBY7P6+xtgbZ0Xko8FwqHDjgdnc=", "dev": true, "license": "MIT", "dependencies": {"isobject": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-regex": {"version": "1.2.1", "resolved": "http://r.npm.sankuai.com/is-regex/download/is-regex-1.2.1.tgz", "integrity": "sha1-dtcKPtEO+b5I61d4h9dCBb8MrSI=", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "gopd": "^1.2.0", "has-tostringtag": "^1.0.2", "hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-stream": {"version": "1.1.0", "resolved": "http://r.npm.sankuai.com/is-stream/download/is-stream-1.1.0.tgz", "integrity": "sha1-EtSj3U5o4Lec6428hBc66A2RykQ=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-typed-array": {"version": "1.1.15", "resolved": "http://r.npm.sankuai.com/is-typed-array/download/is-typed-array-1.1.15.tgz", "integrity": "sha1-S/tKRbYc7oOlpG+6d45OjVnAzgs=", "dev": true, "license": "MIT", "dependencies": {"which-typed-array": "^1.1.16"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-unicode-supported": {"version": "0.1.0", "resolved": "http://r.npm.sankuai.com/is-unicode-supported/download/is-unicode-supported-0.1.0.tgz", "integrity": "sha1-PybHaoCVk7Ur+i7LVxDtJ3m1Iqc=", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-url": {"version": "1.2.4", "resolved": "http://r.npm.sankuai.com/is-url/download/is-url-1.2.4.tgz", "integrity": "sha1-BKTfRtKMTP89c9Af8Gq+sxihqlI=", "license": "MIT"}, "node_modules/is-wsl": {"version": "2.2.0", "resolved": "http://r.npm.sankuai.com/is-wsl/download/is-wsl-2.2.0.tgz", "integrity": "sha1-dKTHbnfKn9P5MvKQwX6jJs0VcnE=", "dev": true, "license": "MIT", "dependencies": {"is-docker": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/isarray": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/isarray/download/isarray-1.0.0.tgz", "integrity": "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=", "dev": true, "license": "MIT"}, "node_modules/isexe": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/isexe/download/isexe-2.0.0.tgz", "integrity": "sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=", "dev": true, "license": "ISC"}, "node_modules/isobject": {"version": "3.0.1", "resolved": "http://r.npm.sankuai.com/isobject/download/isobject-3.0.1.tgz", "integrity": "sha1-TkMekrEalzFjaqH5yNHMvP2reN8=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/javascript-stringify": {"version": "2.1.0", "resolved": "http://r.npm.sankuai.com/javascript-stringify/download/javascript-stringify-2.1.0.tgz", "integrity": "sha1-J8dlOb4U2L0Sghmi1zGwkzeQTnk=", "dev": true, "license": "MIT"}, "node_modules/jest-worker": {"version": "27.5.1", "resolved": "http://r.npm.sankuai.com/jest-worker/download/jest-worker-27.5.1.tgz", "integrity": "sha1-jRRvCQDolzsQa29zzB6ajLhvjbA=", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "engines": {"node": ">= 10.13.0"}}, "node_modules/jest-worker/node_modules/supports-color": {"version": "8.1.1", "resolved": "http://r.npm.sankuai.com/supports-color/download/supports-color-8.1.1.tgz", "integrity": "sha1-zW/BfihQDP9WwbhsCn/UpUpzAFw=", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/supports-color?sponsor=1"}}, "node_modules/joi": {"version": "17.13.3", "resolved": "http://r.npm.sankuai.com/joi/download/joi-17.13.3.tgz", "integrity": "sha1-D1zBFpyZmzDTRDZtOEsS2SVYvOw=", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@hapi/hoek": "^9.3.0", "@hapi/topo": "^5.1.0", "@sideway/address": "^4.1.5", "@sideway/formula": "^3.0.1", "@sideway/pinpoint": "^2.0.0"}}, "node_modules/js-message": {"version": "1.0.7", "resolved": "http://r.npm.sankuai.com/js-message/download/js-message-1.0.7.tgz", "integrity": "sha1-+93QU8ekcCGHG7iyyVOXzBfCDkc=", "dev": true, "license": "MIT", "engines": {"node": ">=0.6.0"}}, "node_modules/js-tokens": {"version": "4.0.0", "resolved": "http://r.npm.sankuai.com/js-tokens/download/js-tokens-4.0.0.tgz", "integrity": "sha1-GSA/tZmR35jjoocFDUZHzerzJJk=", "dev": true, "license": "MIT"}, "node_modules/js-yaml": {"version": "3.14.1", "resolved": "http://r.npm.sankuai.com/js-yaml/download/js-yaml-3.14.1.tgz", "integrity": "sha1-2ugS/bOCX6MGYJqHFzg8UMNqBTc=", "dev": true, "license": "MIT", "dependencies": {"argparse": "^1.0.7", "esprima": "^4.0.0"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/jsesc": {"version": "3.1.0", "resolved": "http://r.npm.sankuai.com/jsesc/download/jsesc-3.1.0.tgz", "integrity": "sha1-dNM1ojT2ftGZB/2t+sfM+dQJgl0=", "dev": true, "license": "MIT", "bin": {"jsesc": "bin/jsesc"}, "engines": {"node": ">=6"}}, "node_modules/json-buffer": {"version": "3.0.1", "resolved": "http://r.npm.sankuai.com/json-buffer/download/json-buffer-3.0.1.tgz", "integrity": "sha1-kziAKjDTtmBfvgYT4JQAjKjAWhM=", "dev": true, "license": "MIT"}, "node_modules/json-parse-better-errors": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/json-parse-better-errors/download/json-parse-better-errors-1.0.2.tgz", "integrity": "sha1-u4Z8+zRQ5pEHwTHRxRS6s9yLyqk=", "dev": true, "license": "MIT"}, "node_modules/json-parse-even-better-errors": {"version": "2.3.1", "resolved": "http://r.npm.sankuai.com/json-parse-even-better-errors/download/json-parse-even-better-errors-2.3.1.tgz", "integrity": "sha1-fEeAWpQxmSjgV3dAXcEuH3pO4C0=", "dev": true, "license": "MIT"}, "node_modules/json-schema-traverse": {"version": "0.4.1", "resolved": "http://r.npm.sankuai.com/json-schema-traverse/download/json-schema-traverse-0.4.1.tgz", "integrity": "sha1-afaofZUTq4u4/mO9sJecRI5oRmA=", "dev": true, "license": "MIT"}, "node_modules/json-stable-stringify-without-jsonify": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/json-stable-stringify-without-jsonify/download/json-stable-stringify-without-jsonify-1.0.1.tgz", "integrity": "sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=", "dev": true, "license": "MIT"}, "node_modules/json5": {"version": "2.2.3", "resolved": "http://r.npm.sankuai.com/json5/download/json5-2.2.3.tgz", "integrity": "sha1-eM1vGhm9wStz21rQxh79ZsHikoM=", "dev": true, "license": "MIT", "bin": {"json5": "lib/cli.js"}, "engines": {"node": ">=6"}}, "node_modules/jsonfile": {"version": "6.1.0", "resolved": "http://r.npm.sankuai.com/jsonfile/download/jsonfile-6.1.0.tgz", "integrity": "sha1-vFWyY0eTxnnsZAMJTrE2mKbsCq4=", "dev": true, "license": "MIT", "dependencies": {"universalify": "^2.0.0"}, "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/keyv": {"version": "4.5.4", "resolved": "http://r.npm.sankuai.com/keyv/download/keyv-4.5.4.tgz", "integrity": "sha1-qHmpnilFL5QkOfKkBeOvizHU3pM=", "dev": true, "license": "MIT", "dependencies": {"json-buffer": "3.0.1"}}, "node_modules/kind-of": {"version": "6.0.3", "resolved": "http://r.npm.sankuai.com/kind-of/download/kind-of-6.0.3.tgz", "integrity": "sha1-B8BQNKbDSfoG4k+jWqdttFgM5N0=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/klona": {"version": "2.0.6", "resolved": "http://r.npm.sankuai.com/klona/download/klona-2.0.6.tgz", "integrity": "sha1-hb/7+BnAOy9TJwQSQgpFVe+ILiI=", "dev": true, "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/launch-editor": {"version": "2.10.0", "resolved": "http://r.npm.sankuai.com/launch-editor/download/launch-editor-2.10.0.tgz", "integrity": "sha1-XKPt/LlmffHochMQ86QPESfUvEI=", "dev": true, "license": "MIT", "dependencies": {"picocolors": "^1.0.0", "shell-quote": "^1.8.1"}}, "node_modules/launch-editor-middleware": {"version": "2.10.0", "resolved": "http://r.npm.sankuai.com/launch-editor-middleware/download/launch-editor-middleware-2.10.0.tgz", "integrity": "sha1-dNkJUWDwvLRFVqCG6d0W5n1GT/Q=", "dev": true, "license": "MIT", "dependencies": {"launch-editor": "^2.10.0"}}, "node_modules/levn": {"version": "0.4.1", "resolved": "http://r.npm.sankuai.com/levn/download/levn-0.4.1.tgz", "integrity": "sha1-rkViwAdHO5MqYgDUAyaN0v/8at4=", "dev": true, "license": "MIT", "dependencies": {"prelude-ls": "^1.2.1", "type-check": "~0.4.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/lilconfig": {"version": "2.1.0", "resolved": "http://r.npm.sankuai.com/lilconfig/download/lilconfig-2.1.0.tgz", "integrity": "sha1-eOI6yJ67fhv78lsYBD3nVlSOf1I=", "dev": true, "license": "MIT", "engines": {"node": ">=10"}}, "node_modules/lines-and-columns": {"version": "1.2.4", "resolved": "http://r.npm.sankuai.com/lines-and-columns/download/lines-and-columns-1.2.4.tgz", "integrity": "sha1-7KKE910pZQeTCdwK2SVauy68FjI=", "dev": true, "license": "MIT"}, "node_modules/loader-runner": {"version": "4.3.0", "resolved": "http://r.npm.sankuai.com/loader-runner/download/loader-runner-4.3.0.tgz", "integrity": "sha1-wbShY7mfYUgwNTsWdV5xSawjFOE=", "dev": true, "license": "MIT", "engines": {"node": ">=6.11.5"}}, "node_modules/loader-utils": {"version": "1.4.2", "resolved": "http://r.npm.sankuai.com/loader-utils/download/loader-utils-1.4.2.tgz", "integrity": "sha1-KalX86Y5c4g+toTxD/09FR/sAaM=", "dev": true, "license": "MIT", "dependencies": {"big.js": "^5.2.2", "emojis-list": "^3.0.0", "json5": "^1.0.1"}, "engines": {"node": ">=4.0.0"}}, "node_modules/loader-utils/node_modules/json5": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/json5/download/json5-1.0.2.tgz", "integrity": "sha1-Y9mNYPIbMTt3xNbaGL+mnYDh1ZM=", "dev": true, "license": "MIT", "dependencies": {"minimist": "^1.2.0"}, "bin": {"json5": "lib/cli.js"}}, "node_modules/locate-path": {"version": "5.0.0", "resolved": "http://r.npm.sankuai.com/locate-path/download/locate-path-5.0.0.tgz", "integrity": "sha1-Gvujlq/WdqbUJQTQpno6frn2KqA=", "dev": true, "license": "MIT", "dependencies": {"p-locate": "^4.1.0"}, "engines": {"node": ">=8"}}, "node_modules/lodash": {"version": "4.17.21", "resolved": "http://r.npm.sankuai.com/lodash/download/lodash-4.17.21.tgz", "integrity": "sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw=", "dev": true, "license": "MIT"}, "node_modules/lodash.debounce": {"version": "4.0.8", "resolved": "http://r.npm.sankuai.com/lodash.debounce/download/lodash.debounce-4.0.8.tgz", "integrity": "sha1-gteb/zCmfEAF/9XiUVMArZyk168=", "dev": true, "license": "MIT"}, "node_modules/lodash.defaultsdeep": {"version": "4.6.1", "resolved": "http://r.npm.sankuai.com/lodash.defaultsdeep/download/lodash.defaultsdeep-4.6.1.tgz", "integrity": "sha1-US6b1yHSctlOPTpjZT+hdRZ0HKY=", "dev": true, "license": "MIT"}, "node_modules/lodash.kebabcase": {"version": "4.1.1", "resolved": "http://r.npm.sankuai.com/lodash.kebabcase/download/lodash.kebabcase-4.1.1.tgz", "integrity": "sha1-hImxyw0p/4gZXM7KRI/21swpXDY=", "dev": true, "license": "MIT"}, "node_modules/lodash.mapvalues": {"version": "4.6.0", "resolved": "http://r.npm.sankuai.com/lodash.mapvalues/download/lodash.mapvalues-4.6.0.tgz", "integrity": "sha1-G6+lAF3p3W9PJmaMMMo3IwzJaJw=", "dev": true, "license": "MIT"}, "node_modules/lodash.memoize": {"version": "4.1.2", "resolved": "http://r.npm.sankuai.com/lodash.memoize/download/lodash.memoize-4.1.2.tgz", "integrity": "sha1-vMbEmkKihA7Zl/Mj6tpezRguC/4=", "dev": true, "license": "MIT"}, "node_modules/lodash.merge": {"version": "4.6.2", "resolved": "http://r.npm.sankuai.com/lodash.merge/download/lodash.merge-4.6.2.tgz", "integrity": "sha1-VYqlO0O2YeGSWgr9+japoQhf5Xo=", "dev": true, "license": "MIT"}, "node_modules/lodash.truncate": {"version": "4.4.2", "resolved": "http://r.npm.sankuai.com/lodash.truncate/download/lodash.truncate-4.4.2.tgz", "integrity": "sha1-WjUNoLERO4N+z//VgSy+WNbq4ZM=", "dev": true, "license": "MIT"}, "node_modules/lodash.uniq": {"version": "4.5.0", "resolved": "http://r.npm.sankuai.com/lodash.uniq/download/lodash.uniq-4.5.0.tgz", "integrity": "sha1-0CJTc662Uq3BvILklFM5qEJ1R3M=", "dev": true, "license": "MIT"}, "node_modules/log-symbols": {"version": "4.1.0", "resolved": "http://r.npm.sankuai.com/log-symbols/download/log-symbols-4.1.0.tgz", "integrity": "sha1-P727lbRoOsn8eFER55LlWNSr1QM=", "dev": true, "license": "MIT", "dependencies": {"chalk": "^4.1.0", "is-unicode-supported": "^0.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/log-update": {"version": "2.3.0", "resolved": "http://r.npm.sankuai.com/log-update/download/log-update-2.3.0.tgz", "integrity": "sha1-iDKP19HOeTiykoN0bwsbwSayRwg=", "dev": true, "license": "MIT", "dependencies": {"ansi-escapes": "^3.0.0", "cli-cursor": "^2.0.0", "wrap-ansi": "^3.0.1"}, "engines": {"node": ">=4"}}, "node_modules/log-update/node_modules/ansi-regex": {"version": "3.0.1", "resolved": "http://r.npm.sankuai.com/ansi-regex/download/ansi-regex-3.0.1.tgz", "integrity": "sha1-Ej1keekq1FrYl9QFTjx8p9tJROE=", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/log-update/node_modules/is-fullwidth-code-point": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/is-fullwidth-code-point/download/is-fullwidth-code-point-2.0.0.tgz", "integrity": "sha1-o7MKXE8ZkYMWeqq5O+764937ZU8=", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/log-update/node_modules/string-width": {"version": "2.1.1", "resolved": "http://r.npm.sankuai.com/string-width/download/string-width-2.1.1.tgz", "integrity": "sha1-q5Pyeo3BPSjKyBXEYhQ6bZASrp4=", "dev": true, "license": "MIT", "dependencies": {"is-fullwidth-code-point": "^2.0.0", "strip-ansi": "^4.0.0"}, "engines": {"node": ">=4"}}, "node_modules/log-update/node_modules/strip-ansi": {"version": "4.0.0", "resolved": "http://r.npm.sankuai.com/strip-ansi/download/strip-ansi-4.0.0.tgz", "integrity": "sha1-qEeQIusaw2iocTibY1JixQXuNo8=", "dev": true, "license": "MIT", "dependencies": {"ansi-regex": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/log-update/node_modules/wrap-ansi": {"version": "3.0.1", "resolved": "http://r.npm.sankuai.com/wrap-ansi/download/wrap-ansi-3.0.1.tgz", "integrity": "sha1-KIoE2H7aXChuBg3+jxNc6NAH+Lo=", "dev": true, "license": "MIT", "dependencies": {"string-width": "^2.1.1", "strip-ansi": "^4.0.0"}, "engines": {"node": ">=4"}}, "node_modules/lower-case": {"version": "2.0.2", "resolved": "http://r.npm.sankuai.com/lower-case/download/lower-case-2.0.2.tgz", "integrity": "sha1-b6I3xj29xKgsoP2ILkci3F5jTig=", "dev": true, "license": "MIT", "dependencies": {"tslib": "^2.0.3"}}, "node_modules/lru-cache": {"version": "4.1.5", "resolved": "http://r.npm.sankuai.com/lru-cache/download/lru-cache-4.1.5.tgz", "integrity": "sha1-i75Q6oW+1ZvJ4z3KuCNe6bz0Q80=", "dev": true, "license": "ISC", "dependencies": {"pseudomap": "^1.0.2", "yallist": "^2.1.2"}}, "node_modules/lru-cache/node_modules/yallist": {"version": "2.1.2", "resolved": "http://r.npm.sankuai.com/yallist/download/yallist-2.1.2.tgz", "integrity": "sha1-HBH5IY8HYImkfdUS+TxmmaaoHVI=", "dev": true, "license": "ISC"}, "node_modules/magic-string": {"version": "0.30.17", "resolved": "http://r.npm.sankuai.com/magic-string/download/magic-string-0.30.17.tgz", "integrity": "sha1-RQpElnPSRg5bvPupphkWoXFMdFM=", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/sourcemap-codec": "^1.5.0"}}, "node_modules/make-dir": {"version": "3.1.0", "resolved": "http://r.npm.sankuai.com/make-dir/download/make-dir-3.1.0.tgz", "integrity": "sha1-QV6WcEazp/HRhSd9hKpYIDcmoT8=", "dev": true, "license": "MIT", "dependencies": {"semver": "^6.0.0"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/marked": {"version": "4.3.0", "resolved": "http://r.npm.sankuai.com/marked/download/marked-4.3.0.tgz", "integrity": "sha1-eWNighsBn3NAVFggOLEWSBtFbPM=", "license": "MIT", "bin": {"marked": "bin/marked.js"}, "engines": {"node": ">= 12"}}, "node_modules/math-intrinsics": {"version": "1.1.0", "resolved": "http://r.npm.sankuai.com/math-intrinsics/download/math-intrinsics-1.1.0.tgz", "integrity": "sha1-oN10voHiqlwvJ+Zc4oNgXuTit/k=", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/md5.js": {"version": "1.3.5", "resolved": "http://r.npm.sankuai.com/md5.js/download/md5.js-1.3.5.tgz", "integrity": "sha1-tdB7jjIW4+J81yjXL3DR5qNCAF8=", "dev": true, "license": "MIT", "dependencies": {"hash-base": "^3.0.0", "inherits": "^2.0.1", "safe-buffer": "^5.1.2"}}, "node_modules/md5.js/node_modules/hash-base": {"version": "3.1.0", "resolved": "http://r.npm.sankuai.com/hash-base/download/hash-base-3.1.0.tgz", "integrity": "sha1-VcOB2eBuHSmXqIO0o/3f5/DTrzM=", "dev": true, "license": "MIT", "dependencies": {"inherits": "^2.0.4", "readable-stream": "^3.6.0", "safe-buffer": "^5.2.0"}, "engines": {"node": ">=4"}}, "node_modules/mdn-data": {"version": "2.0.14", "resolved": "http://r.npm.sankuai.com/mdn-data/download/mdn-data-2.0.14.tgz", "integrity": "sha1-cRP8QoGRfWPOKbQ0RvcB5owlulA=", "dev": true, "license": "CC0-1.0"}, "node_modules/media-typer": {"version": "0.3.0", "resolved": "http://r.npm.sankuai.com/media-typer/download/media-typer-0.3.0.tgz", "integrity": "sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/memfs": {"version": "3.6.0", "resolved": "http://r.npm.sankuai.com/memfs/download/memfs-3.6.0.tgz", "integrity": "sha1-16IRD4b3ndlQqLbfbVe8mEqhhfY=", "dev": true, "license": "Unlicense", "dependencies": {"fs-monkey": "^1.0.4"}, "engines": {"node": ">= 4.0.0"}}, "node_modules/merge-descriptors": {"version": "1.0.3", "resolved": "http://r.npm.sankuai.com/merge-descriptors/download/merge-descriptors-1.0.3.tgz", "integrity": "sha1-2AMZpl88eTU1Hlz9rI+TGFBNvtU=", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/merge-source-map": {"version": "1.1.0", "resolved": "http://r.npm.sankuai.com/merge-source-map/download/merge-source-map-1.1.0.tgz", "integrity": "sha1-L93n5gIJOfcJBqaPLXrmheTIxkY=", "dev": true, "license": "MIT", "dependencies": {"source-map": "^0.6.1"}}, "node_modules/merge-stream": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/merge-stream/download/merge-stream-2.0.0.tgz", "integrity": "sha1-UoI2KaFN0AyXcPtq1H3GMQ8sH2A=", "dev": true, "license": "MIT"}, "node_modules/merge2": {"version": "1.4.1", "resolved": "http://r.npm.sankuai.com/merge2/download/merge2-1.4.1.tgz", "integrity": "sha1-Q2iJL4hekHRVpv19xVwMnUBJkK4=", "dev": true, "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/methods": {"version": "1.1.2", "resolved": "http://r.npm.sankuai.com/methods/download/methods-1.1.2.tgz", "integrity": "sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/micromatch": {"version": "4.0.8", "resolved": "http://r.npm.sankuai.com/micromatch/download/micromatch-4.0.8.tgz", "integrity": "sha1-1m+hjzpHB2eJMgubGvMr2G2fogI=", "dev": true, "license": "MIT", "dependencies": {"braces": "^3.0.3", "picomatch": "^2.3.1"}, "engines": {"node": ">=8.6"}}, "node_modules/miller-rabin": {"version": "4.0.1", "resolved": "http://r.npm.sankuai.com/miller-rabin/download/miller-rabin-4.0.1.tgz", "integrity": "sha1-8IA1HIZbDcViqEYpZtqlNUPHik0=", "dev": true, "license": "MIT", "dependencies": {"bn.js": "^4.0.0", "brorand": "^1.0.1"}, "bin": {"miller-rabin": "bin/miller-rabin"}}, "node_modules/mime": {"version": "1.6.0", "resolved": "http://r.npm.sankuai.com/mime/download/mime-1.6.0.tgz", "integrity": "sha1-Ms2eXGRVO9WNGaVor0Uqz/BJgbE=", "dev": true, "license": "MIT", "bin": {"mime": "cli.js"}, "engines": {"node": ">=4"}}, "node_modules/mime-db": {"version": "1.52.0", "resolved": "http://r.npm.sankuai.com/mime-db/download/mime-db-1.52.0.tgz", "integrity": "sha1-u6vNwChZ9JhzAchW4zh85exDv3A=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/mime-types": {"version": "2.1.35", "resolved": "http://r.npm.sankuai.com/mime-types/download/mime-types-2.1.35.tgz", "integrity": "sha1-OBqHG2KnNEUGYK497uRIE/cNlZo=", "license": "MIT", "dependencies": {"mime-db": "1.52.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/mimic-fn": {"version": "2.1.0", "resolved": "http://r.npm.sankuai.com/mimic-fn/download/mimic-fn-2.1.0.tgz", "integrity": "sha1-ftLCzMyvhNP/y3pptXcR/CCDQBs=", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/mini-css-extract-plugin": {"version": "2.9.2", "resolved": "http://r.npm.sankuai.com/mini-css-extract-plugin/download/mini-css-extract-plugin-2.9.2.tgz", "integrity": "sha1-lmAxtGiRelRG9MJKgIVLKUdQPFs=", "dev": true, "license": "MIT", "dependencies": {"schema-utils": "^4.0.0", "tapable": "^2.2.1"}, "engines": {"node": ">= 12.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"webpack": "^5.0.0"}}, "node_modules/minimalistic-assert": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/minimalistic-assert/download/minimalistic-assert-1.0.1.tgz", "integrity": "sha1-LhlN4ERibUoQ5/f7wAznPoPk1cc=", "dev": true, "license": "ISC"}, "node_modules/minimalistic-crypto-utils": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/minimalistic-crypto-utils/download/minimalistic-crypto-utils-1.0.1.tgz", "integrity": "sha1-9sAMHAsIIkblxNmd+4x8CDsrWCo=", "dev": true, "license": "MIT"}, "node_modules/minimatch": {"version": "3.1.2", "resolved": "http://r.npm.sankuai.com/minimatch/download/minimatch-3.1.2.tgz", "integrity": "sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/minimist": {"version": "1.2.8", "resolved": "http://r.npm.sankuai.com/minimist/download/minimist-1.2.8.tgz", "integrity": "sha1-waRk52kzAuCCoHXO4MBXdBrEdyw=", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/minipass": {"version": "3.3.6", "resolved": "http://r.npm.sankuai.com/minipass/download/minipass-3.3.6.tgz", "integrity": "sha1-e7o4TbOhUg0YycDlJRw0ROld2Uo=", "dev": true, "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/module-alias": {"version": "2.2.3", "resolved": "http://r.npm.sankuai.com/module-alias/download/module-alias-2.2.3.tgz", "integrity": "sha1-7C6Fxolzvaarcc58k7dj7JYFMiE=", "dev": true, "license": "MIT"}, "node_modules/mrmime": {"version": "2.0.1", "resolved": "http://r.npm.sankuai.com/mrmime/download/mrmime-2.0.1.tgz", "integrity": "sha1-vD6H95h4U6VMmFDusfEHjNRK3dw=", "dev": true, "license": "MIT", "engines": {"node": ">=10"}}, "node_modules/ms": {"version": "2.1.3", "resolved": "http://r.npm.sankuai.com/ms/download/ms-2.1.3.tgz", "integrity": "sha1-V0yBOM4dK1hh8LRFedut1gxmFbI=", "license": "MIT"}, "node_modules/multicast-dns": {"version": "7.2.5", "resolved": "http://r.npm.sankuai.com/multicast-dns/download/multicast-dns-7.2.5.tgz", "integrity": "sha1-d+tGBX9NetvRbZKQ+nKZ9vpkzO0=", "dev": true, "license": "MIT", "dependencies": {"dns-packet": "^5.2.2", "thunky": "^1.0.2"}, "bin": {"multicast-dns": "cli.js"}}, "node_modules/mz": {"version": "2.7.0", "resolved": "http://r.npm.sankuai.com/mz/download/mz-2.7.0.tgz", "integrity": "sha1-lQCAV6Vsr63CvGPd5/n/aVWUjjI=", "dev": true, "license": "MIT", "dependencies": {"any-promise": "^1.0.0", "object-assign": "^4.0.1", "thenify-all": "^1.0.0"}}, "node_modules/nanoid": {"version": "3.3.11", "resolved": "http://r.npm.sankuai.com/nanoid/download/nanoid-3.3.11.tgz", "integrity": "sha1-T08RLO++MDIC8hmYOBKJNiZtGFs=", "funding": [{"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "bin": {"nanoid": "bin/nanoid.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}}, "node_modules/natural-compare": {"version": "1.4.0", "resolved": "http://r.npm.sankuai.com/natural-compare/download/natural-compare-1.4.0.tgz", "integrity": "sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=", "dev": true, "license": "MIT"}, "node_modules/negotiator": {"version": "0.6.4", "resolved": "http://r.npm.sankuai.com/negotiator/download/negotiator-0.6.4.tgz", "integrity": "sha1-d3lI4kUmUcVwtxLdAcI+JicT//c=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/neo-async": {"version": "2.6.2", "resolved": "http://r.npm.sankuai.com/neo-async/download/neo-async-2.6.2.tgz", "integrity": "sha1-tKr7k+OustgXTKU88WOrfXMIMF8=", "dev": true, "license": "MIT"}, "node_modules/nice-try": {"version": "1.0.5", "resolved": "http://r.npm.sankuai.com/nice-try/download/nice-try-1.0.5.tgz", "integrity": "sha1-ozeKdpbOfSI+iPybdkvX7xCJ42Y=", "dev": true, "license": "MIT"}, "node_modules/no-case": {"version": "3.0.4", "resolved": "http://r.npm.sankuai.com/no-case/download/no-case-3.0.4.tgz", "integrity": "sha1-02H9XJgA9VhVGoNp/A3NRmK2Ek0=", "dev": true, "license": "MIT", "dependencies": {"lower-case": "^2.0.2", "tslib": "^2.0.3"}}, "node_modules/node-domexception": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/node-domexception/download/node-domexception-1.0.0.tgz", "integrity": "sha1-aIjbRqH3HAt2s/dVUBa2P+ZHZuU=", "funding": [{"type": "github", "url": "https://github.com/sponsors/jimmywarting"}, {"type": "github", "url": "https://paypal.me/jimmywarting"}], "license": "MIT", "engines": {"node": ">=10.5.0"}}, "node_modules/node-fetch": {"version": "2.7.0", "resolved": "http://r.npm.sankuai.com/node-fetch/download/node-fetch-2.7.0.tgz", "integrity": "sha1-0PD6bj4twdJ+/NitmdVQvalNGH0=", "license": "MIT", "dependencies": {"whatwg-url": "^5.0.0"}, "engines": {"node": "4.x || >=6.0.0"}, "peerDependencies": {"encoding": "^0.1.0"}, "peerDependenciesMeta": {"encoding": {"optional": true}}}, "node_modules/node-forge": {"version": "1.3.1", "resolved": "http://r.npm.sankuai.com/node-forge/download/node-forge-1.3.1.tgz", "integrity": "sha1-vo2iryQ7JBfV9kancGY6krfp3tM=", "dev": true, "license": "(BSD-3-<PERSON><PERSON> OR GPL-2.0)", "engines": {"node": ">= 6.13.0"}}, "node_modules/node-releases": {"version": "2.0.19", "resolved": "http://r.npm.sankuai.com/node-releases/download/node-releases-2.0.19.tgz", "integrity": "sha1-nkRaUpUJUexNF32EOvNwtBHK8xQ=", "dev": true, "license": "MIT"}, "node_modules/normalize-package-data": {"version": "2.5.0", "resolved": "http://r.npm.sankuai.com/normalize-package-data/download/normalize-package-data-2.5.0.tgz", "integrity": "sha1-5m2xg4sgDB38IzIl0SyzZSDiNKg=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"hosted-git-info": "^2.1.4", "resolve": "^1.10.0", "semver": "2 || 3 || 4 || 5", "validate-npm-package-license": "^3.0.1"}}, "node_modules/normalize-package-data/node_modules/semver": {"version": "5.7.2", "resolved": "http://r.npm.sankuai.com/semver/download/semver-5.7.2.tgz", "integrity": "sha1-SNVdtzfDKHzUg14X+hP+rOHEHvg=", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver"}}, "node_modules/normalize-path": {"version": "3.0.0", "resolved": "http://r.npm.sankuai.com/normalize-path/download/normalize-path-3.0.0.tgz", "integrity": "sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/normalize-range": {"version": "0.1.2", "resolved": "http://r.npm.sankuai.com/normalize-range/download/normalize-range-0.1.2.tgz", "integrity": "sha1-LRDAa9/TEuqXd2laTShDlFa3WUI=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/normalize-url": {"version": "6.1.0", "resolved": "http://r.npm.sankuai.com/normalize-url/download/normalize-url-6.1.0.tgz", "integrity": "sha1-QNCIW1Nd7/4/MUe+yHfQX+TFZoo=", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/normalize-wheel": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/normalize-wheel/download/normalize-wheel-1.0.1.tgz", "integrity": "sha1-rsiGr/2wRQcNhWRH32Ls+GFG7EU=", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/npm-run-path": {"version": "2.0.2", "resolved": "http://r.npm.sankuai.com/npm-run-path/download/npm-run-path-2.0.2.tgz", "integrity": "sha1-NakjLfo11wZ7TLLd8jV7GHFTbF8=", "dev": true, "license": "MIT", "dependencies": {"path-key": "^2.0.0"}, "engines": {"node": ">=4"}}, "node_modules/nth-check": {"version": "2.1.1", "resolved": "http://r.npm.sankuai.com/nth-check/download/nth-check-2.1.1.tgz", "integrity": "sha1-yeq0KO/842zWuSySS9sADvHx7R0=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"boolbase": "^1.0.0"}, "funding": {"url": "https://github.com/fb55/nth-check?sponsor=1"}}, "node_modules/object-assign": {"version": "4.1.1", "resolved": "http://r.npm.sankuai.com/object-assign/download/object-assign-4.1.1.tgz", "integrity": "sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/object-inspect": {"version": "1.13.4", "resolved": "http://r.npm.sankuai.com/object-inspect/download/object-inspect-1.13.4.tgz", "integrity": "sha1-g3UmXiG8IND6WCwi4bE0hdbgAhM=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/object-keys": {"version": "1.1.1", "resolved": "http://r.npm.sankuai.com/object-keys/download/object-keys-1.1.1.tgz", "integrity": "sha1-HEfyct8nfzsdrwYWd9nILiMixg4=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/object.assign": {"version": "4.1.7", "resolved": "http://r.npm.sankuai.com/object.assign/download/object.assign-4.1.7.tgz", "integrity": "sha1-jBTKGkJMalYbC7KiL2b1BJqUXT0=", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.3", "define-properties": "^1.2.1", "es-object-atoms": "^1.0.0", "has-symbols": "^1.1.0", "object-keys": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/obuf": {"version": "1.1.2", "resolved": "http://r.npm.sankuai.com/obuf/download/obuf-1.1.2.tgz", "integrity": "sha1-Cb6jND1BhZ69RGKS0RydTbYZCE4=", "dev": true, "license": "MIT"}, "node_modules/on-finished": {"version": "2.4.1", "resolved": "http://r.npm.sankuai.com/on-finished/download/on-finished-2.4.1.tgz", "integrity": "sha1-WMjEQRblSEWtV/FKsQsDUzGErD8=", "dev": true, "license": "MIT", "dependencies": {"ee-first": "1.1.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/on-headers": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/on-headers/download/on-headers-1.0.2.tgz", "integrity": "sha1-dysK5qqlJcOZ5Imt+tkMQD6zwo8=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/once": {"version": "1.4.0", "resolved": "http://r.npm.sankuai.com/once/download/once-1.4.0.tgz", "integrity": "sha1-WDsap3WWHUsROsF9nFC6753Xa9E=", "dev": true, "license": "ISC", "dependencies": {"wrappy": "1"}}, "node_modules/onetime": {"version": "5.1.2", "resolved": "http://r.npm.sankuai.com/onetime/download/onetime-5.1.2.tgz", "integrity": "sha1-0Oluu1awdHbfHdnEgG5SN5hcpF4=", "dev": true, "license": "MIT", "dependencies": {"mimic-fn": "^2.1.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/open": {"version": "8.4.2", "resolved": "http://r.npm.sankuai.com/open/download/open-8.4.2.tgz", "integrity": "sha1-W1/+Ko95Pc0qrXPlUMuHtZywhPk=", "dev": true, "license": "MIT", "dependencies": {"define-lazy-prop": "^2.0.0", "is-docker": "^2.1.1", "is-wsl": "^2.2.0"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/openai": {"version": "4.98.0", "resolved": "http://r.npm.sankuai.com/openai/download/openai-4.98.0.tgz", "integrity": "sha1-gdgijgbl2RlbrDsXCvQqVFQ5GZk=", "license": "Apache-2.0", "dependencies": {"@types/node": "^18.11.18", "@types/node-fetch": "^2.6.4", "abort-controller": "^3.0.0", "agentkeepalive": "^4.2.1", "form-data-encoder": "1.7.2", "formdata-node": "^4.3.2", "node-fetch": "^2.6.7"}, "bin": {"openai": "bin/cli"}, "peerDependencies": {"ws": "^8.18.0", "zod": "^3.23.8"}, "peerDependenciesMeta": {"ws": {"optional": true}, "zod": {"optional": true}}}, "node_modules/openai/node_modules/@types/node": {"version": "18.19.100", "resolved": "http://r.npm.sankuai.com/@types/node/download/@types/node-18.19.100.tgz", "integrity": "sha1-fzrvu2kRCZq34JAqHzc7Gk0sGUc=", "license": "MIT", "dependencies": {"undici-types": "~5.26.4"}}, "node_modules/openai/node_modules/undici-types": {"version": "5.26.5", "resolved": "http://r.npm.sankuai.com/undici-types/download/undici-types-5.26.5.tgz", "integrity": "sha1-vNU5iT0AtW6WT9JlekhmsiGmVhc=", "license": "MIT"}, "node_modules/opencollective-postinstall": {"version": "2.0.3", "resolved": "http://r.npm.sankuai.com/opencollective-postinstall/download/opencollective-postinstall-2.0.3.tgz", "integrity": "sha1-eg//l49tv6TQBiOPusmO1BmMMlk=", "license": "MIT", "bin": {"opencollective-postinstall": "index.js"}}, "node_modules/opener": {"version": "1.5.2", "resolved": "http://r.npm.sankuai.com/opener/download/opener-1.5.2.tgz", "integrity": "sha1-XTfh81B3udysQwE3InGv3rKhNZg=", "dev": true, "license": "(WTFPL OR MIT)", "bin": {"opener": "bin/opener-bin.js"}}, "node_modules/optionator": {"version": "0.9.4", "resolved": "http://r.npm.sankuai.com/optionator/download/optionator-0.9.4.tgz", "integrity": "sha1-fqHBpdkddk+yghOciP4R4YKjpzQ=", "dev": true, "license": "MIT", "dependencies": {"deep-is": "^0.1.3", "fast-levenshtein": "^2.0.6", "levn": "^0.4.1", "prelude-ls": "^1.2.1", "type-check": "^0.4.0", "word-wrap": "^1.2.5"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/ora": {"version": "5.4.1", "resolved": "http://r.npm.sankuai.com/ora/download/ora-5.4.1.tgz", "integrity": "sha1-GyZ4Qmr0rEpQkAjl5KyemVnbnhg=", "dev": true, "license": "MIT", "dependencies": {"bl": "^4.1.0", "chalk": "^4.1.0", "cli-cursor": "^3.1.0", "cli-spinners": "^2.5.0", "is-interactive": "^1.0.0", "is-unicode-supported": "^0.1.0", "log-symbols": "^4.1.0", "strip-ansi": "^6.0.0", "wcwidth": "^1.0.1"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/ora/node_modules/cli-cursor": {"version": "3.1.0", "resolved": "http://r.npm.sankuai.com/cli-cursor/download/cli-cursor-3.1.0.tgz", "integrity": "sha1-JkMFp65JDR0Dvwybp8kl0XU68wc=", "dev": true, "license": "MIT", "dependencies": {"restore-cursor": "^3.1.0"}, "engines": {"node": ">=8"}}, "node_modules/ora/node_modules/restore-cursor": {"version": "3.1.0", "resolved": "http://r.npm.sankuai.com/restore-cursor/download/restore-cursor-3.1.0.tgz", "integrity": "sha1-OfZ8VLOnpYzqUjbZXPADQjljH34=", "dev": true, "license": "MIT", "dependencies": {"onetime": "^5.1.0", "signal-exit": "^3.0.2"}, "engines": {"node": ">=8"}}, "node_modules/os-browserify": {"version": "0.3.0", "resolved": "http://r.npm.sankuai.com/os-browserify/download/os-browserify-0.3.0.tgz", "integrity": "sha1-hUNzx/XCMVkU/Jv8a9gjj92h7Cc=", "dev": true, "license": "MIT"}, "node_modules/p-finally": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/p-finally/download/p-finally-1.0.0.tgz", "integrity": "sha1-P7z7FbiZpEEjs0ttzBi3JDNqLK4=", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/p-limit": {"version": "2.3.0", "resolved": "http://r.npm.sankuai.com/p-limit/download/p-limit-2.3.0.tgz", "integrity": "sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE=", "dev": true, "license": "MIT", "dependencies": {"p-try": "^2.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-locate": {"version": "4.1.0", "resolved": "http://r.npm.sankuai.com/p-locate/download/p-locate-4.1.0.tgz", "integrity": "sha1-o0KLtwiLOmApL2aRkni3wpetTwc=", "dev": true, "license": "MIT", "dependencies": {"p-limit": "^2.2.0"}, "engines": {"node": ">=8"}}, "node_modules/p-retry": {"version": "4.6.2", "resolved": "http://r.npm.sankuai.com/p-retry/download/p-retry-4.6.2.tgz", "integrity": "sha1-m6rnGEBX7dThcjHO4EJkEG4JKhY=", "dev": true, "license": "MIT", "dependencies": {"@types/retry": "0.12.0", "retry": "^0.13.1"}, "engines": {"node": ">=8"}}, "node_modules/p-try": {"version": "2.2.0", "resolved": "http://r.npm.sankuai.com/p-try/download/p-try-2.2.0.tgz", "integrity": "sha1-yyhoVA4xPWHeWPr741zpAE1VQOY=", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/param-case": {"version": "3.0.4", "resolved": "http://r.npm.sankuai.com/param-case/download/param-case-3.0.4.tgz", "integrity": "sha1-fRf+SqEr3jTUp32RrPtiGcqtAcU=", "dev": true, "license": "MIT", "dependencies": {"dot-case": "^3.0.4", "tslib": "^2.0.3"}}, "node_modules/parent-module": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/parent-module/download/parent-module-1.0.1.tgz", "integrity": "sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=", "dev": true, "license": "MIT", "dependencies": {"callsites": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/parse-asn1": {"version": "5.1.7", "resolved": "http://r.npm.sankuai.com/parse-asn1/download/parse-asn1-5.1.7.tgz", "integrity": "sha1-c82qqCISX5ZHFlYl60X4oFHS3wY=", "dev": true, "license": "ISC", "dependencies": {"asn1.js": "^4.10.1", "browserify-aes": "^1.2.0", "evp_bytestokey": "^1.0.3", "hash-base": "~3.0", "pbkdf2": "^3.1.2", "safe-buffer": "^5.2.1"}, "engines": {"node": ">= 0.10"}}, "node_modules/parse-json": {"version": "5.2.0", "resolved": "http://r.npm.sankuai.com/parse-json/download/parse-json-5.2.0.tgz", "integrity": "sha1-x2/Gbe5UIxyWKyK8yKcs8vmXU80=", "dev": true, "license": "MIT", "dependencies": {"@babel/code-frame": "^7.0.0", "error-ex": "^1.3.1", "json-parse-even-better-errors": "^2.3.0", "lines-and-columns": "^1.1.6"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/parse5": {"version": "5.1.1", "resolved": "http://r.npm.sankuai.com/parse5/download/parse5-5.1.1.tgz", "integrity": "sha1-9o5OW6GFKsLK3AD0VV//bCq7YXg=", "dev": true, "license": "MIT"}, "node_modules/parse5-htmlparser2-tree-adapter": {"version": "6.0.1", "resolved": "http://r.npm.sankuai.com/parse5-htmlparser2-tree-adapter/download/parse5-htmlparser2-tree-adapter-6.0.1.tgz", "integrity": "sha1-LN+a2CMyEUA3DU2/XT6Sx8jdxuY=", "dev": true, "license": "MIT", "dependencies": {"parse5": "^6.0.1"}}, "node_modules/parse5-htmlparser2-tree-adapter/node_modules/parse5": {"version": "6.0.1", "resolved": "http://r.npm.sankuai.com/parse5/download/parse5-6.0.1.tgz", "integrity": "sha1-4aHAhcVps9wIMhGE8Zo5zCf3wws=", "dev": true, "license": "MIT"}, "node_modules/parseurl": {"version": "1.3.3", "resolved": "http://r.npm.sankuai.com/parseurl/download/parseurl-1.3.3.tgz", "integrity": "sha1-naGee+6NEt/wUT7Vt2lXeTvC6NQ=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/pascal-case": {"version": "3.1.2", "resolved": "http://r.npm.sankuai.com/pascal-case/download/pascal-case-3.1.2.tgz", "integrity": "sha1-tI4O8rmOIF58Ha50fQsVCCN2YOs=", "dev": true, "license": "MIT", "dependencies": {"no-case": "^3.0.4", "tslib": "^2.0.3"}}, "node_modules/path-browserify": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/path-browserify/download/path-browserify-1.0.1.tgz", "integrity": "sha1-2YRUqcN1PVeQhg8W9ohnueRr4f0=", "dev": true, "license": "MIT"}, "node_modules/path-exists": {"version": "4.0.0", "resolved": "http://r.npm.sankuai.com/path-exists/download/path-exists-4.0.0.tgz", "integrity": "sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/path-is-absolute": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/path-is-absolute/download/path-is-absolute-1.0.1.tgz", "integrity": "sha1-F0uSaHNVNP+8es5r9TpanhtcX18=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/path-key": {"version": "2.0.1", "resolved": "http://r.npm.sankuai.com/path-key/download/path-key-2.0.1.tgz", "integrity": "sha1-QRyttXTFoUDTpLGRDUDYDMn0C0A=", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/path-parse": {"version": "1.0.7", "resolved": "http://r.npm.sankuai.com/path-parse/download/path-parse-1.0.7.tgz", "integrity": "sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU=", "dev": true, "license": "MIT"}, "node_modules/path-to-regexp": {"version": "0.1.12", "resolved": "http://r.npm.sankuai.com/path-to-regexp/download/path-to-regexp-0.1.12.tgz", "integrity": "sha1-1eGhLkeKl21DLvPFjVNLmSMWS7c=", "dev": true, "license": "MIT"}, "node_modules/path-type": {"version": "4.0.0", "resolved": "http://r.npm.sankuai.com/path-type/download/path-type-4.0.0.tgz", "integrity": "sha1-hO0BwKe6OAr+CdkKjBgNzZ0DBDs=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/pbkdf2": {"version": "3.1.2", "resolved": "http://r.npm.sankuai.com/pbkdf2/download/pbkdf2-3.1.2.tgz", "integrity": "sha1-3YIqoIh1gOUvGgOdw+2hCO+uMHU=", "dev": true, "license": "MIT", "dependencies": {"create-hash": "^1.1.2", "create-hmac": "^1.1.4", "ripemd160": "^2.0.1", "safe-buffer": "^5.0.1", "sha.js": "^2.4.8"}, "engines": {"node": ">=0.12"}}, "node_modules/picocolors": {"version": "1.1.1", "resolved": "http://r.npm.sankuai.com/picocolors/download/picocolors-1.1.1.tgz", "integrity": "sha1-PTIa8+q5ObCDyPkpodEs2oHCa2s=", "license": "ISC"}, "node_modules/picomatch": {"version": "2.3.1", "resolved": "http://r.npm.sankuai.com/picomatch/download/picomatch-2.3.1.tgz", "integrity": "sha1-O6ODNzNkbZ0+SZWUbBNlpn+wekI=", "dev": true, "license": "MIT", "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/pkg-dir": {"version": "4.2.0", "resolved": "http://r.npm.sankuai.com/pkg-dir/download/pkg-dir-4.2.0.tgz", "integrity": "sha1-8JkTPfft5CLoHR2ESCcO6z5CYfM=", "dev": true, "license": "MIT", "dependencies": {"find-up": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/portfinder": {"version": "1.0.37", "resolved": "http://r.npm.sankuai.com/portfinder/download/portfinder-1.0.37.tgz", "integrity": "sha1-krdU74mhGAHI7+Sw5c2EWwBkwhI=", "dev": true, "license": "MIT", "dependencies": {"async": "^3.2.6", "debug": "^4.3.6"}, "engines": {"node": ">= 10.12"}}, "node_modules/possible-typed-array-names": {"version": "1.1.0", "resolved": "http://r.npm.sankuai.com/possible-typed-array-names/download/possible-typed-array-names-1.1.0.tgz", "integrity": "sha1-k+NYK8DlQmWG2dB7ee5A/IQd5K4=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/postcss": {"version": "8.5.3", "resolved": "http://r.npm.sankuai.com/postcss/download/postcss-8.5.3.tgz", "integrity": "sha1-FGO28cf7Fv4lhzbLopot41I36vs=", "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/postcss"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"nanoid": "^3.3.8", "picocolors": "^1.1.1", "source-map-js": "^1.2.1"}, "engines": {"node": "^10 || ^12 || >=14"}}, "node_modules/postcss-calc": {"version": "8.2.4", "resolved": "http://r.npm.sankuai.com/postcss-calc/download/postcss-calc-8.2.4.tgz", "integrity": "sha1-d7nCm/y+igf/ZpPchwUIKIiXOaU=", "dev": true, "license": "MIT", "dependencies": {"postcss-selector-parser": "^6.0.9", "postcss-value-parser": "^4.2.0"}, "peerDependencies": {"postcss": "^8.2.2"}}, "node_modules/postcss-colormin": {"version": "5.3.1", "resolved": "http://r.npm.sankuai.com/postcss-colormin/download/postcss-colormin-5.3.1.tgz", "integrity": "sha1-hsJ8Ju1roA2Wx54I8/+0GNHRmI8=", "dev": true, "license": "MIT", "dependencies": {"browserslist": "^4.21.4", "caniuse-api": "^3.0.0", "colord": "^2.9.1", "postcss-value-parser": "^4.2.0"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/postcss-convert-values": {"version": "5.1.3", "resolved": "http://r.npm.sankuai.com/postcss-convert-values/download/postcss-convert-values-5.1.3.tgz", "integrity": "sha1-BJmLubprZaoxA11mmmrzQsX505M=", "dev": true, "license": "MIT", "dependencies": {"browserslist": "^4.21.4", "postcss-value-parser": "^4.2.0"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/postcss-discard-comments": {"version": "5.1.2", "resolved": "http://r.npm.sankuai.com/postcss-discard-comments/download/postcss-discard-comments-5.1.2.tgz", "integrity": "sha1-jfXoHSklryeAB1hAwVJvBmDlNpY=", "dev": true, "license": "MIT", "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/postcss-discard-duplicates": {"version": "5.1.0", "resolved": "http://r.npm.sankuai.com/postcss-discard-duplicates/download/postcss-discard-duplicates-5.1.0.tgz", "integrity": "sha1-nrT+hFZwak7r1tO3t3fQe60D6Eg=", "dev": true, "license": "MIT", "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/postcss-discard-empty": {"version": "5.1.1", "resolved": "http://r.npm.sankuai.com/postcss-discard-empty/download/postcss-discard-empty-5.1.1.tgz", "integrity": "sha1-5XdiND/39QP+U/ylU9GNfww2nGw=", "dev": true, "license": "MIT", "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/postcss-discard-overridden": {"version": "5.1.0", "resolved": "http://r.npm.sankuai.com/postcss-discard-overridden/download/postcss-discard-overridden-5.1.0.tgz", "integrity": "sha1-foxbUzJXR+nZATG7iGNSgvtKJ24=", "dev": true, "license": "MIT", "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/postcss-loader": {"version": "6.2.1", "resolved": "http://r.npm.sankuai.com/postcss-loader/download/postcss-loader-6.2.1.tgz", "integrity": "sha1-CJX3NGsXAhA9MP3Gbk1JSpPACO8=", "dev": true, "license": "MIT", "dependencies": {"cosmiconfig": "^7.0.0", "klona": "^2.0.5", "semver": "^7.3.5"}, "engines": {"node": ">= 12.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"postcss": "^7.0.0 || ^8.0.1", "webpack": "^5.0.0"}}, "node_modules/postcss-loader/node_modules/semver": {"version": "7.7.2", "resolved": "http://r.npm.sankuai.com/semver/download/semver-7.7.2.tgz", "integrity": "sha1-Z9mf3NNc7CHm+Lh6f9UVoz+YK1g=", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/postcss-merge-longhand": {"version": "5.1.7", "resolved": "http://r.npm.sankuai.com/postcss-merge-longhand/download/postcss-merge-longhand-5.1.7.tgz", "integrity": "sha1-JKG99ALZ7w5w9Wjzm9wDRNVo+xY=", "dev": true, "license": "MIT", "dependencies": {"postcss-value-parser": "^4.2.0", "stylehacks": "^5.1.1"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/postcss-merge-rules": {"version": "5.1.4", "resolved": "http://r.npm.sankuai.com/postcss-merge-rules/download/postcss-merge-rules-5.1.4.tgz", "integrity": "sha1-Lyb6XKy3WxQC4hN4n2dmrl5AMTw=", "dev": true, "license": "MIT", "dependencies": {"browserslist": "^4.21.4", "caniuse-api": "^3.0.0", "cssnano-utils": "^3.1.0", "postcss-selector-parser": "^6.0.5"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/postcss-minify-font-values": {"version": "5.1.0", "resolved": "http://r.npm.sankuai.com/postcss-minify-font-values/download/postcss-minify-font-values-5.1.0.tgz", "integrity": "sha1-8d8AFKcmCD0mDTvYXXOF+4nR8Bs=", "dev": true, "license": "MIT", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/postcss-minify-gradients": {"version": "5.1.1", "resolved": "http://r.npm.sankuai.com/postcss-minify-gradients/download/postcss-minify-gradients-5.1.1.tgz", "integrity": "sha1-8f4bT0mBNKUGgkDC8l1G/NI2uiw=", "dev": true, "license": "MIT", "dependencies": {"colord": "^2.9.1", "cssnano-utils": "^3.1.0", "postcss-value-parser": "^4.2.0"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/postcss-minify-params": {"version": "5.1.4", "resolved": "http://r.npm.sankuai.com/postcss-minify-params/download/postcss-minify-params-5.1.4.tgz", "integrity": "sha1-wGpseHEosyCLOMk2TPxAyKpdc1I=", "dev": true, "license": "MIT", "dependencies": {"browserslist": "^4.21.4", "cssnano-utils": "^3.1.0", "postcss-value-parser": "^4.2.0"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/postcss-minify-selectors": {"version": "5.2.1", "resolved": "http://r.npm.sankuai.com/postcss-minify-selectors/download/postcss-minify-selectors-5.2.1.tgz", "integrity": "sha1-1OfmtGFHuBF+qTJakVqAHV/mVsY=", "dev": true, "license": "MIT", "dependencies": {"postcss-selector-parser": "^6.0.5"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/postcss-modules-extract-imports": {"version": "3.1.0", "resolved": "http://r.npm.sankuai.com/postcss-modules-extract-imports/download/postcss-modules-extract-imports-3.1.0.tgz", "integrity": "sha1-tEl8uFqcDEtaq+t1m7JejYnxUAI=", "dev": true, "license": "ISC", "engines": {"node": "^10 || ^12 || >= 14"}, "peerDependencies": {"postcss": "^8.1.0"}}, "node_modules/postcss-modules-local-by-default": {"version": "4.2.0", "resolved": "http://r.npm.sankuai.com/postcss-modules-local-by-default/download/postcss-modules-local-by-default-4.2.0.tgz", "integrity": "sha1-0VD0ODeDHa4l5AhVluhPb11uw2g=", "dev": true, "license": "MIT", "dependencies": {"icss-utils": "^5.0.0", "postcss-selector-parser": "^7.0.0", "postcss-value-parser": "^4.1.0"}, "engines": {"node": "^10 || ^12 || >= 14"}, "peerDependencies": {"postcss": "^8.1.0"}}, "node_modules/postcss-modules-local-by-default/node_modules/postcss-selector-parser": {"version": "7.1.0", "resolved": "http://r.npm.sankuai.com/postcss-selector-parser/download/postcss-selector-parser-7.1.0.tgz", "integrity": "sha1-TWr5frpl1zvE2EvLND6GXX3RYmI=", "dev": true, "license": "MIT", "dependencies": {"cssesc": "^3.0.0", "util-deprecate": "^1.0.2"}, "engines": {"node": ">=4"}}, "node_modules/postcss-modules-scope": {"version": "3.2.1", "resolved": "http://r.npm.sankuai.com/postcss-modules-scope/download/postcss-modules-scope-3.2.1.tgz", "integrity": "sha1-G7zN3LOY8delEeCi0dBHcYr0B4w=", "dev": true, "license": "ISC", "dependencies": {"postcss-selector-parser": "^7.0.0"}, "engines": {"node": "^10 || ^12 || >= 14"}, "peerDependencies": {"postcss": "^8.1.0"}}, "node_modules/postcss-modules-scope/node_modules/postcss-selector-parser": {"version": "7.1.0", "resolved": "http://r.npm.sankuai.com/postcss-selector-parser/download/postcss-selector-parser-7.1.0.tgz", "integrity": "sha1-TWr5frpl1zvE2EvLND6GXX3RYmI=", "dev": true, "license": "MIT", "dependencies": {"cssesc": "^3.0.0", "util-deprecate": "^1.0.2"}, "engines": {"node": ">=4"}}, "node_modules/postcss-modules-values": {"version": "4.0.0", "resolved": "http://r.npm.sankuai.com/postcss-modules-values/download/postcss-modules-values-4.0.0.tgz", "integrity": "sha1-18Xn5ow7s8myfL9Iyguz/7RgLJw=", "dev": true, "license": "ISC", "dependencies": {"icss-utils": "^5.0.0"}, "engines": {"node": "^10 || ^12 || >= 14"}, "peerDependencies": {"postcss": "^8.1.0"}}, "node_modules/postcss-normalize-charset": {"version": "5.1.0", "resolved": "http://r.npm.sankuai.com/postcss-normalize-charset/download/postcss-normalize-charset-5.1.0.tgz", "integrity": "sha1-kwLeCykJS1LCWemyz43Ah5h58O0=", "dev": true, "license": "MIT", "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/postcss-normalize-display-values": {"version": "5.1.0", "resolved": "http://r.npm.sankuai.com/postcss-normalize-display-values/download/postcss-normalize-display-values-5.1.0.tgz", "integrity": "sha1-cqu65YCBlg6e3XIA/PIauDJcPag=", "dev": true, "license": "MIT", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/postcss-normalize-positions": {"version": "5.1.1", "resolved": "http://r.npm.sankuai.com/postcss-normalize-positions/download/postcss-normalize-positions-5.1.1.tgz", "integrity": "sha1-75cnnYlAh7WTJbRcR/HoY9rvu5I=", "dev": true, "license": "MIT", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/postcss-normalize-repeat-style": {"version": "5.1.1", "resolved": "http://r.npm.sankuai.com/postcss-normalize-repeat-style/download/postcss-normalize-repeat-style-5.1.1.tgz", "integrity": "sha1-6euWgFIE9HZt9m/QntLhNUVCD7I=", "dev": true, "license": "MIT", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/postcss-normalize-string": {"version": "5.1.0", "resolved": "http://r.npm.sankuai.com/postcss-normalize-string/download/postcss-normalize-string-5.1.0.tgz", "integrity": "sha1-QRlhFp4HMIyCwfjFXz6KM3dX4ig=", "dev": true, "license": "MIT", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/postcss-normalize-timing-functions": {"version": "5.1.0", "resolved": "http://r.npm.sankuai.com/postcss-normalize-timing-functions/download/postcss-normalize-timing-functions-5.1.0.tgz", "integrity": "sha1-1WFEEPjwsjiOnyQKpgEbpvUtr7s=", "dev": true, "license": "MIT", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/postcss-normalize-unicode": {"version": "5.1.1", "resolved": "http://r.npm.sankuai.com/postcss-normalize-unicode/download/postcss-normalize-unicode-5.1.1.tgz", "integrity": "sha1-9nKX/KP+p/F+DSyqQHaa/Eh6oDA=", "dev": true, "license": "MIT", "dependencies": {"browserslist": "^4.21.4", "postcss-value-parser": "^4.2.0"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/postcss-normalize-url": {"version": "5.1.0", "resolved": "http://r.npm.sankuai.com/postcss-normalize-url/download/postcss-normalize-url-5.1.0.tgz", "integrity": "sha1-7Z2IyoLiGr75n3Q0V9NymgQq3Nw=", "dev": true, "license": "MIT", "dependencies": {"normalize-url": "^6.0.1", "postcss-value-parser": "^4.2.0"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/postcss-normalize-whitespace": {"version": "5.1.1", "resolved": "http://r.npm.sankuai.com/postcss-normalize-whitespace/download/postcss-normalize-whitespace-5.1.1.tgz", "integrity": "sha1-CKGg0f+henzG7+HmydqWnMRJPPo=", "dev": true, "license": "MIT", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/postcss-ordered-values": {"version": "5.1.3", "resolved": "http://r.npm.sankuai.com/postcss-ordered-values/download/postcss-ordered-values-5.1.3.tgz", "integrity": "sha1-tv0r0Q+TeyPYa8gpxp53Ms526jg=", "dev": true, "license": "MIT", "dependencies": {"cssnano-utils": "^3.1.0", "postcss-value-parser": "^4.2.0"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/postcss-reduce-initial": {"version": "5.1.2", "resolved": "http://r.npm.sankuai.com/postcss-reduce-initial/download/postcss-reduce-initial-5.1.2.tgz", "integrity": "sha1-eYzXez4DPq5xBcGMnTcdmJ4TgtY=", "dev": true, "license": "MIT", "dependencies": {"browserslist": "^4.21.4", "caniuse-api": "^3.0.0"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/postcss-reduce-transforms": {"version": "5.1.0", "resolved": "http://r.npm.sankuai.com/postcss-reduce-transforms/download/postcss-reduce-transforms-5.1.0.tgz", "integrity": "sha1-Mztw53WLgC890N3+mLscz++Wtuk=", "dev": true, "license": "MIT", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/postcss-selector-parser": {"version": "6.1.2", "resolved": "http://r.npm.sankuai.com/postcss-selector-parser/download/postcss-selector-parser-6.1.2.tgz", "integrity": "sha1-J+y0H7Djtrp6HshP/zR/c0x5Kd4=", "dev": true, "license": "MIT", "dependencies": {"cssesc": "^3.0.0", "util-deprecate": "^1.0.2"}, "engines": {"node": ">=4"}}, "node_modules/postcss-svgo": {"version": "5.1.0", "resolved": "http://r.npm.sankuai.com/postcss-svgo/download/postcss-svgo-5.1.0.tgz", "integrity": "sha1-CjF0AM7XifIzoogm53Uj8VhX2A0=", "dev": true, "license": "MIT", "dependencies": {"postcss-value-parser": "^4.2.0", "svgo": "^2.7.0"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/postcss-unique-selectors": {"version": "5.1.1", "resolved": "http://r.npm.sankuai.com/postcss-unique-selectors/download/postcss-unique-selectors-5.1.1.tgz", "integrity": "sha1-qfJz0erNCemqYIj0sFB7GLG1QbY=", "dev": true, "license": "MIT", "dependencies": {"postcss-selector-parser": "^6.0.5"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/postcss-value-parser": {"version": "4.2.0", "resolved": "http://r.npm.sankuai.com/postcss-value-parser/download/postcss-value-parser-4.2.0.tgz", "integrity": "sha1-cjwJkgg2um0+WvAZ+SvAlxwC5RQ=", "dev": true, "license": "MIT"}, "node_modules/prelude-ls": {"version": "1.2.1", "resolved": "http://r.npm.sankuai.com/prelude-ls/download/prelude-ls-1.2.1.tgz", "integrity": "sha1-3rxkidem5rDnYRiIzsiAM30xY5Y=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8.0"}}, "node_modules/prettier": {"version": "2.8.8", "resolved": "http://r.npm.sankuai.com/prettier/download/prettier-2.8.8.tgz", "integrity": "sha1-6MXX6YpDBf/j3i4fxKyhpxwosdo=", "license": "MIT", "optional": true, "bin": {"prettier": "bin-prettier.js"}, "engines": {"node": ">=10.13.0"}, "funding": {"url": "https://github.com/prettier/prettier?sponsor=1"}}, "node_modules/pretty-error": {"version": "4.0.0", "resolved": "http://r.npm.sankuai.com/pretty-error/download/pretty-error-4.0.0.tgz", "integrity": "sha1-kKcD9G3XI0rbRtD4SCPp0cuPENY=", "dev": true, "license": "MIT", "dependencies": {"lodash": "^4.17.20", "renderkid": "^3.0.0"}}, "node_modules/process": {"version": "0.11.10", "resolved": "http://r.npm.sankuai.com/process/download/process-0.11.10.tgz", "integrity": "sha1-czIwDoQBYb2j5podHZGn1LwW8YI=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6.0"}}, "node_modules/process-nextick-args": {"version": "2.0.1", "resolved": "http://r.npm.sankuai.com/process-nextick-args/download/process-nextick-args-2.0.1.tgz", "integrity": "sha1-eCDZsWEgzFXKmud5JoCufbptf+I=", "dev": true, "license": "MIT"}, "node_modules/progress": {"version": "2.0.3", "resolved": "http://r.npm.sankuai.com/progress/download/progress-2.0.3.tgz", "integrity": "sha1-foz42PW48jnBvGi+tOt4Vn1XLvg=", "dev": true, "license": "MIT", "engines": {"node": ">=0.4.0"}}, "node_modules/progress-webpack-plugin": {"version": "1.0.16", "resolved": "http://r.npm.sankuai.com/progress-webpack-plugin/download/progress-webpack-plugin-1.0.16.tgz", "integrity": "sha1-J49cGv0hr3g6rXLF7JUkFSAjD+U=", "dev": true, "license": "MIT", "dependencies": {"chalk": "^2.1.0", "figures": "^2.0.0", "log-update": "^2.3.0"}, "engines": {"node": ">= 10.13.0"}, "peerDependencies": {"webpack": "^2.0.0 || ^3.0.0 || ^4.0.0 || ^5.0.0"}}, "node_modules/progress-webpack-plugin/node_modules/ansi-styles": {"version": "3.2.1", "resolved": "http://r.npm.sankuai.com/ansi-styles/download/ansi-styles-3.2.1.tgz", "integrity": "sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0=", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^1.9.0"}, "engines": {"node": ">=4"}}, "node_modules/progress-webpack-plugin/node_modules/chalk": {"version": "2.4.2", "resolved": "http://r.npm.sankuai.com/chalk/download/chalk-2.4.2.tgz", "integrity": "sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ=", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}, "engines": {"node": ">=4"}}, "node_modules/progress-webpack-plugin/node_modules/color-convert": {"version": "1.9.3", "resolved": "http://r.npm.sankuai.com/color-convert/download/color-convert-1.9.3.tgz", "integrity": "sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg=", "dev": true, "license": "MIT", "dependencies": {"color-name": "1.1.3"}}, "node_modules/progress-webpack-plugin/node_modules/color-name": {"version": "1.1.3", "resolved": "http://r.npm.sankuai.com/color-name/download/color-name-1.1.3.tgz", "integrity": "sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=", "dev": true, "license": "MIT"}, "node_modules/progress-webpack-plugin/node_modules/has-flag": {"version": "3.0.0", "resolved": "http://r.npm.sankuai.com/has-flag/download/has-flag-3.0.0.tgz", "integrity": "sha1-tdRU3CGZriJWmfNGfloH87lVuv0=", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/progress-webpack-plugin/node_modules/supports-color": {"version": "5.5.0", "resolved": "http://r.npm.sankuai.com/supports-color/download/supports-color-5.5.0.tgz", "integrity": "sha1-4uaaRKyHcveKHsCzW2id9lMO/I8=", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/proxy-addr": {"version": "2.0.7", "resolved": "http://r.npm.sankuai.com/proxy-addr/download/proxy-addr-2.0.7.tgz", "integrity": "sha1-8Z/mnOqzEe65S0LnDowgcPm6ECU=", "dev": true, "license": "MIT", "dependencies": {"forwarded": "0.2.0", "ipaddr.js": "1.9.1"}, "engines": {"node": ">= 0.10"}}, "node_modules/proxy-addr/node_modules/ipaddr.js": {"version": "1.9.1", "resolved": "http://r.npm.sankuai.com/ipaddr.js/download/ipaddr.js-1.9.1.tgz", "integrity": "sha1-v/OFQ+64mEglB5/zoqjmy9RngbM=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.10"}}, "node_modules/proxy-from-env": {"version": "1.1.0", "resolved": "http://r.npm.sankuai.com/proxy-from-env/download/proxy-from-env-1.1.0.tgz", "integrity": "sha1-4QLxbKNVQkhldV0sno6k8k1Yw+I=", "license": "MIT"}, "node_modules/pseudomap": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/pseudomap/download/pseudomap-1.0.2.tgz", "integrity": "sha1-8FKijacOYYkX7wqKw0wa5aaChrM=", "dev": true, "license": "ISC"}, "node_modules/public-encrypt": {"version": "4.0.3", "resolved": "http://r.npm.sankuai.com/public-encrypt/download/public-encrypt-4.0.3.tgz", "integrity": "sha1-T8ydd6B+SLp1J+fL4N4z0HATMeA=", "dev": true, "license": "MIT", "dependencies": {"bn.js": "^4.1.0", "browserify-rsa": "^4.0.0", "create-hash": "^1.1.0", "parse-asn1": "^5.0.0", "randombytes": "^2.0.1", "safe-buffer": "^5.1.2"}}, "node_modules/pump": {"version": "3.0.2", "resolved": "http://r.npm.sankuai.com/pump/download/pump-3.0.2.tgz", "integrity": "sha1-g28+3WvC7lmSVskk/+DYhXPdy/g=", "dev": true, "license": "MIT", "dependencies": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "node_modules/punycode": {"version": "2.3.1", "resolved": "http://r.npm.sankuai.com/punycode/download/punycode-2.3.1.tgz", "integrity": "sha1-AnQi4vrsCyXhVJw+G9gwm5EztuU=", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/qs": {"version": "6.13.0", "resolved": "http://r.npm.sankuai.com/qs/download/qs-6.13.0.tgz", "integrity": "sha1-bKO9WEOffiRWVXmJl3h7DYilGQY=", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"side-channel": "^1.0.6"}, "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/queue-microtask": {"version": "1.2.3", "resolved": "http://r.npm.sankuai.com/queue-microtask/download/queue-microtask-1.2.3.tgz", "integrity": "sha1-SSkii7xyTfrEPg77BYyve2z7YkM=", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/randombytes": {"version": "2.1.0", "resolved": "http://r.npm.sankuai.com/randombytes/download/randombytes-2.1.0.tgz", "integrity": "sha1-32+ENy8CcNxlzfYpE0mrekc9Tyo=", "dev": true, "license": "MIT", "dependencies": {"safe-buffer": "^5.1.0"}}, "node_modules/randomfill": {"version": "1.0.4", "resolved": "http://r.npm.sankuai.com/randomfill/download/randomfill-1.0.4.tgz", "integrity": "sha1-ySGW/IarQr6YPxvzF3giSTHWFFg=", "dev": true, "license": "MIT", "dependencies": {"randombytes": "^2.0.5", "safe-buffer": "^5.1.0"}}, "node_modules/range-parser": {"version": "1.2.1", "resolved": "http://r.npm.sankuai.com/range-parser/download/range-parser-1.2.1.tgz", "integrity": "sha1-PPNwI9GZ4cJNGlW4SADC8+ZGgDE=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/raw-body": {"version": "2.5.2", "resolved": "http://r.npm.sankuai.com/raw-body/download/raw-body-2.5.2.tgz", "integrity": "sha1-mf69g7kOCJdQh+jx+UGaFJNmtoo=", "dev": true, "license": "MIT", "dependencies": {"bytes": "3.1.2", "http-errors": "2.0.0", "iconv-lite": "0.4.24", "unpipe": "1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/read-pkg": {"version": "5.2.0", "resolved": "http://r.npm.sankuai.com/read-pkg/download/read-pkg-5.2.0.tgz", "integrity": "sha1-e/KVQ4yloz5WzTDgU7NO5yUMk8w=", "dev": true, "license": "MIT", "dependencies": {"@types/normalize-package-data": "^2.4.0", "normalize-package-data": "^2.5.0", "parse-json": "^5.0.0", "type-fest": "^0.6.0"}, "engines": {"node": ">=8"}}, "node_modules/read-pkg-up": {"version": "7.0.1", "resolved": "http://r.npm.sankuai.com/read-pkg-up/download/read-pkg-up-7.0.1.tgz", "integrity": "sha1-86YTV1hFlzOuK5VjgFbhhU5+9Qc=", "dev": true, "license": "MIT", "dependencies": {"find-up": "^4.1.0", "read-pkg": "^5.2.0", "type-fest": "^0.8.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/read-pkg-up/node_modules/type-fest": {"version": "0.8.1", "resolved": "http://r.npm.sankuai.com/type-fest/download/type-fest-0.8.1.tgz", "integrity": "sha1-CeJJ696FHTseSNJ8EFREZn8XuD0=", "dev": true, "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=8"}}, "node_modules/read-pkg/node_modules/type-fest": {"version": "0.6.0", "resolved": "http://r.npm.sankuai.com/type-fest/download/type-fest-0.6.0.tgz", "integrity": "sha1-jSojcNPfiG61yQraHFv2GIrPg4s=", "dev": true, "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=8"}}, "node_modules/readable-stream": {"version": "3.6.2", "resolved": "http://r.npm.sankuai.com/readable-stream/download/readable-stream-3.6.2.tgz", "integrity": "sha1-VqmzbqllwAxak+8x6xEaDxEFaWc=", "dev": true, "license": "MIT", "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/readdirp": {"version": "3.6.0", "resolved": "http://r.npm.sankuai.com/readdirp/download/readdirp-3.6.0.tgz", "integrity": "sha1-dKNwvYVxFuJFspzJc0DNQxoCpsc=", "dev": true, "license": "MIT", "dependencies": {"picomatch": "^2.2.1"}, "engines": {"node": ">=8.10.0"}}, "node_modules/regenerate": {"version": "1.4.2", "resolved": "http://r.npm.sankuai.com/regenerate/download/regenerate-1.4.2.tgz", "integrity": "sha1-uTRtiCfo9aMve6KWN9OYtpAUhIo=", "dev": true, "license": "MIT"}, "node_modules/regenerate-unicode-properties": {"version": "10.2.0", "resolved": "http://r.npm.sankuai.com/regenerate-unicode-properties/download/regenerate-unicode-properties-10.2.0.tgz", "integrity": "sha1-Ym4534w3Izjqm4Ao0fmdw/2cPbA=", "dev": true, "license": "MIT", "dependencies": {"regenerate": "^1.4.2"}, "engines": {"node": ">=4"}}, "node_modules/regenerator-runtime": {"version": "0.13.11", "resolved": "http://r.npm.sankuai.com/regenerator-runtime/download/regenerator-runtime-0.13.11.tgz", "integrity": "sha1-9tyj587sIFkNB62nhWNqkM3KF/k=", "license": "MIT"}, "node_modules/regexpp": {"version": "3.2.0", "resolved": "http://r.npm.sankuai.com/regexpp/download/regexpp-3.2.0.tgz", "integrity": "sha1-BCWido2PI7rXDKS5BGH6LxIT4bI=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/mysticatea"}}, "node_modules/regexpu-core": {"version": "6.2.0", "resolved": "http://r.npm.sankuai.com/regexpu-core/download/regexpu-core-6.2.0.tgz", "integrity": "sha1-DlGQ155UK/KUlV3Mq64E08fVOCY=", "dev": true, "license": "MIT", "dependencies": {"regenerate": "^1.4.2", "regenerate-unicode-properties": "^10.2.0", "regjsgen": "^0.8.0", "regjsparser": "^0.12.0", "unicode-match-property-ecmascript": "^2.0.0", "unicode-match-property-value-ecmascript": "^2.1.0"}, "engines": {"node": ">=4"}}, "node_modules/regjsgen": {"version": "0.8.0", "resolved": "http://r.npm.sankuai.com/regjsgen/download/regjsgen-0.8.0.tgz", "integrity": "sha1-3yP/JuDFswCmRwytFgqdCQw6N6s=", "dev": true, "license": "MIT"}, "node_modules/regjsparser": {"version": "0.12.0", "resolved": "http://r.npm.sankuai.com/regjsparser/download/regjsparser-0.12.0.tgz", "integrity": "sha1-DoRt9sZTBYZCk3feVuBHVYOwiNw=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"jsesc": "~3.0.2"}, "bin": {"regjsparser": "bin/parser"}}, "node_modules/regjsparser/node_modules/jsesc": {"version": "3.0.2", "resolved": "http://r.npm.sankuai.com/jsesc/download/jsesc-3.0.2.tgz", "integrity": "sha1-u4sJpll7pCZCXy5KByRcPQC5ND4=", "dev": true, "license": "MIT", "bin": {"jsesc": "bin/jsesc"}, "engines": {"node": ">=6"}}, "node_modules/relateurl": {"version": "0.2.7", "resolved": "http://r.npm.sankuai.com/relateurl/download/relateurl-0.2.7.tgz", "integrity": "sha1-VNvzd+UUQKypCkzSdGANP/LYiKk=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.10"}}, "node_modules/renderkid": {"version": "3.0.0", "resolved": "http://r.npm.sankuai.com/renderkid/download/renderkid-3.0.0.tgz", "integrity": "sha1-X9gj5NaVHTc1jsyaWLHwaDa2Joo=", "dev": true, "license": "MIT", "dependencies": {"css-select": "^4.1.3", "dom-converter": "^0.2.0", "htmlparser2": "^6.1.0", "lodash": "^4.17.21", "strip-ansi": "^6.0.1"}}, "node_modules/require-directory": {"version": "2.1.1", "resolved": "http://r.npm.sankuai.com/require-directory/download/require-directory-2.1.1.tgz", "integrity": "sha1-jGStX9MNqxyXbiNE/+f3kqam30I=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/require-from-string": {"version": "2.0.2", "resolved": "http://r.npm.sankuai.com/require-from-string/download/require-from-string-2.0.2.tgz", "integrity": "sha1-iaf92TgmEmcxjq/hT5wy5ZjDaQk=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/requires-port": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/requires-port/download/requires-port-1.0.0.tgz", "integrity": "sha1-kl0mAdOaxIXgkc8NpcbmlNw9yv8=", "dev": true, "license": "MIT"}, "node_modules/resize-observer-polyfill": {"version": "1.5.1", "resolved": "http://r.npm.sankuai.com/resize-observer-polyfill/download/resize-observer-polyfill-1.5.1.tgz", "integrity": "sha1-DpAg3T0hAkRY1OvSfiPkAmmBBGQ=", "license": "MIT"}, "node_modules/resolve": {"version": "1.22.10", "resolved": "http://r.npm.sankuai.com/resolve/download/resolve-1.22.10.tgz", "integrity": "sha1-tmPoP/sJu/I4aURza6roAwKbizk=", "dev": true, "license": "MIT", "dependencies": {"is-core-module": "^2.16.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}, "bin": {"resolve": "bin/resolve"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/resolve-from": {"version": "4.0.0", "resolved": "http://r.npm.sankuai.com/resolve-from/download/resolve-from-4.0.0.tgz", "integrity": "sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY=", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/restore-cursor": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/restore-cursor/download/restore-cursor-2.0.0.tgz", "integrity": "sha1-n37ih/gv0ybU/RYpI9YhKe7g368=", "dev": true, "license": "MIT", "dependencies": {"onetime": "^2.0.0", "signal-exit": "^3.0.2"}, "engines": {"node": ">=4"}}, "node_modules/restore-cursor/node_modules/mimic-fn": {"version": "1.2.0", "resolved": "http://r.npm.sankuai.com/mimic-fn/download/mimic-fn-1.2.0.tgz", "integrity": "sha1-ggyGo5M0ZA6ZUWkovQP8qIBX0CI=", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/restore-cursor/node_modules/onetime": {"version": "2.0.1", "resolved": "http://r.npm.sankuai.com/onetime/download/onetime-2.0.1.tgz", "integrity": "sha1-BnQoIw/WdEOyeUsiu6UotoZ5YtQ=", "dev": true, "license": "MIT", "dependencies": {"mimic-fn": "^1.0.0"}, "engines": {"node": ">=4"}}, "node_modules/retry": {"version": "0.13.1", "resolved": "http://r.npm.sankuai.com/retry/download/retry-0.13.1.tgz", "integrity": "sha1-GFsVh6z2eRnWOzVzSeA1N7JIRlg=", "dev": true, "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/reusify": {"version": "1.1.0", "resolved": "http://r.npm.sankuai.com/reusify/download/reusify-1.1.0.tgz", "integrity": "sha1-D+E7lSLhRz9RtVjueW4I8R+bSJ8=", "dev": true, "license": "MIT", "engines": {"iojs": ">=1.0.0", "node": ">=0.10.0"}}, "node_modules/rimraf": {"version": "3.0.2", "resolved": "http://r.npm.sankuai.com/rimraf/download/rimraf-3.0.2.tgz", "integrity": "sha1-8aVAK6YiCtUswSgrrBrjqkn9Bho=", "dev": true, "license": "ISC", "dependencies": {"glob": "^7.1.3"}, "bin": {"rimraf": "bin.js"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/ripemd160": {"version": "2.0.2", "resolved": "http://r.npm.sankuai.com/ripemd160/download/ripemd160-2.0.2.tgz", "integrity": "sha1-ocGm9iR1FXe6XQeRTLyShQWFiQw=", "dev": true, "license": "MIT", "dependencies": {"hash-base": "^3.0.0", "inherits": "^2.0.1"}}, "node_modules/ripemd160/node_modules/hash-base": {"version": "3.1.0", "resolved": "http://r.npm.sankuai.com/hash-base/download/hash-base-3.1.0.tgz", "integrity": "sha1-VcOB2eBuHSmXqIO0o/3f5/DTrzM=", "dev": true, "license": "MIT", "dependencies": {"inherits": "^2.0.4", "readable-stream": "^3.6.0", "safe-buffer": "^5.2.0"}, "engines": {"node": ">=4"}}, "node_modules/run-parallel": {"version": "1.2.0", "resolved": "http://r.npm.sankuai.com/run-parallel/download/run-parallel-1.2.0.tgz", "integrity": "sha1-ZtE2jae9+SHrnZW9GpIp5/IaQ+4=", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"queue-microtask": "^1.2.2"}}, "node_modules/safe-buffer": {"version": "5.2.1", "resolved": "http://r.npm.sankuai.com/safe-buffer/download/safe-buffer-5.2.1.tgz", "integrity": "sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY=", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/safe-regex-test": {"version": "1.1.0", "resolved": "http://r.npm.sankuai.com/safe-regex-test/download/safe-regex-test-1.1.0.tgz", "integrity": "sha1-f4fftnoxUHguqvGFg/9dFxGsEME=", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "is-regex": "^1.2.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/safer-buffer": {"version": "2.1.2", "resolved": "http://r.npm.sankuai.com/safer-buffer/download/safer-buffer-2.1.2.tgz", "integrity": "sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=", "dev": true, "license": "MIT"}, "node_modules/schema-utils": {"version": "4.3.2", "resolved": "http://r.npm.sankuai.com/schema-utils/download/schema-utils-4.3.2.tgz", "integrity": "sha1-DBCHi/SnP9Kx39FLlGKyZ4jIBq4=", "dev": true, "license": "MIT", "dependencies": {"@types/json-schema": "^7.0.9", "ajv": "^8.9.0", "ajv-formats": "^2.1.1", "ajv-keywords": "^5.1.0"}, "engines": {"node": ">= 10.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}}, "node_modules/schema-utils/node_modules/ajv": {"version": "8.17.1", "resolved": "http://r.npm.sankuai.com/ajv/download/ajv-8.17.1.tgz", "integrity": "sha1-N9mlx3ava8ktf0+VEOukwKYNEaY=", "dev": true, "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.3", "fast-uri": "^3.0.1", "json-schema-traverse": "^1.0.0", "require-from-string": "^2.0.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/schema-utils/node_modules/ajv-formats": {"version": "2.1.1", "resolved": "http://r.npm.sankuai.com/ajv-formats/download/ajv-formats-2.1.1.tgz", "integrity": "sha1-bmaUAGWet0lzu/LjMycYCgmWtSA=", "dev": true, "license": "MIT", "dependencies": {"ajv": "^8.0.0"}, "peerDependencies": {"ajv": "^8.0.0"}, "peerDependenciesMeta": {"ajv": {"optional": true}}}, "node_modules/schema-utils/node_modules/ajv-keywords": {"version": "5.1.0", "resolved": "http://r.npm.sankuai.com/ajv-keywords/download/ajv-keywords-5.1.0.tgz", "integrity": "sha1-adTThaRzPNvqtElkoRcKiPh/DhY=", "dev": true, "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.3"}, "peerDependencies": {"ajv": "^8.8.2"}}, "node_modules/schema-utils/node_modules/json-schema-traverse": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/json-schema-traverse/download/json-schema-traverse-1.0.0.tgz", "integrity": "sha1-rnvLNlard6c7pcSb9lTzjmtoYOI=", "dev": true, "license": "MIT"}, "node_modules/select-hose": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/select-hose/download/select-hose-2.0.0.tgz", "integrity": "sha1-Yl2GWPhlr0Psliv8N2o3NZpJlMo=", "dev": true, "license": "MIT"}, "node_modules/selfsigned": {"version": "2.4.1", "resolved": "http://r.npm.sankuai.com/selfsigned/download/selfsigned-2.4.1.tgz", "integrity": "sha1-Vg2QVlRCo+01tnQDTOxOldzrSuA=", "dev": true, "license": "MIT", "dependencies": {"@types/node-forge": "^1.3.0", "node-forge": "^1"}, "engines": {"node": ">=10"}}, "node_modules/semver": {"version": "6.3.1", "resolved": "http://r.npm.sankuai.com/semver/download/semver-6.3.1.tgz", "integrity": "sha1-VW0u+GiRRuRtzqS/3QlfNDTf/LQ=", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/send": {"version": "0.19.0", "resolved": "http://r.npm.sankuai.com/send/download/send-0.19.0.tgz", "integrity": "sha1-u8WjiMjqbASJZwSdvqwOSj8J1/g=", "dev": true, "license": "MIT", "dependencies": {"debug": "2.6.9", "depd": "2.0.0", "destroy": "1.2.0", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "etag": "~1.8.1", "fresh": "0.5.2", "http-errors": "2.0.0", "mime": "1.6.0", "ms": "2.1.3", "on-finished": "2.4.1", "range-parser": "~1.2.1", "statuses": "2.0.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/send/node_modules/debug": {"version": "2.6.9", "resolved": "http://r.npm.sankuai.com/debug/download/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "dev": true, "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/send/node_modules/debug/node_modules/ms": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/ms/download/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true, "license": "MIT"}, "node_modules/send/node_modules/encodeurl": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/encodeurl/download/encodeurl-1.0.2.tgz", "integrity": "sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/serialize-javascript": {"version": "6.0.2", "resolved": "http://r.npm.sankuai.com/serialize-javascript/download/serialize-javascript-6.0.2.tgz", "integrity": "sha1-3voeBVyDv21Z6oBdjahiJU62psI=", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"randombytes": "^2.1.0"}}, "node_modules/serve-index": {"version": "1.9.1", "resolved": "http://r.npm.sankuai.com/serve-index/download/serve-index-1.9.1.tgz", "integrity": "sha1-03aNabHn2C5c4FD/9bRTvqEqkjk=", "dev": true, "license": "MIT", "dependencies": {"accepts": "~1.3.4", "batch": "0.6.1", "debug": "2.6.9", "escape-html": "~1.0.3", "http-errors": "~1.6.2", "mime-types": "~2.1.17", "parseurl": "~1.3.2"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/serve-index/node_modules/debug": {"version": "2.6.9", "resolved": "http://r.npm.sankuai.com/debug/download/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "dev": true, "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/serve-index/node_modules/depd": {"version": "1.1.2", "resolved": "http://r.npm.sankuai.com/depd/download/depd-1.1.2.tgz", "integrity": "sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/serve-index/node_modules/http-errors": {"version": "1.6.3", "resolved": "http://r.npm.sankuai.com/http-errors/download/http-errors-1.6.3.tgz", "integrity": "sha1-i1VoC7S+KDoLW/TqLjhYC+HZMg0=", "dev": true, "license": "MIT", "dependencies": {"depd": "~1.1.2", "inherits": "2.0.3", "setprototypeof": "1.1.0", "statuses": ">= 1.4.0 < 2"}, "engines": {"node": ">= 0.6"}}, "node_modules/serve-index/node_modules/inherits": {"version": "2.0.3", "resolved": "http://r.npm.sankuai.com/inherits/download/inherits-2.0.3.tgz", "integrity": "sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4=", "dev": true, "license": "ISC"}, "node_modules/serve-index/node_modules/ms": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/ms/download/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true, "license": "MIT"}, "node_modules/serve-index/node_modules/setprototypeof": {"version": "1.1.0", "resolved": "http://r.npm.sankuai.com/setprototypeof/download/setprototypeof-1.1.0.tgz", "integrity": "sha1-0L2FU2iHtv58DYGMuWLZ2RxU5lY=", "dev": true, "license": "ISC"}, "node_modules/serve-index/node_modules/statuses": {"version": "1.5.0", "resolved": "http://r.npm.sankuai.com/statuses/download/statuses-1.5.0.tgz", "integrity": "sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/serve-static": {"version": "1.16.2", "resolved": "http://r.npm.sankuai.com/serve-static/download/serve-static-1.16.2.tgz", "integrity": "sha1-tqU0PaR/a90mc4SL9FdUlB6AMpY=", "dev": true, "license": "MIT", "dependencies": {"encodeurl": "~2.0.0", "escape-html": "~1.0.3", "parseurl": "~1.3.3", "send": "0.19.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/set-function-length": {"version": "1.2.2", "resolved": "http://r.npm.sankuai.com/set-function-length/download/set-function-length-1.2.2.tgz", "integrity": "sha1-qscjFBmOrtl1z3eyw7a4gGleVEk=", "dev": true, "license": "MIT", "dependencies": {"define-data-property": "^1.1.4", "es-errors": "^1.3.0", "function-bind": "^1.1.2", "get-intrinsic": "^1.2.4", "gopd": "^1.0.1", "has-property-descriptors": "^1.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/setprototypeof": {"version": "1.2.0", "resolved": "http://r.npm.sankuai.com/setprototypeof/download/setprototypeof-1.2.0.tgz", "integrity": "sha1-ZsmiSnP5/CjL5msJ/tPTPcrxtCQ=", "dev": true, "license": "ISC"}, "node_modules/sha.js": {"version": "2.4.11", "resolved": "http://r.npm.sankuai.com/sha.js/download/sha.js-2.4.11.tgz", "integrity": "sha1-N6XPC4HsvGlD3hCbopYNGyZYSuc=", "dev": true, "license": "(MIT AND BSD-3-<PERSON><PERSON>)", "dependencies": {"inherits": "^2.0.1", "safe-buffer": "^5.0.1"}, "bin": {"sha.js": "bin.js"}}, "node_modules/shallow-clone": {"version": "3.0.1", "resolved": "http://r.npm.sankuai.com/shallow-clone/download/shallow-clone-3.0.1.tgz", "integrity": "sha1-jymBrZJTH1UDWwH7IwdppA4C76M=", "dev": true, "license": "MIT", "dependencies": {"kind-of": "^6.0.2"}, "engines": {"node": ">=8"}}, "node_modules/shebang-command": {"version": "1.2.0", "resolved": "http://r.npm.sankuai.com/shebang-command/download/shebang-command-1.2.0.tgz", "integrity": "sha1-RKrGW2lbAzmJaMOfNj/uXer98eo=", "dev": true, "license": "MIT", "dependencies": {"shebang-regex": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/shebang-regex": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/shebang-regex/download/shebang-regex-1.0.0.tgz", "integrity": "sha1-2kL0l0DAtC2yypcoVxyxkMmO/qM=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/shell-quote": {"version": "1.8.2", "resolved": "http://r.npm.sankuai.com/shell-quote/download/shell-quote-1.8.2.tgz", "integrity": "sha1-0tg+BXlZ1T7CYTEenpuPUdyyk0o=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel": {"version": "1.1.0", "resolved": "http://r.npm.sankuai.com/side-channel/download/side-channel-1.1.0.tgz", "integrity": "sha1-w/z/nE2pMnhIczNeyXZfqU/2a8k=", "dev": true, "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "object-inspect": "^1.13.3", "side-channel-list": "^1.0.0", "side-channel-map": "^1.0.1", "side-channel-weakmap": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-list": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/side-channel-list/download/side-channel-list-1.0.0.tgz", "integrity": "sha1-EMtZhCYxFdO3oOM2WR4pCoMK+K0=", "dev": true, "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "object-inspect": "^1.13.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-map": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/side-channel-map/download/side-channel-map-1.0.1.tgz", "integrity": "sha1-1rtrN5Asb+9RdOX1M/q0xzKib0I=", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-weakmap": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/side-channel-weakmap/download/side-channel-weakmap-1.0.2.tgz", "integrity": "sha1-Ed2hnVNo5Azp7CvcH7DsvAeQ7Oo=", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3", "side-channel-map": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/signal-exit": {"version": "3.0.7", "resolved": "http://r.npm.sankuai.com/signal-exit/download/signal-exit-3.0.7.tgz", "integrity": "sha1-qaF2f4r4QVURTqq9c/mSc8j1mtk=", "dev": true, "license": "ISC"}, "node_modules/sirv": {"version": "2.0.4", "resolved": "http://r.npm.sankuai.com/sirv/download/sirv-2.0.4.tgz", "integrity": "sha1-XdmnJcV4405EnzMnA+sqdORqKbA=", "dev": true, "license": "MIT", "dependencies": {"@polka/url": "^1.0.0-next.24", "mrmime": "^2.0.0", "totalist": "^3.0.0"}, "engines": {"node": ">= 10"}}, "node_modules/slash": {"version": "3.0.0", "resolved": "http://r.npm.sankuai.com/slash/download/slash-3.0.0.tgz", "integrity": "sha1-ZTm+hwwWWtvVJAIg2+Nh8bxNRjQ=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/slice-ansi": {"version": "4.0.0", "resolved": "http://r.npm.sankuai.com/slice-ansi/download/slice-ansi-4.0.0.tgz", "integrity": "sha1-UA6N0P1VsFgVCGJVsxla3ypF/ms=", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "astral-regex": "^2.0.0", "is-fullwidth-code-point": "^3.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/slice-ansi?sponsor=1"}}, "node_modules/sockjs": {"version": "0.3.24", "resolved": "http://r.npm.sankuai.com/sockjs/download/sockjs-0.3.24.tgz", "integrity": "sha1-ybyJlfM6ERvqA5XsMKoyBr21zM4=", "dev": true, "license": "MIT", "dependencies": {"faye-websocket": "^0.11.3", "uuid": "^8.3.2", "websocket-driver": "^0.7.4"}}, "node_modules/source-map": {"version": "0.6.1", "resolved": "http://r.npm.sankuai.com/source-map/download/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM=", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/source-map-js": {"version": "1.2.1", "resolved": "http://r.npm.sankuai.com/source-map-js/download/source-map-js-1.2.1.tgz", "integrity": "sha1-HOVlD93YerwJnto33P8CTCZnrkY=", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/source-map-support": {"version": "0.5.21", "resolved": "http://r.npm.sankuai.com/source-map-support/download/source-map-support-0.5.21.tgz", "integrity": "sha1-BP58f54e0tZiIzwoyys1ufY/bk8=", "dev": true, "license": "MIT", "dependencies": {"buffer-from": "^1.0.0", "source-map": "^0.6.0"}}, "node_modules/spdx-correct": {"version": "3.2.0", "resolved": "http://r.npm.sankuai.com/spdx-correct/download/spdx-correct-3.2.0.tgz", "integrity": "sha1-T1qwZo8AWeNPnADc4zF4ShLeTpw=", "dev": true, "license": "Apache-2.0", "dependencies": {"spdx-expression-parse": "^3.0.0", "spdx-license-ids": "^3.0.0"}}, "node_modules/spdx-exceptions": {"version": "2.5.0", "resolved": "http://r.npm.sankuai.com/spdx-exceptions/download/spdx-exceptions-2.5.0.tgz", "integrity": "sha1-XWB9J/yAb2bXtkp2ZlD6iQ8E7WY=", "dev": true, "license": "CC-BY-3.0"}, "node_modules/spdx-expression-parse": {"version": "3.0.1", "resolved": "http://r.npm.sankuai.com/spdx-expression-parse/download/spdx-expression-parse-3.0.1.tgz", "integrity": "sha1-z3D1BILu/cmOPOCmgz5KU87rpnk=", "dev": true, "license": "MIT", "dependencies": {"spdx-exceptions": "^2.1.0", "spdx-license-ids": "^3.0.0"}}, "node_modules/spdx-license-ids": {"version": "3.0.21", "resolved": "http://r.npm.sankuai.com/spdx-license-ids/download/spdx-license-ids-3.0.21.tgz", "integrity": "sha1-bW6YDJ3ytvyQU0OjstcCpiOVNsM=", "dev": true, "license": "CC0-1.0"}, "node_modules/spdy": {"version": "4.0.2", "resolved": "http://r.npm.sankuai.com/spdy/download/spdy-4.0.2.tgz", "integrity": "sha1-t09GYgOj7aRSwCSSuR+56EonZ3s=", "dev": true, "license": "MIT", "dependencies": {"debug": "^4.1.0", "handle-thing": "^2.0.0", "http-deceiver": "^1.2.7", "select-hose": "^2.0.0", "spdy-transport": "^3.0.0"}, "engines": {"node": ">=6.0.0"}}, "node_modules/spdy-transport": {"version": "3.0.0", "resolved": "http://r.npm.sankuai.com/spdy-transport/download/spdy-transport-3.0.0.tgz", "integrity": "sha1-ANSGOmQArXXfkzYaFghgXl3NzzE=", "dev": true, "license": "MIT", "dependencies": {"debug": "^4.1.0", "detect-node": "^2.0.4", "hpack.js": "^2.1.6", "obuf": "^1.1.2", "readable-stream": "^3.0.6", "wbuf": "^1.7.3"}}, "node_modules/sprintf-js": {"version": "1.0.3", "resolved": "http://r.npm.sankuai.com/sprintf-js/download/sprintf-js-1.0.3.tgz", "integrity": "sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw=", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/ssri": {"version": "8.0.1", "resolved": "http://r.npm.sankuai.com/ssri/download/ssri-8.0.1.tgz", "integrity": "sha1-Y45OQ54v+9LNKJd21cpFfE9Roq8=", "dev": true, "license": "ISC", "dependencies": {"minipass": "^3.1.1"}, "engines": {"node": ">= 8"}}, "node_modules/stable": {"version": "0.1.8", "resolved": "http://r.npm.sankuai.com/stable/download/stable-0.1.8.tgz", "integrity": "sha1-g26zyDgv4pNv6vVEYxAXzn1Ho88=", "dev": true, "license": "MIT"}, "node_modules/stackframe": {"version": "1.3.4", "resolved": "http://r.npm.sankuai.com/stackframe/download/stackframe-1.3.4.tgz", "integrity": "sha1-uIGgBMjBSaXo7+831RsW5BKUMxA=", "dev": true, "license": "MIT"}, "node_modules/statuses": {"version": "2.0.1", "resolved": "http://r.npm.sankuai.com/statuses/download/statuses-2.0.1.tgz", "integrity": "sha1-VcsADM8dSHKL0jxoWgY5mM8aG2M=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/stream-browserify": {"version": "3.0.0", "resolved": "http://r.npm.sankuai.com/stream-browserify/download/stream-browserify-3.0.0.tgz", "integrity": "sha1-IrCihQzfZQPnMIXaH8e30MISLy8=", "dev": true, "license": "MIT", "dependencies": {"inherits": "~2.0.4", "readable-stream": "^3.5.0"}}, "node_modules/string_decoder": {"version": "1.3.0", "resolved": "http://r.npm.sankuai.com/string_decoder/download/string_decoder-1.3.0.tgz", "integrity": "sha1-QvEUWUpGzxqOMLCoT1bHjD7awh4=", "dev": true, "license": "MIT", "dependencies": {"safe-buffer": "~5.2.0"}}, "node_modules/string-width": {"version": "4.2.3", "resolved": "http://r.npm.sankuai.com/string-width/download/string-width-4.2.3.tgz", "integrity": "sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=", "dev": true, "license": "MIT", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-ansi": {"version": "6.0.1", "resolved": "http://r.npm.sankuai.com/strip-ansi/download/strip-ansi-6.0.1.tgz", "integrity": "sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=", "dev": true, "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-eof": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/strip-eof/download/strip-eof-1.0.0.tgz", "integrity": "sha1-u0P/VZim6wXYm1n80SnJgzE2Br8=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/strip-final-newline": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/strip-final-newline/download/strip-final-newline-2.0.0.tgz", "integrity": "sha1-ibhS+y/L6Tb29LMYevsKEsGrWK0=", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/strip-indent": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/strip-indent/download/strip-indent-2.0.0.tgz", "integrity": "sha1-XvjbKV0B5u1sv3qrlpmNeCJSe2g=", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/strip-json-comments": {"version": "3.1.1", "resolved": "http://r.npm.sankuai.com/strip-json-comments/download/strip-json-comments-3.1.1.tgz", "integrity": "sha1-MfEoGzgyYwQ0gxwxDAHMzajL4AY=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/stylehacks": {"version": "5.1.1", "resolved": "http://r.npm.sankuai.com/stylehacks/download/stylehacks-5.1.1.tgz", "integrity": "sha1-eTSjTrWdcVIUn6adbp5W8vw0vMk=", "dev": true, "license": "MIT", "dependencies": {"browserslist": "^4.21.4", "postcss-selector-parser": "^6.0.4"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/supports-color": {"version": "7.2.0", "resolved": "http://r.npm.sankuai.com/supports-color/download/supports-color-7.2.0.tgz", "integrity": "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/supports-preserve-symlinks-flag": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/supports-preserve-symlinks-flag/download/supports-preserve-symlinks-flag-1.0.0.tgz", "integrity": "sha1-btpL00SjyUrqN21MwxvHcxEDngk=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/svg-tags": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/svg-tags/download/svg-tags-1.0.0.tgz", "integrity": "sha1-WPcc7jvVGbWdSyqEO2x95krAR2Q=", "dev": true}, "node_modules/svgo": {"version": "2.8.0", "resolved": "http://r.npm.sankuai.com/svgo/download/svgo-2.8.0.tgz", "integrity": "sha1-T/gMzmcQ3CeV8MfHQQHmdkz8zSQ=", "dev": true, "license": "MIT", "dependencies": {"@trysound/sax": "0.2.0", "commander": "^7.2.0", "css-select": "^4.1.3", "css-tree": "^1.1.3", "csso": "^4.2.0", "picocolors": "^1.0.0", "stable": "^0.1.8"}, "bin": {"svgo": "bin/svgo"}, "engines": {"node": ">=10.13.0"}}, "node_modules/table": {"version": "6.9.0", "resolved": "http://r.npm.sankuai.com/table/download/table-6.9.0.tgz", "integrity": "sha1-UAQK+mJkFBx1ZrO4HU2CxHqGaPU=", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"ajv": "^8.0.1", "lodash.truncate": "^4.4.2", "slice-ansi": "^4.0.0", "string-width": "^4.2.3", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=10.0.0"}}, "node_modules/table/node_modules/ajv": {"version": "8.17.1", "resolved": "http://r.npm.sankuai.com/ajv/download/ajv-8.17.1.tgz", "integrity": "sha1-N9mlx3ava8ktf0+VEOukwKYNEaY=", "dev": true, "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.3", "fast-uri": "^3.0.1", "json-schema-traverse": "^1.0.0", "require-from-string": "^2.0.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/table/node_modules/json-schema-traverse": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/json-schema-traverse/download/json-schema-traverse-1.0.0.tgz", "integrity": "sha1-rnvLNlard6c7pcSb9lTzjmtoYOI=", "dev": true, "license": "MIT"}, "node_modules/tapable": {"version": "2.2.1", "resolved": "http://r.npm.sankuai.com/tapable/download/tapable-2.2.1.tgz", "integrity": "sha1-GWenPvQGCoLxKrlq+G1S/bdu7KA=", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/terser": {"version": "5.39.2", "resolved": "http://r.npm.sankuai.com/terser/download/terser-5.39.2.tgz", "integrity": "sha1-WhYmAwckpnLi5bXJzZBwMIwg6Pk=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"@jridgewell/source-map": "^0.3.3", "acorn": "^8.14.0", "commander": "^2.20.0", "source-map-support": "~0.5.20"}, "bin": {"terser": "bin/terser"}, "engines": {"node": ">=10"}}, "node_modules/terser-webpack-plugin": {"version": "5.3.14", "resolved": "http://r.npm.sankuai.com/terser-webpack-plugin/download/terser-webpack-plugin-5.3.14.tgz", "integrity": "sha1-kDHUjlerJ1Z/AqzoXH1pDbZsPgY=", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/trace-mapping": "^0.3.25", "jest-worker": "^27.4.5", "schema-utils": "^4.3.0", "serialize-javascript": "^6.0.2", "terser": "^5.31.1"}, "engines": {"node": ">= 10.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"webpack": "^5.1.0"}, "peerDependenciesMeta": {"@swc/core": {"optional": true}, "esbuild": {"optional": true}, "uglify-js": {"optional": true}}}, "node_modules/terser/node_modules/commander": {"version": "2.20.3", "resolved": "http://r.npm.sankuai.com/commander/download/commander-2.20.3.tgz", "integrity": "sha1-/UhehMA+tIgcIHIrpIA16FMa6zM=", "dev": true, "license": "MIT"}, "node_modules/tesseract.js": {"version": "6.0.1", "resolved": "http://r.npm.sankuai.com/tesseract.js/download/tesseract.js-6.0.1.tgz", "integrity": "sha1-Wy/zmq6S1ZzveViaQ6Dzq5Y4Acw=", "hasInstallScript": true, "license": "Apache-2.0", "dependencies": {"bmp-js": "^0.1.0", "idb-keyval": "^6.2.0", "is-url": "^1.2.4", "node-fetch": "^2.6.9", "opencollective-postinstall": "^2.0.3", "regenerator-runtime": "^0.13.3", "tesseract.js-core": "^6.0.0", "wasm-feature-detect": "^1.2.11", "zlibjs": "^0.3.1"}}, "node_modules/tesseract.js-core": {"version": "6.0.0", "resolved": "http://r.npm.sankuai.com/tesseract.js-core/download/tesseract.js-core-6.0.0.tgz", "integrity": "sha1-byXalPcPjo8Cr/R6Q75h1J5vZ8M=", "license": "Apache-2.0"}, "node_modules/text-table": {"version": "0.2.0", "resolved": "http://r.npm.sankuai.com/text-table/download/text-table-0.2.0.tgz", "integrity": "sha1-f17oI66AUgfACvLfSoTsP8+lcLQ=", "dev": true, "license": "MIT"}, "node_modules/thenify": {"version": "3.3.1", "resolved": "http://r.npm.sankuai.com/thenify/download/thenify-3.3.1.tgz", "integrity": "sha1-iTLmhqQGYDigFt2eLKRq3Zg4qV8=", "dev": true, "license": "MIT", "dependencies": {"any-promise": "^1.0.0"}}, "node_modules/thenify-all": {"version": "1.6.0", "resolved": "http://r.npm.sankuai.com/thenify-all/download/thenify-all-1.6.0.tgz", "integrity": "sha1-GhkY1ALY/D+Y+/I02wvMjMEOlyY=", "dev": true, "license": "MIT", "dependencies": {"thenify": ">= 3.1.0 < 4"}, "engines": {"node": ">=0.8"}}, "node_modules/thread-loader": {"version": "3.0.4", "resolved": "http://r.npm.sankuai.com/thread-loader/download/thread-loader-3.0.4.tgz", "integrity": "sha1-w5LkwCQfvIBDDraA5IhoGbUEoxs=", "dev": true, "license": "MIT", "dependencies": {"json-parse-better-errors": "^1.0.2", "loader-runner": "^4.1.0", "loader-utils": "^2.0.0", "neo-async": "^2.6.2", "schema-utils": "^3.0.0"}, "engines": {"node": ">= 10.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"webpack": "^4.27.0 || ^5.0.0"}}, "node_modules/thread-loader/node_modules/loader-utils": {"version": "2.0.4", "resolved": "http://r.npm.sankuai.com/loader-utils/download/loader-utils-2.0.4.tgz", "integrity": "sha1-i1yzi1w0qaAY7h/A5qBm0d/MUow=", "dev": true, "license": "MIT", "dependencies": {"big.js": "^5.2.2", "emojis-list": "^3.0.0", "json5": "^2.1.2"}, "engines": {"node": ">=8.9.0"}}, "node_modules/thread-loader/node_modules/schema-utils": {"version": "3.3.0", "resolved": "http://r.npm.sankuai.com/schema-utils/download/schema-utils-3.3.0.tgz", "integrity": "sha1-9QqIh3w8AWUqFbYirp6Xld96YP4=", "dev": true, "license": "MIT", "dependencies": {"@types/json-schema": "^7.0.8", "ajv": "^6.12.5", "ajv-keywords": "^3.5.2"}, "engines": {"node": ">= 10.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}}, "node_modules/throttle-debounce": {"version": "1.1.0", "resolved": "http://r.npm.sankuai.com/throttle-debounce/download/throttle-debounce-1.1.0.tgz", "integrity": "sha1-UYU9o3vmihVctugns1FKPEIuic0=", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/thunky": {"version": "1.1.0", "resolved": "http://r.npm.sankuai.com/thunky/download/thunky-1.1.0.tgz", "integrity": "sha1-Wrr3FKlAXbBQRzK7zNLO3Z75U30=", "dev": true, "license": "MIT"}, "node_modules/to-regex-range": {"version": "5.0.1", "resolved": "http://r.npm.sankuai.com/to-regex-range/download/to-regex-range-5.0.1.tgz", "integrity": "sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=", "dev": true, "license": "MIT", "dependencies": {"is-number": "^7.0.0"}, "engines": {"node": ">=8.0"}}, "node_modules/toidentifier": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/toidentifier/download/toidentifier-1.0.1.tgz", "integrity": "sha1-O+NDIaiKgg7RvYDfqjPkefu43TU=", "dev": true, "license": "MIT", "engines": {"node": ">=0.6"}}, "node_modules/totalist": {"version": "3.0.1", "resolved": "http://r.npm.sankuai.com/totalist/download/totalist-3.0.1.tgz", "integrity": "sha1-ujo9YAyRWxqXhyNI95wSdHX2rPg=", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/tr46": {"version": "0.0.3", "resolved": "http://r.npm.sankuai.com/tr46/download/tr46-0.0.3.tgz", "integrity": "sha1-gYT9NH2snNwYWZLzpmIuFLnZq2o=", "license": "MIT"}, "node_modules/tslib": {"version": "2.8.1", "resolved": "http://r.npm.sankuai.com/tslib/download/tslib-2.8.1.tgz", "integrity": "sha1-YS7+TtI11Wfoq6Xypfq3AoCt6D8=", "dev": true, "license": "0BSD"}, "node_modules/type-check": {"version": "0.4.0", "resolved": "http://r.npm.sankuai.com/type-check/download/type-check-0.4.0.tgz", "integrity": "sha1-B7ggO/pwVsBlcFDjzNLDdzC6uPE=", "dev": true, "license": "MIT", "dependencies": {"prelude-ls": "^1.2.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/type-fest": {"version": "0.20.2", "resolved": "http://r.npm.sankuai.com/type-fest/download/type-fest-0.20.2.tgz", "integrity": "sha1-G/IH9LKPkVg2ZstfvTJ4hzAc1fQ=", "dev": true, "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/type-is": {"version": "1.6.18", "resolved": "http://r.npm.sankuai.com/type-is/download/type-is-1.6.18.tgz", "integrity": "sha1-TlUs0F3wlGfcvE73Od6J8s83wTE=", "dev": true, "license": "MIT", "dependencies": {"media-typer": "0.3.0", "mime-types": "~2.1.24"}, "engines": {"node": ">= 0.6"}}, "node_modules/undici-types": {"version": "6.21.0", "resolved": "http://r.npm.sankuai.com/undici-types/download/undici-types-6.21.0.tgz", "integrity": "sha1-aR0ArzkJvpOn+qE75hs6W1DvEss=", "license": "MIT"}, "node_modules/unicode-canonical-property-names-ecmascript": {"version": "2.0.1", "resolved": "http://r.npm.sankuai.com/unicode-canonical-property-names-ecmascript/download/unicode-canonical-property-names-ecmascript-2.0.1.tgz", "integrity": "sha1-yzFz/kfKdD4ighbko93EyE1ijMI=", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/unicode-match-property-ecmascript": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/unicode-match-property-ecmascript/download/unicode-match-property-ecmascript-2.0.0.tgz", "integrity": "sha1-VP0W4OyxZ88Ezx91a9zJLrp5dsM=", "dev": true, "license": "MIT", "dependencies": {"unicode-canonical-property-names-ecmascript": "^2.0.0", "unicode-property-aliases-ecmascript": "^2.0.0"}, "engines": {"node": ">=4"}}, "node_modules/unicode-match-property-value-ecmascript": {"version": "2.2.0", "resolved": "http://r.npm.sankuai.com/unicode-match-property-value-ecmascript/download/unicode-match-property-value-ecmascript-2.2.0.tgz", "integrity": "sha1-oEAa7nJxRZj3ObaLEE5P46DLPHE=", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/unicode-property-aliases-ecmascript": {"version": "2.1.0", "resolved": "http://r.npm.sankuai.com/unicode-property-aliases-ecmascript/download/unicode-property-aliases-ecmascript-2.1.0.tgz", "integrity": "sha1-Q9QeO+aYvUk++REHfJsTH4J+jM0=", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/universalify": {"version": "2.0.1", "resolved": "http://r.npm.sankuai.com/universalify/download/universalify-2.0.1.tgz", "integrity": "sha1-Fo78IYCWTmOG0GHglN9hr+I5sY0=", "dev": true, "license": "MIT", "engines": {"node": ">= 10.0.0"}}, "node_modules/unpipe": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/unpipe/download/unpipe-1.0.0.tgz", "integrity": "sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/update-browserslist-db": {"version": "1.1.3", "resolved": "http://r.npm.sankuai.com/update-browserslist-db/download/update-browserslist-db-1.1.3.tgz", "integrity": "sha1-NIN33SRSFvnnBg/1CxWht0C3VCA=", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"escalade": "^3.2.0", "picocolors": "^1.1.1"}, "bin": {"update-browserslist-db": "cli.js"}, "peerDependencies": {"browserslist": ">= 4.21.0"}}, "node_modules/uri-js": {"version": "4.4.1", "resolved": "http://r.npm.sankuai.com/uri-js/download/uri-js-4.4.1.tgz", "integrity": "sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"punycode": "^2.1.0"}}, "node_modules/util": {"version": "0.12.5", "resolved": "http://r.npm.sankuai.com/util/download/util-0.12.5.tgz", "integrity": "sha1-XxemBZtz22GodWaHgaHCsTa9b7w=", "dev": true, "license": "MIT", "dependencies": {"inherits": "^2.0.3", "is-arguments": "^1.0.4", "is-generator-function": "^1.0.7", "is-typed-array": "^1.1.3", "which-typed-array": "^1.1.2"}}, "node_modules/util-deprecate": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/util-deprecate/download/util-deprecate-1.0.2.tgz", "integrity": "sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=", "dev": true, "license": "MIT"}, "node_modules/utila": {"version": "0.4.0", "resolved": "http://r.npm.sankuai.com/utila/download/utila-0.4.0.tgz", "integrity": "sha1-ihagXURWV6Oupe7MWxKk+lN5dyw=", "dev": true, "license": "MIT"}, "node_modules/utils-merge": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/utils-merge/download/utils-merge-1.0.1.tgz", "integrity": "sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4.0"}}, "node_modules/uuid": {"version": "8.3.2", "resolved": "http://r.npm.sankuai.com/uuid/download/uuid-8.3.2.tgz", "integrity": "sha1-gNW1ztJxu5r2xEXyGhoExgbO++I=", "dev": true, "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/v8-compile-cache": {"version": "2.4.0", "resolved": "http://r.npm.sankuai.com/v8-compile-cache/download/v8-compile-cache-2.4.0.tgz", "integrity": "sha1-za2ovsYeFYZfBdCXxfT9MOlNwSg=", "dev": true, "license": "MIT"}, "node_modules/validate-npm-package-license": {"version": "3.0.4", "resolved": "http://r.npm.sankuai.com/validate-npm-package-license/download/validate-npm-package-license-3.0.4.tgz", "integrity": "sha1-/JH2uce6FchX9MssXe/uw51PQQo=", "dev": true, "license": "Apache-2.0", "dependencies": {"spdx-correct": "^3.0.0", "spdx-expression-parse": "^3.0.0"}}, "node_modules/vary": {"version": "1.1.2", "resolved": "http://r.npm.sankuai.com/vary/download/vary-1.1.2.tgz", "integrity": "sha1-IpnwLG3tMNSllhsLn3RSShj2NPw=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/vue": {"version": "2.7.16", "resolved": "http://r.npm.sankuai.com/vue/download/vue-2.7.16.tgz", "integrity": "sha1-mMYN6d75nA49qNrlmzBOrUO5Z8k=", "license": "MIT", "dependencies": {"@vue/compiler-sfc": "2.7.16", "csstype": "^3.1.0"}}, "node_modules/vue-eslint-parser": {"version": "8.3.0", "resolved": "http://r.npm.sankuai.com/vue-eslint-parser/download/vue-eslint-parser-8.3.0.tgz", "integrity": "sha1-XTESmhs92JwAacoKHIj5cMNgvQ0=", "dev": true, "license": "MIT", "dependencies": {"debug": "^4.3.2", "eslint-scope": "^7.0.0", "eslint-visitor-keys": "^3.1.0", "espree": "^9.0.0", "esquery": "^1.4.0", "lodash": "^4.17.21", "semver": "^7.3.5"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://github.com/sponsors/mysticatea"}, "peerDependencies": {"eslint": ">=6.0.0"}}, "node_modules/vue-eslint-parser/node_modules/eslint-scope": {"version": "7.2.2", "resolved": "http://r.npm.sankuai.com/eslint-scope/download/eslint-scope-7.2.2.tgz", "integrity": "sha1-3rT5JWM5DzIAaJSvYqItuhxGQj8=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^5.2.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/vue-eslint-parser/node_modules/eslint-visitor-keys": {"version": "3.4.3", "resolved": "http://r.npm.sankuai.com/eslint-visitor-keys/download/eslint-visitor-keys-3.4.3.tgz", "integrity": "sha1-DNcv6FUOPC6uFWqWpN3c0cisWAA=", "dev": true, "license": "Apache-2.0", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/vue-eslint-parser/node_modules/espree": {"version": "9.6.1", "resolved": "http://r.npm.sankuai.com/espree/download/espree-9.6.1.tgz", "integrity": "sha1-oqF7jkNGkKVDLy+AGM5x0zGkjG8=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"acorn": "^8.9.0", "acorn-jsx": "^5.3.2", "eslint-visitor-keys": "^3.4.1"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/vue-eslint-parser/node_modules/semver": {"version": "7.7.2", "resolved": "http://r.npm.sankuai.com/semver/download/semver-7.7.2.tgz", "integrity": "sha1-Z9mf3NNc7CHm+Lh6f9UVoz+YK1g=", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/vue-hot-reload-api": {"version": "2.3.4", "resolved": "http://r.npm.sankuai.com/vue-hot-reload-api/download/vue-hot-reload-api-2.3.4.tgz", "integrity": "sha1-UylVzB6yCKPZkLOp+acFdGV+CPI=", "dev": true, "license": "MIT"}, "node_modules/vue-loader": {"version": "17.4.2", "resolved": "http://r.npm.sankuai.com/vue-loader/download/vue-loader-17.4.2.tgz", "integrity": "sha1-+H8Nit/LvoYj3p66GXnUG6Ijxto=", "dev": true, "license": "MIT", "dependencies": {"chalk": "^4.1.0", "hash-sum": "^2.0.0", "watchpack": "^2.4.0"}, "peerDependencies": {"webpack": "^4.1.0 || ^5.0.0-0"}, "peerDependenciesMeta": {"@vue/compiler-sfc": {"optional": true}, "vue": {"optional": true}}}, "node_modules/vue-loader/node_modules/hash-sum": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/hash-sum/download/hash-sum-2.0.0.tgz", "integrity": "sha1-gdAbtd6OpKIUrV1urRtSNGCwtFo=", "dev": true, "license": "MIT"}, "node_modules/vue-router": {"version": "3.6.5", "resolved": "http://r.npm.sankuai.com/vue-router/download/vue-router-3.6.5.tgz", "integrity": "sha1-lYR9Urmn4/E2HLYFyOZEHyAq+tg=", "license": "MIT"}, "node_modules/vue-style-loader": {"version": "4.1.3", "resolved": "http://r.npm.sankuai.com/vue-style-loader/download/vue-style-loader-4.1.3.tgz", "integrity": "sha1-bVWGOlH6dXqyTonZNxRlByqnvDU=", "dev": true, "license": "MIT", "dependencies": {"hash-sum": "^1.0.2", "loader-utils": "^1.0.2"}}, "node_modules/vue-template-compiler": {"version": "2.7.16", "resolved": "http://r.npm.sankuai.com/vue-template-compiler/download/vue-template-compiler-2.7.16.tgz", "integrity": "sha1-yBstR3UyZMd6wDuZZqRmN0grsDs=", "dev": true, "license": "MIT", "dependencies": {"de-indent": "^1.0.2", "he": "^1.2.0"}}, "node_modules/vue-template-es2015-compiler": {"version": "1.9.1", "resolved": "http://r.npm.sankuai.com/vue-template-es2015-compiler/download/vue-template-es2015-compiler-1.9.1.tgz", "integrity": "sha1-HuO8mhbsv1EYvjNLsV+cRvgvWCU=", "dev": true, "license": "MIT"}, "node_modules/vuex": {"version": "3.6.2", "resolved": "http://r.npm.sankuai.com/vuex/download/vuex-3.6.2.tgz", "integrity": "sha1-I2vAhqhww655lG8QfxbeWdWJXnE=", "license": "MIT", "peerDependencies": {"vue": "^2.0.0"}}, "node_modules/wasm-feature-detect": {"version": "1.8.0", "resolved": "http://r.npm.sankuai.com/wasm-feature-detect/download/wasm-feature-detect-1.8.0.tgz", "integrity": "sha1-Tp9VsKZNgB83L7sDJO0RrTq9DHg=", "license": "Apache-2.0"}, "node_modules/watchpack": {"version": "2.4.2", "resolved": "http://r.npm.sankuai.com/watchpack/download/watchpack-2.4.2.tgz", "integrity": "sha1-L+6u1nQS58MxhOWnnKc4+9OFZNo=", "dev": true, "license": "MIT", "dependencies": {"glob-to-regexp": "^0.4.1", "graceful-fs": "^4.1.2"}, "engines": {"node": ">=10.13.0"}}, "node_modules/wbuf": {"version": "1.7.3", "resolved": "http://r.npm.sankuai.com/wbuf/download/wbuf-1.7.3.tgz", "integrity": "sha1-wdjRSTFtPqhShIiVy2oL/oh7h98=", "dev": true, "license": "MIT", "dependencies": {"minimalistic-assert": "^1.0.0"}}, "node_modules/wcwidth": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/wcwidth/download/wcwidth-1.0.1.tgz", "integrity": "sha1-8LDc+RW8X/FSivrbLA4XtTLaL+g=", "dev": true, "license": "MIT", "dependencies": {"defaults": "^1.0.3"}}, "node_modules/web-streams-polyfill": {"version": "4.0.0-beta.3", "resolved": "http://r.npm.sankuai.com/web-streams-polyfill/download/web-streams-polyfill-4.0.0-beta.3.tgz", "integrity": "sha1-KJhIa3T1FWCV5HPv6Ync8YUEejg=", "license": "MIT", "engines": {"node": ">= 14"}}, "node_modules/webidl-conversions": {"version": "3.0.1", "resolved": "http://r.npm.sankuai.com/webidl-conversions/download/webidl-conversions-3.0.1.tgz", "integrity": "sha1-JFNCdeKnvGvnvIZhHMFq4KVlSHE=", "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/webpack": {"version": "5.99.8", "resolved": "http://r.npm.sankuai.com/webpack/download/webpack-5.99.8.tgz", "integrity": "sha1-3TGgILfAktMMTG2aTtuVgJ5/WUY=", "dev": true, "license": "MIT", "dependencies": {"@types/eslint-scope": "^3.7.7", "@types/estree": "^1.0.6", "@types/json-schema": "^7.0.15", "@webassemblyjs/ast": "^1.14.1", "@webassemblyjs/wasm-edit": "^1.14.1", "@webassemblyjs/wasm-parser": "^1.14.1", "acorn": "^8.14.0", "browserslist": "^4.24.0", "chrome-trace-event": "^1.0.2", "enhanced-resolve": "^5.17.1", "es-module-lexer": "^1.2.1", "eslint-scope": "5.1.1", "events": "^3.2.0", "glob-to-regexp": "^0.4.1", "graceful-fs": "^4.2.11", "json-parse-even-better-errors": "^2.3.1", "loader-runner": "^4.2.0", "mime-types": "^2.1.27", "neo-async": "^2.6.2", "schema-utils": "^4.3.2", "tapable": "^2.1.1", "terser-webpack-plugin": "^5.3.11", "watchpack": "^2.4.1", "webpack-sources": "^3.2.3"}, "bin": {"webpack": "bin/webpack.js"}, "engines": {"node": ">=10.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependenciesMeta": {"webpack-cli": {"optional": true}}}, "node_modules/webpack-bundle-analyzer": {"version": "4.10.2", "resolved": "http://r.npm.sankuai.com/webpack-bundle-analyzer/download/webpack-bundle-analyzer-4.10.2.tgz", "integrity": "sha1-YzryhiwhNzC+Pb30BFbbFxtg1b0=", "dev": true, "license": "MIT", "dependencies": {"@discoveryjs/json-ext": "0.5.7", "acorn": "^8.0.4", "acorn-walk": "^8.0.0", "commander": "^7.2.0", "debounce": "^1.2.1", "escape-string-regexp": "^4.0.0", "gzip-size": "^6.0.0", "html-escaper": "^2.0.2", "opener": "^1.5.2", "picocolors": "^1.0.0", "sirv": "^2.0.3", "ws": "^7.3.1"}, "bin": {"webpack-bundle-analyzer": "lib/bin/analyzer.js"}, "engines": {"node": ">= 10.13.0"}}, "node_modules/webpack-bundle-analyzer/node_modules/escape-string-regexp": {"version": "4.0.0", "resolved": "http://r.npm.sankuai.com/escape-string-regexp/download/escape-string-regexp-4.0.0.tgz", "integrity": "sha1-FLqDpdNz49MR5a/KKc9b+tllvzQ=", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/webpack-bundle-analyzer/node_modules/ws": {"version": "7.5.10", "resolved": "http://r.npm.sankuai.com/ws/download/ws-7.5.10.tgz", "integrity": "sha1-WLXCDcKBYz9sGRE/ObNJvYvVWNk=", "dev": true, "license": "MIT", "engines": {"node": ">=8.3.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}}, "node_modules/webpack-chain": {"version": "6.5.1", "resolved": "http://r.npm.sankuai.com/webpack-chain/download/webpack-chain-6.5.1.tgz", "integrity": "sha1-TycoTLu2N+PI+970Pu9YjU2GEgY=", "dev": true, "license": "MPL-2.0", "dependencies": {"deepmerge": "^1.5.2", "javascript-stringify": "^2.0.1"}, "engines": {"node": ">=8"}}, "node_modules/webpack-dev-middleware": {"version": "5.3.4", "resolved": "http://r.npm.sankuai.com/webpack-dev-middleware/download/webpack-dev-middleware-5.3.4.tgz", "integrity": "sha1-63s5KBy84Q4QTrK4vytj/OSaNRc=", "dev": true, "license": "MIT", "dependencies": {"colorette": "^2.0.10", "memfs": "^3.4.3", "mime-types": "^2.1.31", "range-parser": "^1.2.1", "schema-utils": "^4.0.0"}, "engines": {"node": ">= 12.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"webpack": "^4.0.0 || ^5.0.0"}}, "node_modules/webpack-dev-server": {"version": "4.15.2", "resolved": "http://r.npm.sankuai.com/webpack-dev-server/download/webpack-dev-server-4.15.2.tgz", "integrity": "sha1-ngxwpCoBJWCGCtsYaYbaEkgzMXM=", "dev": true, "license": "MIT", "dependencies": {"@types/bonjour": "^3.5.9", "@types/connect-history-api-fallback": "^1.3.5", "@types/express": "^4.17.13", "@types/serve-index": "^1.9.1", "@types/serve-static": "^1.13.10", "@types/sockjs": "^0.3.33", "@types/ws": "^8.5.5", "ansi-html-community": "^0.0.8", "bonjour-service": "^1.0.11", "chokidar": "^3.5.3", "colorette": "^2.0.10", "compression": "^1.7.4", "connect-history-api-fallback": "^2.0.0", "default-gateway": "^6.0.3", "express": "^4.17.3", "graceful-fs": "^4.2.6", "html-entities": "^2.3.2", "http-proxy-middleware": "^2.0.3", "ipaddr.js": "^2.0.1", "launch-editor": "^2.6.0", "open": "^8.0.9", "p-retry": "^4.5.0", "rimraf": "^3.0.2", "schema-utils": "^4.0.0", "selfsigned": "^2.1.1", "serve-index": "^1.9.1", "sockjs": "^0.3.24", "spdy": "^4.0.2", "webpack-dev-middleware": "^5.3.4", "ws": "^8.13.0"}, "bin": {"webpack-dev-server": "bin/webpack-dev-server.js"}, "engines": {"node": ">= 12.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"webpack": "^4.37.0 || ^5.0.0"}, "peerDependenciesMeta": {"webpack": {"optional": true}, "webpack-cli": {"optional": true}}}, "node_modules/webpack-merge": {"version": "5.10.0", "resolved": "http://r.npm.sankuai.com/webpack-merge/download/webpack-merge-5.10.0.tgz", "integrity": "sha1-o61ddzJB6caCgDq/Yo1M1iuKQXc=", "dev": true, "license": "MIT", "dependencies": {"clone-deep": "^4.0.1", "flat": "^5.0.2", "wildcard": "^2.0.0"}, "engines": {"node": ">=10.0.0"}}, "node_modules/webpack-sources": {"version": "3.2.3", "resolved": "http://r.npm.sankuai.com/webpack-sources/download/webpack-sources-3.2.3.tgz", "integrity": "sha1-LU2quEUf1LJAzCcFX/agwszqDN4=", "dev": true, "license": "MIT", "engines": {"node": ">=10.13.0"}}, "node_modules/webpack-virtual-modules": {"version": "0.4.6", "resolved": "http://r.npm.sankuai.com/webpack-virtual-modules/download/webpack-virtual-modules-0.4.6.tgz", "integrity": "sha1-PkAIIwcx8dsHjZy29ouvhXEYK0U=", "dev": true, "license": "MIT"}, "node_modules/websocket-driver": {"version": "0.7.4", "resolved": "http://r.npm.sankuai.com/websocket-driver/download/websocket-driver-0.7.4.tgz", "integrity": "sha1-ia1Slbv2S0gKvLox5JU6ynBvV2A=", "dev": true, "license": "Apache-2.0", "dependencies": {"http-parser-js": ">=0.5.1", "safe-buffer": ">=5.1.0", "websocket-extensions": ">=0.1.1"}, "engines": {"node": ">=0.8.0"}}, "node_modules/websocket-extensions": {"version": "0.1.4", "resolved": "http://r.npm.sankuai.com/websocket-extensions/download/websocket-extensions-0.1.4.tgz", "integrity": "sha1-f4RzvIOd/YdgituV1+sHUhFXikI=", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=0.8.0"}}, "node_modules/whatwg-fetch": {"version": "3.6.20", "resolved": "http://r.npm.sankuai.com/whatwg-fetch/download/whatwg-fetch-3.6.20.tgz", "integrity": "sha1-WAzm15H6zskdN8cokJlaC0jTHHA=", "dev": true, "license": "MIT"}, "node_modules/whatwg-url": {"version": "5.0.0", "resolved": "http://r.npm.sankuai.com/whatwg-url/download/whatwg-url-5.0.0.tgz", "integrity": "sha1-lmRU6HZUYuN2RNNib2dCzotwll0=", "license": "MIT", "dependencies": {"tr46": "~0.0.3", "webidl-conversions": "^3.0.0"}}, "node_modules/which": {"version": "1.3.1", "resolved": "http://r.npm.sankuai.com/which/download/which-1.3.1.tgz", "integrity": "sha1-pFBD1U9YBTFtqNYvn1CRjT2nCwo=", "dev": true, "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"which": "bin/which"}}, "node_modules/which-typed-array": {"version": "1.1.19", "resolved": "http://r.npm.sankuai.com/which-typed-array/download/which-typed-array-1.1.19.tgz", "integrity": "sha1-3wOELocLa4jhF1JKSzZLb8aJ+VY=", "dev": true, "license": "MIT", "dependencies": {"available-typed-arrays": "^1.0.7", "call-bind": "^1.0.8", "call-bound": "^1.0.4", "for-each": "^0.3.5", "get-proto": "^1.0.1", "gopd": "^1.2.0", "has-tostringtag": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/wildcard": {"version": "2.0.1", "resolved": "http://r.npm.sankuai.com/wildcard/download/wildcard-2.0.1.tgz", "integrity": "sha1-WrENAkhxmJVINrY0n3T/+WHhD2c=", "dev": true, "license": "MIT"}, "node_modules/word-wrap": {"version": "1.2.5", "resolved": "http://r.npm.sankuai.com/word-wrap/download/word-wrap-1.2.5.tgz", "integrity": "sha1-0sRcbdT7zmIaZvE2y+Mor9BBCzQ=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/wrap-ansi": {"version": "7.0.0", "resolved": "http://r.npm.sankuai.com/wrap-ansi/download/wrap-ansi-7.0.0.tgz", "integrity": "sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM=", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/wrappy": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/wrappy/download/wrappy-1.0.2.tgz", "integrity": "sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=", "dev": true, "license": "ISC"}, "node_modules/ws": {"version": "8.18.2", "resolved": "http://r.npm.sankuai.com/ws/download/ws-8.18.2.tgz", "integrity": "sha1-QnOLK+V87YX0YVQyCqu1GrADcFo=", "devOptional": true, "license": "MIT", "engines": {"node": ">=10.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": ">=5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}}, "node_modules/y18n": {"version": "5.0.8", "resolved": "http://r.npm.sankuai.com/y18n/download/y18n-5.0.8.tgz", "integrity": "sha1-f0k00PfKjFb5UxSTndzS3ZHOHVU=", "dev": true, "license": "ISC", "engines": {"node": ">=10"}}, "node_modules/yallist": {"version": "4.0.0", "resolved": "http://r.npm.sankuai.com/yallist/download/yallist-4.0.0.tgz", "integrity": "sha1-m7knkNnA7/7GO+c1GeEaNQGaOnI=", "dev": true, "license": "ISC"}, "node_modules/yaml": {"version": "1.10.2", "resolved": "http://r.npm.sankuai.com/yaml/download/yaml-1.10.2.tgz", "integrity": "sha1-IwHF/78StGfejaIzOkWeKeeSDks=", "dev": true, "license": "ISC", "engines": {"node": ">= 6"}}, "node_modules/yargs": {"version": "16.2.0", "resolved": "http://r.npm.sankuai.com/yargs/download/yargs-16.2.0.tgz", "integrity": "sha1-HIK/D2tqZur85+8w43b0mhJHf2Y=", "dev": true, "license": "MIT", "dependencies": {"cliui": "^7.0.2", "escalade": "^3.1.1", "get-caller-file": "^2.0.5", "require-directory": "^2.1.1", "string-width": "^4.2.0", "y18n": "^5.0.5", "yargs-parser": "^20.2.2"}, "engines": {"node": ">=10"}}, "node_modules/yargs-parser": {"version": "20.2.9", "resolved": "http://r.npm.sankuai.com/yargs-parser/download/yargs-parser-20.2.9.tgz", "integrity": "sha1-LrfcOwKJcY/ClfNidThFxBoMlO4=", "dev": true, "license": "ISC", "engines": {"node": ">=10"}}, "node_modules/yorkie": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/yorkie/download/yorkie-2.0.0.tgz", "integrity": "sha1-kkEZEtQ1IU4SxRwq4Qk+VLa7g9k=", "dev": true, "hasInstallScript": true, "license": "MIT", "dependencies": {"execa": "^0.8.0", "is-ci": "^1.0.10", "normalize-path": "^1.0.0", "strip-indent": "^2.0.0"}, "engines": {"node": ">=4"}}, "node_modules/yorkie/node_modules/cross-spawn": {"version": "5.1.0", "resolved": "http://r.npm.sankuai.com/cross-spawn/download/cross-spawn-5.1.0.tgz", "integrity": "sha1-6L0O/uWPz/b4+UUQoKVUu/ojVEk=", "dev": true, "license": "MIT", "dependencies": {"lru-cache": "^4.0.1", "shebang-command": "^1.2.0", "which": "^1.2.9"}}, "node_modules/yorkie/node_modules/execa": {"version": "0.8.0", "resolved": "http://r.npm.sankuai.com/execa/download/execa-0.8.0.tgz", "integrity": "sha1-2NdrvBtVIX7RkP1t1J08d07PyNo=", "dev": true, "license": "MIT", "dependencies": {"cross-spawn": "^5.0.1", "get-stream": "^3.0.0", "is-stream": "^1.1.0", "npm-run-path": "^2.0.0", "p-finally": "^1.0.0", "signal-exit": "^3.0.0", "strip-eof": "^1.0.0"}, "engines": {"node": ">=4"}}, "node_modules/yorkie/node_modules/get-stream": {"version": "3.0.0", "resolved": "http://r.npm.sankuai.com/get-stream/download/get-stream-3.0.0.tgz", "integrity": "sha1-jpQ9E1jcN1VQVOy+LtsFqhdO3hQ=", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/yorkie/node_modules/normalize-path": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/normalize-path/download/normalize-path-1.0.0.tgz", "integrity": "sha1-MtDkcvkf80VwHBWoMRAY07CpA3k=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/zlibjs": {"version": "0.3.1", "resolved": "http://r.npm.sankuai.com/zlibjs/download/zlibjs-0.3.1.tgz", "integrity": "sha1-UBl+2yihxCymWcyLTmqd3W1ERVQ=", "license": "MIT", "engines": {"node": "*"}}}}