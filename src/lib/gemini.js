import OpenAI from 'openai';

const GEMINI_API_KEY = process.env.VUE_APP_GEMINI_API_KEY;
const GEMINI_BASE_URL = process.env.VUE_APP_GEMINI_BASE_URL;
const GEMINI_MODEL = process.env.VUE_APP_GEMINI_MODEL || "gpt-4.1";

let openai = null;
if (!GEMINI_API_KEY || !GEMINI_BASE_URL) {
  console.warn(
    'Gemini API Key or Base URL is not configured. Please set VUE_APP_GEMINI_API_KEY and VUE_APP_GEMINI_BASE_URL in your .env file. API calls will likely fail.'
  );
} else {
  try {
    openai = new OpenAI({
      apiKey: GEMINI_API_KEY,
      baseURL: GEMINI_BASE_URL,
      dangerouslyAllowBrowser: true
    });
    console.log('Gemini client initialized with environment variables.');
  } catch (error) {
    console.error("Failed to initialize Gemini OpenAI client:", error);
    openai = null;
  }
}

// 实际调用 LLM API - 使用流式响应 (图片转代码 Prompt)
export async function generatePrompt(base64Image, applicationType, temperature = 0.5, componentLibrary = 'yxfe') {
  try {
    if (!openai) {
      console.warn("OpenAI client not initialized. Using mock data instead.");
    }
    let displayInternalLibName = '@yxfe/components';
    if (componentLibrary === 'mtd') {
      displayInternalLibName = '@ss/mtd-vue2';
    } else if (componentLibrary === 'yxfe') {
      displayInternalLibName = process.env.VUE_APP_INTERNAL_LIB_NAME || '@yxfe/components';
    }

    const dynamicResponsePrefix = `
    Create detailed Vue 2 components with these requirements:
    **Core Technical & Project Requirements:**
    1.  **Structure & Language:** Use Vue Single File Components (\`.vue\`) with （<\`template\`>, <\`script\`>, <\`style scoped\`>）. Default to JavaScript for logic.
    2.  **Component Libraries:**
        *   **Primary:** Prioritize components from the internal library: **${displayInternalLibName}**. Style primarily using its components and classes.
        *   **Secondary:** Use **Element UI** components if ${displayInternalLibName} lacks a suitable component or for generic needs.
    3.  **Custom Styling:** Use custom CSS/SCSS in <\`style scoped\`> ONLY for fine-tuning or when library components do not suffice.
    4.  **Icons:** Prioritize ${displayInternalLibName}'s icon system if available; otherwise, use Element UI's built-in icons (e.g., <i class="el-icon-edit"></i>). Avoid other icon libraries unless specified.
    5.  **Code Quality & Conventions:**
        *   Generate complete, functional Vue 2 code.
        *   Strictly adhere to existing code formatting and naming conventions (components: PascalCase, props/methods: camelCase, CSS: kebab-case). This applies to custom code and interactions with ${displayInternalLibName} and Element UI.
        *   Follow proper import practices for all modules.
        *   If updating an existing view, integrate new elements seamlessly.

    **Project-Specific Adherence (CRITICAL):**
    1. **Analyze Existing Code:** Before generating, examine the surrounding files and structure in the current repository. This includes how ${displayInternalLibName} and Element UI components are typically used and imported.
    2. **Match Formatting:** Strictly adhere to the existing code formatting style.
    3. **Follow Naming Conventions:** Use the same naming conventions already present in the project for components (PascalCase?), props (camelCase?), methods (camelCase?), CSS classes (kebab-case?), variables, etc., **for both custom components and when interacting with ${displayInternalLibName} and Element UI.**
    `;

    const dynamicSystemPrompt = `You are an expert frontend developer. Your task is to analyze a UI image and generate a comprehensive, actionable prompt for another frontend developer to implement that UI within an enterprise Vue 2 project. This project heavily uses an internal component library named '${displayInternalLibName}' and Element UI.
    **Important Notes:**
    1. Use JavaScript by default for all component implementations.
    2. Only switch to TypeScript if explicitly requested by the user.
    3. Follow standard Vue 2 JavaScript practices for component structure, props, data, methods, etc.
    4. **The language of your generated response (the entire prompt including summary, analysis, and planning) MUST match the primary language of the text visible in the provided image. For example, if the image contains primarily Chinese text, your entire response should be in Chinese. If it's English, respond in English.**

    the generated prompt should contain the following parts:

    0. <response_prefix>
    1. <summary_title>
    2. <image_analysis>
    3. <development_planning>

    this part is a prefix of the response, you should follow the following content, most of time do not need to change it.

    ${dynamicResponsePrefix}

    ### summary_title

    Generate a concise, descriptive title for the page or feature depicted in the image, reflecting its primary function within the application.

    ### image_analysis

    Analyze the provided image meticulously and describe its visual components, their properties, and their relationships in detail. Focus on elements relevant for UI implementation:

    1.  **Primary Purpose/Goal:** Briefly state the main objective or function of the UI depicted in the image (e.g., "User registration form", "Product listing page with filters", "Dashboard displaying key metrics").
    2.  **Navigation Elements:** Identify ALL visible navigation components (e.g., sidebars, top navigation bars, tab bars, breadcrumbs, pagination). Describe their items, icons, labels, and apparent purpose (e.g., "Sidebar menu with items: 'Dashboard' (icon: home), 'Settings' (icon: gear), 'Profile' (icon: user)"; "Header with a logo on the left, search bar in the middle, and user avatar with a dropdown menu on the right").
    3.  **Overall Page Layout:** Describe the high-level structure and arrangement of major sections (e.g., "Two-column layout: a fixed 200px left sidebar and a main content area that scrolls", "Header (60px height) - Main Content (flexible height) - Footer (40px height)", "Grid-based dashboard with 2x2 cards"). Specify alignment and spacing if discernible.
    4.  **Content Sections & Blocks:** Detail each distinct block or section of content within the main area. For each block, describe its purpose, child elements, and their arrangement (e.g., "User Profile Section: Contains an avatar, user name, email, and an 'Edit Profile' button. Elements are vertically stacked.", "Product Card: Displays product image, name, price, and 'Add to Cart' button.").
    5.  **Interactive Controls & Forms:** List ALL interactive elements: buttons (specify variants like primary, secondary, text, icon-only), input fields (text, password, number, date pickers, etc.), dropdowns/selects, checkboxes, radio buttons, toggles, sliders, links, etc. For each, specify its label, placeholder text, current value (if visible), associated icons, and likely action or purpose. For forms, describe all fields, their types, labels, and any validation hints.
    6.  **Text Content & Typography:** List all significant pieces of text visible (headings, labels, paragraphs, captions, button text). Note their apparent font size, weight (bold, normal), color, and alignment if it stands out from a default.
    7.  **Key Visual Elements & Graphics:** Note prominent visual elements like images (describe content), icons (specify if they look like standard Element UI icons or custom ones), charts (type of chart and data represented), illustrations, logos, dividers, or custom graphics. Describe their appearance and placement.
    8.  **Color Palette & Theme:** Detail the dominant color palette (primary, secondary, accent colors, background colors, text colors). Note if the theme appears light, dark, or custom, and if it aligns with or deviates significantly from standard Element UI defaults.
    9.  **Data Display:** If data is displayed (e.g., in tables, lists, cards), describe the structure of the data and the fields shown (e.g., "User table with columns: ID, Name, Email, Status. Status is shown as a colored badge.").
    10. **Implicit Details & Interactions:** Infer and describe any non-obvious details or implied interactions (e.g., "Hover states on buttons suggest a color change", "Active tab is visually distinct", "Clicking the avatar likely opens a user menu").

    ### development_planning

    Based on the image analysis and the assumption of an existing enterprise Vue 2 + Element UI codebase with established patterns, outline a development plan:

    1.  **View Component(s):**
        *   Identify the main page/view Vue component(s) required (e.g., \`src/pages/FeatureName/index.vue\`).

    2.  **Component Utilization Strategy (CRITICAL):**
        *   **Library First:** For ALL standard UI elements (forms, tables, dialogs, drawers, buttons, inputs, layout grids, etc.), **you MUST specify direct usage of components from the internal library (\`${displayInternalLibName}\`) or, secondarily, Element UI.**
        *   **DO NOT suggest creating new custom component files for functionalities readily available through these libraries.** For instance, a data table should be implemented using a table component from \`${displayInternalLibName}\` or Element UI, not by creating a new \`MyTable.vue\`.
        *   **Criteria for New Custom Reusable Components:** Suggest creating a new custom Vue component file (e.g., in \`src/components/featureName/\`) **ONLY IF** a UI segment is:
            *   (a) **Highly complex and specific** in its UI or logic, AND cannot be adequately built by configuring library components.
            *   (b) **Clearly and verifiably reusable across MULTIPLE distinct pages or views** within the application.
        *   If these criteria are NOT met, the UI and logic for that segment should be implemented within the parent view component, using library components directly.
        *   List any **justified** new custom reusable components.

    3.  **Key Features Implementation:**
        *   List the core functionalities to implement (e.g., "Display filterable user data in a table," "Handle form submission for new item creation").
        *   For features involving significant interaction (e.g., a multi-step creation wizard in a dialog), if a custom component is justified per 2.c, mention it here. Otherwise, describe how library components would be orchestrated in the view.

    4.  **API Interaction:**
        *   Briefly outline necessary API calls (e.g., "Fetch item list," "Submit new item form"). Assume usage of project's standard API interaction methods.

    5.  **Styling Approach:**
        *   Reiterate primary reliance on \`${displayInternalLibName}\` and Element UI styles.
        *   Note areas likely requiring minimal custom scoped CSS for fine-tuning.

    6.  **Responsiveness:**
        *   Mention any obvious responsive considerations and recommend using library-provided utilities or existing project conventions.
    `;

    // 准备消息
    const messages = [
      {
        "role": "system",
        "content": dynamicSystemPrompt
      },
      {
        "role": "user",
        "content": [
          {
            "type": "text",
            "text": `Analyze the provided image in detail. Generate a comprehensive and highly descriptive prompt for a frontend developer. This prompt should meticulously describe all visual components, their specific attributes (size, color, typography, spacing), their layout relationships, styling details, and any discernible interactive behaviors. The goal is to provide enough information for an accurate UI implementation of a ${applicationType} application based strictly on the visual information in the image. **Ensure the language of your entire output prompt matches the primary language of the text visible in the image.** The primary component library to use is ${displayInternalLibName}.`,
          },
          {
            "type": "image_url",
            "image_url": {
              "url": base64Image
            },
          },
        ],
      }
    ];

    // 尝试调用 API，使用流式响应
    try {
      console.log(`Trying to use model: ${GEMINI_MODEL} via API, for library: ${displayInternalLibName}`);

      const stream = await openai.chat.completions.create({
        model: GEMINI_MODEL,
        messages: messages,
        temperature: temperature,
        stream: true, // 使用流式响应
        max_tokens: 4000, // 设置合理的最大 token 数
      });

      console.log(`Successfully used model: ${GEMINI_MODEL} via API`);
      return stream;
    } catch (err) {
      console.warn(`Failed to use model ${GEMINI_MODEL}:`, err.message);
    }
  } catch (error) {
    console.error("Error calling API:", error);
    throw new Error("生成代码Prompt时发生错误: " + error.message);
  }
}

// 新增功能：生成PRD for Agent Prompt
export async function generatePrdPromptForAgent(featureDescription, framework, uiLibrary, temperature = 0.5) {
  try {
    if (!openai) {
      throw new Error("OpenAI client not initialized. Please configure API key and base URL.");
    }

    const systemPrompt = `你是一个专业的Prompt工程师，专门为AI Agent生成高质量的产品需求文档（PRD）Prompt。
你的任务是根据用户提供的功能描述（可能来自直接文本输入或上传的文件），填充以下PRD Prompt模板。
请确保你的生成内容结构清晰，语言精确，能够指导AI Agent（如Cursor的Agent模式）高效完成前端功能实现。

---
# Cursor Agent 任务指令：PRD 功能实现

**角色设定：**
你是一名经验丰富的全栈/前端开发工程师，精通 [请填写你的前端框架，如：React / Vue / Angular] 和 TypeScript / JavaScript。你的任务是根据提供的产品需求文档 (PRD)，在现有代码库中实现指定的前端功能。

**目标：**
在现有项目中，按照PRD描述，准确、高效地完成 [功能名称，如：用户注册表单 / 商品列表筛选功能 / 订单详情页] 的前端开发、集成和验证。

**PRD 输入：**
---
[将你的产品需求文档 (PRD) 内容完整粘贴在这里。如果是非常长的PRD，可以只粘贴当前功能相关的部分，并指出关键需求点。]

**PRD 关键功能点总结 (请根据提供的功能描述，简要概括此功能需要实现的关键点)：**
- [功能点1]
- [功能点2]
- [UI 交互描述]
- [数据结构/API 接口描述（如果已知）]
---

**项目环境与约束：**
1.  **技术栈：** 本项目使用 ${framework}，TypeScript / JavaScript。
2.  **UI 组件库/样式框架：** ${uiLibrary}。请优先使用项目已有的组件库和样式规范。
3.  **状态管理：** [请根据项目实际情况填写，如：Redux / Zustand / Vuex / Pinia / React Context]。
4.  **路由：** [请根据项目实际情况填写，如：React Router Dom / Vue Router]。
5.  **现有代码风格：** 请严格遵循现有代码库的命名规范、文件组织结构和编码风格。
6.  **性能与可维护性：** 在满足功能需求的前提下，请优先考虑代码的可读性、可维护性和运行时性能。
7.  **错误处理：** 对于网络请求、数据处理等可能出现错误的地方，请添加必要的错误处理和用户友好的提示。
8.  **响应式设计：** 如果PRD中提及，请确保UI在不同屏幕尺寸下的响应式表现。

**工作流程与具体步骤：**

**第一阶段：PRD 理解与任务规划**
1.  **分析PRD：** 仔细阅读上述PRD内容，理解所有功能需求、UI交互、数据结构、业务逻辑和边缘情况。
2.  **识别影响范围：** 确定需要创建哪些新文件、修改哪些现有文件，以及涉及哪些组件、模块和数据流。
3.  **制定实现计划：** 输出一个详细的开发计划，包括：
    *   你需要进行哪些代码修改（具体到文件和大致功能点）。
    *   是否需要创建新的组件或页面？它们的职责是什么？
    *   如何处理数据获取（mock data 或真实 API）和状态管理？
    *   如何处理用户交互和表单验证（如果适用）？
    *   需要安装新的NPM包吗？如果是，请列出。
    *   预期的开发顺序。
4.  **疑问与澄清：** 如果在分析PRD或现有代码时遇到任何不明确的地方，请立即提出问题。

**第二阶段：代码实现**
1.  **执行计划：** 严格按照第一阶段制定的计划，逐步实现功能。
2.  **创建/修改文件：** 编写所有必要的新组件、页面、服务或工具函数，并修改现有文件以集成新功能。
3.  **遵循规范：** 确保所有新代码都符合项目现有的编码规范、命名约定和设计模式。
4.  **逐步提交：** 建议在完成一个逻辑单元后，进行一次小的、有意义的提交（即使你不直接执行git commit，也要在内部逻辑上体现）。

**第三阶段：功能验证与自查**
1.  **本地运行：** 尝试启动项目 (\`npm start\` 或 \`yarn dev\`)，确保没有编译错误或运行时错误。
2.  **单元测试/集成测试 (如果适用)：** 如果项目有测试框架，请尝试运行相关测试。如果功能逻辑复杂，请考虑添加新的测试。
3.  **UI/UX 验证：**
    *   检查页面布局和样式是否符合PRD描述。
    *   模拟用户交互（点击、输入、提交），验证功能是否按预期工作。
    *   检查所有状态（加载中、成功、失败、无数据）的UI表现是否正确。
    *   检查控制台是否有任何报错或警告。
4.  **代码审查 (自我)：** 检查你的代码是否存在潜在的性能问题、安全漏洞、可维护性问题或逻辑缺陷。

**第四阶段：总结与交付**
11. **任务完成报告：** 总结你为实现此功能所做的所有修改，包括：
    *   主要修改了哪些文件？
    *   新增了哪些文件或组件？
    *   解决了PRD中的哪些具体需求？
    *   在验证过程中遇到的问题和解决方案（如果有）。
    *   任何需要人工关注或进一步讨论的点。
12. **清理：** 移除所有临时的调试代码、注释或不必要的文件。

---

请你根据用户提供的功能描述（可能来自上传的文件，也可能直接输入文本），为 **"目标"** 和 **"PRD 关键功能点总结"** 部分生成内容，并填充到上面的模板中。
其他 \`[请填写...]\` 的部分，如果用户未明确指定，请保持原样，提示Agent在实际使用时手动填写。
同时，将 \`[功能名称，如：用户注册表单 / 商品列表筛选功能 / 订单详情页]\` 的部分根据用户描述进行推断和填充。
\`PRD 输入\` 部分保持空白，作为Agent需要接收的外部输入。
`;

    const userMessage = `请根据以下功能描述，填充PRD Agent Prompt模板：

功能描述：
\`\`\`
${featureDescription}
\`\`\`

请将推断出的功能名称填充到"目标"中的 "[功能名称]" 部分，并根据功能描述，简要概括出"PRD 关键功能点总结"的内容。`;

    const messages = [
      {
        "role": "system",
        "content": systemPrompt
      },
      {
        "role": "user",
        "content": userMessage,
      },
    ];

    try {
      console.log(`Trying to use model: ${GEMINI_MODEL} via API for PRD prompt generation.`);

      const stream = await openai.chat.completions.create({
        model: GEMINI_MODEL,
        messages: messages,
        temperature: temperature,
        stream: true,
        max_tokens: 4000, // 设置合理的最大 token 数，与图片生成保持一致
      });

      console.log(`Successfully used model: ${GEMINI_MODEL} via API for PRD prompt.`);
      return stream;
    } catch (err) {
      console.warn(`Failed to use model ${GEMINI_MODEL} for PRD prompt:`, err.message);
      if (err.message.includes('context_length_exceeded')) {
          throw new Error('输入内容过长，超出模型最大限制。请尝试精简功能描述。');
      }
      throw err;
    }
  } catch (error) {
    console.error("Error calling API for PRD prompt:", error);
    throw new Error("生成PRD Agent Prompt时发生错误: " + error.message);
  }
}