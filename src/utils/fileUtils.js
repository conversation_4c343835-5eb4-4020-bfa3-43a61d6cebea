// import mammoth from 'mammoth'; // 导入 mammoth.js
import imageCompression from 'browser-image-compression'; // 导入图片压缩库

export async function convertFileToBase64(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result);
      reader.onerror = error => reject(error);
      reader.readAsDataURL(file);
    });
  }
  
export async function convertFileToString(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      
      // 处理文本和Markdown文件
      if (file.type === 'text/plain' || file.type === 'text/markdown' || file.name.endsWith('.txt') || file.name.endsWith('.md')) {
        reader.onload = (e) => resolve(e.target.result);
        reader.onerror = (error) => reject(new Error('Failed to read text file: ' + error));
        reader.readAsText(file);
      } 
      // 处理 DOCX 文件
      else if (file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' || file.name.endsWith('.docx')) {
        reader.onload = async (e) => {
          try {
            const arrayBuffer = e.target.result;
            // 确保 mammoth 已经加载
            const mammothModule = await import('mammoth'); // 动态导入 mammoth
            const result = await mammothModule.extractRawText({ arrayBuffer: arrayBuffer });
            resolve(result.value);
          } catch (error) {
            reject(new Error('Failed to read docx file: ' + error.message));
          }
        };
        reader.onerror = (error) => reject(new Error('Failed to read docx file: ' + error));
        reader.readAsArrayBuffer(file);
      } 
      // 不支持的文件类型
      else {
        reject(new Error('Unsupported file type: ' + file.type));
      }
    });
  }

// 新增图片压缩函数
export async function compressAndConvertImage(file) {
    // 压缩选项：
    // - maxIteration: 10 （最大迭代次数）
    // - maxSizeMB: 1 （最大文件大小，单位 MB，目标是小于 1MB）
    // - maxWidthOrHeight: 1024 （最大宽度或高度，图片尺寸限制）
    // - use      WebWorker: true （使用 WebWorker 进行异步处理，避免阻塞主线程）
    const options = {
      maxSizeMB: 1, // (max file size in MB)
      maxWidthOrHeight: 1024, // (max width or height in pixels)
      useWebWorker: true, // (use web worker for faster compression)
      initialQuality: 0.9, // (initial quality, 0 to 1)
      alwaysKeepResolution: false, // (whether to keep the image's original resolution)
      fileType: 'image/jpeg', // Force JPEG for smaller size
    }

    try {
        console.log('Original image file size:', file.size / 1024 / 1024, 'MB');
        const compressedFile = await imageCompression(file, options);
        console.log('Compressed image file size:', compressedFile.size / 1024 / 1024, 'MB');
        
        // Convert compressed file to Base64
        const base64 = await convertFileToBase64(compressedFile);
        return base64;
    } catch (error) {
        console.error('Image compression failed:', error);
        throw new Error('图片处理失败，请尝试其他图片。');
    }
}