<template>
  <el-row :gutter="20" class="prd-to-agent-panel">
    <el-col :span="12" class="card-column">
      <el-card class="controls-card">
        <h3 class="section-title">功能描述 (通过文本或上传文件)：</h3>
        <div class="description-input-area">
          <div class="upload-controls">
            <!-- 文件上传按钮 -->
            <el-upload
              action=""
              :auto-upload="false"
              :show-file-list="false"
              :on-change="handleFileUpload"
              class="upload-btn"
              accept=".txt,.md,.docx"
              :disabled="loading"
              ref="prdFileUploaderRef"
            >
              <el-button slot="trigger" size="small" icon="el-icon-upload2" :disabled="loading">上传文件</el-button>
            </el-upload>
            <el-button
                size="small"
                icon="el-icon-delete"
                @click="clearAllInput"
                :disabled="loading || (!featureDescription.trim() && !uploadedFileName)"
            >清空所有</el-button>
          </div>
          <div class="upload-tip">支持 .txt, .md, .docx 文件上传，文件内容将覆盖当前输入框内容。</div>

          <!-- Markdown 编辑器 -->
          <VueEasyMDE
            v-model="featureDescription"
            :options="mdeOptions"
            ref="mdeEditorRef"
            class="markdown-editor"
            :class="{'is-disabled': loading}"
          />
        </div>

        <h3 class="section-title" style="margin-top: 20px;">目标项目技术栈：</h3>
        <el-select v-model="form.framework" placeholder="选择前端框架" class="full-width" style="margin-bottom: 15px;">
          <el-option label="Vue" value="Vue"></el-option>
          <el-option label="React" value="React"></el-option>
          <el-option label="Angular" value="Angular"></el-option>
        </el-select>

        <el-select v-model="form.uiLibrary" placeholder="选择UI组件库/样式框架" class="full-width">
          <el-option label="Element UI" value="Element UI"></el-option>
          <el-option label="Ant Design" value="Ant Design"></el-option>
          <el-option label="Material-UI" value="Material-UI"></el-option>
          <el-option label="Chakra UI" value="Chakra UI"></el-option>
          <el-option label="Tailwind CSS" value="Tailwind CSS"></el-option>
        </el-select>

        <h3 class="section-title temperature-title" style="margin-top: 20px;">
          <span>创意度 (Temperature):</span>
          <span class="temperature-value">{{ form.temperature / 100 }}</span>
        </h3>
        <div class="temperature-slider">
          <span class="slider-label">Focused</span>
          <el-slider
            v-model="form.temperature"
            :min="0"
            :max="100"
            :step="1"
            :format-tooltip="formatTemperatureTooltip"
            style="flex-grow: 1; margin: 0 15px;"
          ></el-slider>
          <span class="slider-label">Creative</span>
        </div>
        <p class="temperature-desc">
          控制Agent生成Prompt的随机性。数值越低越专注，越高越有创造力。
        </p>

        <el-alert
          v-if="!apiKeySet"
          title="API Key Not Set"
          type="warning"
          description="To use the actual LLM API, please set your API key in the .env file. Currently using mock data."
          show-icon
          :closable="false"
          style="margin-top: 15px;"
        >
        </el-alert>

        <el-button
          type="primary"
          @click="handleGeneratePrdPrompt"
          :loading="loading"
          :disabled="!canGenerate"
          class="generate-button"
        >
          {{ loading ? '生成中...' : '生成 Agent PRD Prompt' }}
        </el-button>
      </el-card>
    </el-col>

    <el-col :span="12" class="card-column">
      <el-card class="controls-card">
        <div v-if="generatedPrdPrompt || loading" class="result-section">
          <div class="result-header">
            <h4>Generated Agent PRD Prompt:</h4>
            <el-button
              type="text"
              icon="el-icon-document-copy"
              @click="copyPrdPrompt"
            >
              {{ copied ? 'Copied!' : 'Copy' }}
            </el-button>
          </div>
          <div class="result-content" ref="prdPromptContainer" v-if="generatedPrdPrompt">
            <div v-html="formattedPrdPrompt"></div>
          </div>
          <el-empty v-if="!generatedPrdPrompt && !loading" description="输入功能描述和项目信息，点击 '生成 Agent PRD Prompt'。" style="display:flex; flex-direction:column; justify-content:center; flex-grow: 1;"></el-empty>
          <div v-if="loading" style="display:flex; align-items:center; justify-content:center; flex-grow: 1;">
              <i class="el-icon-loading" style="font-size: 24px;"></i> Generating...
          </div>
        </div>
      </el-card>
    </el-col>
  </el-row>
</template>

<script>
import { mapState, mapActions } from 'vuex';
import { marked } from 'marked';
import { convertFileToString } from '@/utils/fileUtils';
import VueEasyMDE from 'vue-easymde';
import 'easymde/dist/easymde.min.css';
import hljs from 'highlight.js';
import 'highlight.js/styles/github.css';

// 初始化 marked，使用 highlight.js 进行代码高亮
marked.setOptions({
  highlight: function(code, lang) {
    const language = hljs.getLanguage(lang) ? lang : 'plaintext';
    return hljs.highlight(code, { language }).value;
  },
  langPrefix: 'language-',
  gfm: true,
  breaks: true,
});

export default {
  name: 'PRDToAgentPanel',
  components: {
    VueEasyMDE
  },
  props: {
      loading: {
          type: Boolean,
          default: false
      }
  },
  data() {
    return {
      featureDescription: '',
      uploadedFileContent: '',
      uploadedFileName: '',
      form: {
        framework: 'Vue',
        uiLibrary: 'Element UI',
        temperature: 50,
      },
      copied: false,
      apiKeySet: false,
      mdeOptions: {
        spellChecker: false,
        autosave: {
          enabled: false,
          delay: 500,
          uniqueId: "prd-editor-autosave",
        },
        status: false,
        toolbar: false,
        renderingConfig: {
          codeSyntaxHighlighting: true,
        },
        placeholder: "在此处输入功能描述（支持 Markdown 格式）...",
        hideIcons: ["guide", "image", "fullscreen", "side-by-side", "preview"],
        forceSync: true,
      },

    };
  },
  computed: {
    ...mapState({
        generatedPrdPrompt: state => state.generatedPrdPrompt,
    }),
    formattedPrdPrompt() {
      if (!this.generatedPrdPrompt) return '';
      return marked(this.generatedPrdPrompt);
    },
    canGenerate() {
        return this.featureDescription.trim() &&
               this.form.framework && this.form.uiLibrary;
    }
  },
  watch: {
    loading(newVal) {
      // 设置编辑器的只读状态
      if (this.$refs.mdeEditorRef && this.$refs.mdeEditorRef.easymde) {
        this.$refs.mdeEditorRef.easymde.codemirror.setOption("readOnly", newVal);
      }
    },
    generatedPrdPrompt() {
          this.$nextTick(() => {
              if (this.$refs.prdPromptContainer) {
                  this.$refs.prdPromptContainer.scrollTop = 0;
              }
          });
      }
  },
  created() {
    this.apiKeySet = process.env.VUE_APP_GEMINI_API_KEY &&
                     process.env.VUE_APP_GEMINI_API_KEY !== 'your_api_key_here';
  },
  mounted() {
    this.$nextTick(() => {
        // 确保DOM更新后设置编辑器内容
        if (this.$refs.mdeEditorRef && this.$refs.mdeEditorRef.easymde && this.featureDescription) {
            this.$refs.mdeEditorRef.easymde.value(this.featureDescription);
        }
        // 初始设置编辑器的只读状态
        if (this.$refs.mdeEditorRef && this.$refs.mdeEditorRef.easymde) {
            this.$refs.mdeEditorRef.easymde.codemirror.setOption("readOnly", this.loading);
        }
    });
  },
  methods: {
    ...mapActions(['generatePrdPrompt']),
    marked,
    convertFileToString,

    formatTemperatureTooltip(val) {
      return `${val / 100}`;
    },
    async handleFileUpload(file) {
      const allowedTypes = [
          'text/plain',
          'text/markdown',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      ];
      const fileExtension = file.name.split('.').pop().toLowerCase();
      const isAllowed = allowedTypes.includes(file.raw.type) || ['txt', 'md', 'docx'].includes(fileExtension);

      if (!isAllowed) {
        this.$message.error('只能上传 .txt, .md 或 .docx 文件!');
        if (this.$refs.prdFileUploaderRef) this.$refs.prdFileUploaderRef.clearFiles();
        return;
      }
      const isLt5M = file.size / 1024 / 1024 < 5;
      if (!isLt5M) {
        this.$message.error('上传文件大小不能超过 5MB!');
        if (this.$refs.prdFileUploaderRef) this.$refs.prdFileUploaderRef.clearFiles();
        return;
      }

      this.uploadedFileName = file.name;
      try {
        const content = await this.convertFileToString(file.raw);
        this.uploadedFileContent = content;
        this.featureDescription = content;

        this.$nextTick(() => {
            if (this.$refs.mdeEditorRef && this.$refs.mdeEditorRef.easymde) {
                this.$refs.mdeEditorRef.easymde.value(content);
            }
        });

      } catch (error) {
        console.error('Error reading uploaded file:', error);
        this.$message.error('文件读取失败: ' + error.message);
        this.removeUploadedFile();
      }
    },
    removeUploadedFile() {
      this.uploadedFileContent = '';
      this.uploadedFileName = '';
      this.featureDescription = '';

      if (this.$refs.mdeEditorRef && this.$refs.mdeEditorRef.easymde) {
          this.$refs.mdeEditorRef.easymde.value("");
      }
      if (this.$refs.prdFileUploaderRef) this.$refs.prdFileUploaderRef.clearFiles();
    },
    clearAllInput() {
        this.featureDescription = '';
        this.removeUploadedFile();
        this.form.framework = 'Vue';
        this.form.uiLibrary = 'Element UI';
        this.form.temperature = 50;
        this.$message.info('已清空所有输入。');
    },
    async handleGeneratePrdPrompt() {
      const descriptionToUse = this.featureDescription.trim();

      if (!descriptionToUse) {
        this.$message.warning('请输入功能描述或上传文件。');
        return;
      }
      if (!this.form.framework) {
        this.$message.warning('请选择前端框架。');
        return;
      }
      if (!this.form.uiLibrary) {
        this.$message.warning('请选择UI组件库/样式框架。');
        return;
      }

      await this.generatePrdPrompt({
        featureDescription: descriptionToUse,
        framework: this.form.framework,
        uiLibrary: this.form.uiLibrary,
        temperature: this.form.temperature / 100,
      });
    },
    async copyPrdPrompt() {
      try {
        await navigator.clipboard.writeText(this.generatedPrdPrompt);
        this.copied = true;
        this.$message.success('已复制到剪贴板');
        setTimeout(() => {
          this.copied = false;
        }, 2000);
      } catch (error) {
        this.$message.error('复制失败');
      }
    },

    clearState() {
        this.featureDescription = '';
        this.removeUploadedFile();
        this.form.framework = 'Vue';
        this.form.uiLibrary = 'Element UI';
        this.form.temperature = 50;
    }
  },
};
</script>

<style scoped>
.prd-to-agent-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Markdown 编辑器样式 */
.markdown-editor {
    min-height: 240px;
    margin-top: 12px;
    border-radius: 12px;
    overflow: hidden;
    border: 2px solid #e2e8f0;
    transition: all 0.3s ease;
}

.markdown-editor:focus-within {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.markdown-editor.is-disabled {
    opacity: 0.6;
    pointer-events: none;
}

.markdown-editor :deep(.CodeMirror) {
    border-radius: 12px;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 14px;
    line-height: 1.6;
    padding: 16px;
}

.markdown-editor :deep(.CodeMirror-scroll) {
    min-height: 240px;
}

.markdown-editor :deep(.CodeMirror-placeholder) {
    color: #9ca3af;
    font-style: italic;
}



/* 上传控制区域 */
.upload-controls {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
    align-items: center;
}

.upload-controls :deep(.el-button) {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.upload-controls :deep(.el-button--primary) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.upload-controls :deep(.el-button--primary:hover) {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

/* 确保编辑器在卡片中正确显示 */
.prd-to-agent-panel .el-card {
    height: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    border-radius: 16px;
    transition: all 0.3s ease;
}

.prd-to-agent-panel .el-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
}

.description-input-area {
    margin-bottom: 24px;
}

.upload-tip {
    font-size: 13px;
    color: #6b7280;
    margin-bottom: 12px;
    padding: 12px 16px;
    background: rgba(102, 126, 234, 0.05);
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.section-title {
    font-size: 20px;
    font-weight: 700;
    color: #1a202c;
    margin: 24px 0 16px;
    padding-bottom: 12px;
    position: relative;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40px;
    height: 3px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 2px;
}

.section-title:first-child {
    margin-top: 0;
}

.temperature-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.temperature-value {
    font-size: 16px;
    font-weight: 600;
    color: #667eea;
}

.temperature-slider {
    display: flex;
    align-items: center;
    margin: 12px 0;
}

.temperature-slider :deep(.el-slider) {
    flex: 1;
    margin: 0 16px;
}

.temperature-slider :deep(.el-slider__runway) {
    background: #e5e7eb;
    border-radius: 6px;
    height: 6px;
}

.temperature-slider :deep(.el-slider__bar) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 6px;
}

.temperature-slider :deep(.el-slider__button) {
    border: 3px solid #667eea;
    background: #fff;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.slider-label {
    font-size: 13px;
    color: #6b7280;
    font-weight: 500;
    min-width: 30px;
    text-align: center;
}

.temperature-desc {
    font-size: 13px;
    color: #6b7280;
    margin: 8px 0 20px;
    line-height: 1.4;
}

.generate-button {
    width: 100%;
    margin-top: 24px;
    height: 48px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    transition: all 0.3s ease;
    color: white;
}

.generate-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.generate-button:disabled {
    opacity: 0.6;
    transform: none;
    box-shadow: none;
}

.full-width {
    width: 100%;
}

/* 表单项优化 */
:deep(.el-form-item__label) {
    font-weight: 600;
    color: #374151;
    font-size: 15px;
    margin-bottom: 8px;
}

:deep(.el-select),
:deep(.el-input) {
    border-radius: 12px;
}

:deep(.el-select .el-input__inner),
:deep(.el-input__inner) {
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    transition: all 0.3s ease;
    font-size: 14px;
    padding: 12px 16px;
}

:deep(.el-select .el-input__inner:focus),
:deep(.el-input__inner:focus) {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 结果区域样式 */
.result-content {
    flex-grow: 1;
    overflow-y: auto;
    padding: 20px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 12px;
    border: 2px solid #e2e8f0;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
}

.result-content :deep(h1),
.result-content :deep(h2),
.result-content :deep(h3),
.result-content :deep(h4),
.result-content :deep(h5),
.result-content :deep(h6) {
    color: #1a202c;
    font-weight: 700;
    margin: 24px 0 16px 0;
    line-height: 1.3;
}

.result-content :deep(h1) { font-size: 28px; }
.result-content :deep(h2) { font-size: 24px; }
.result-content :deep(h3) { font-size: 20px; }
.result-content :deep(h4) { font-size: 18px; }

.result-content :deep(p) {
    color: #374151;
    margin: 16px 0;
    font-size: 15px;
}

.result-content :deep(ul),
.result-content :deep(ol) {
    margin: 16px 0;
    padding-left: 24px;
}

.result-content :deep(li) {
    color: #374151;
    margin: 8px 0;
    font-size: 15px;
}

.result-content :deep(code) {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
    padding: 2px 6px;
    border-radius: 6px;
    font-family: 'JetBrains Mono', 'Fira Code', Consolas, monospace;
    font-size: 13px;
}

.result-content :deep(pre) {
    background: #1a202c;
    color: #e2e8f0;
    padding: 20px;
    border-radius: 12px;
    overflow-x: auto;
    margin: 20px 0;
    border: 1px solid #374151;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.result-content :deep(pre code) {
    background: transparent;
    color: inherit;
    padding: 0;
    border-radius: 0;
    font-family: 'JetBrains Mono', 'Fira Code', Consolas, monospace;
    font-size: 14px;
    line-height: 1.5;
}

/* 响应式优化 */
@media screen and (max-width: 768px) {
    .prd-to-agent-panel .el-card {
        border-radius: 12px;
    }

    .section-title {
        font-size: 18px;
        margin: 20px 0 12px;
    }

    .markdown-editor {
        min-height: 200px;
        border-radius: 8px;
    }

    .result-content {
        padding: 16px;
        border-radius: 8px;
    }

    .generate-button {
        height: 44px;
        font-size: 15px;
    }
}
</style>