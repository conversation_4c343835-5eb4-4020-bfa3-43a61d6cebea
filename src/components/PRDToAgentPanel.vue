<template>
  <el-row :gutter="20" class="prd-to-agent-panel">
    <el-col :span="12" class="card-column">
      <el-card class="controls-card">
        <h3 class="section-title">功能描述 (通过文本或上传文件)：</h3>
        <div class="description-input-area">
          <div class="upload-controls">
            <!-- 文件上传按钮 -->
            <el-upload
              action=""
              :auto-upload="false"
              :show-file-list="false"
              :on-change="handleFileUpload"
              class="upload-btn"
              accept=".txt,.md,.docx"
              :disabled="loading"
              ref="prdFileUploaderRef"
            >
              <el-button slot="trigger" size="small" icon="el-icon-upload2" :disabled="loading">上传文件</el-button>
            </el-upload>
            <el-button
                size="small"
                icon="el-icon-delete"
                @click="clearAllInput"
                :disabled="loading || (!featureDescription.trim() && !uploadedFileName)"
            >清空所有</el-button>
          </div>
          <div class="upload-tip">支持 .txt, .md, .docx 文件上传，文件内容将覆盖当前输入框内容。</div>

          <!-- Markdown 编辑器：使用 v-if 控制加载 -->
          <vue-easy-mde
            v-if="isEditorLoaded"
            v-model="featureDescription"
            :options="mdeOptions"
            ref="mdeEditorRef"
            class="markdown-editor"
            :class="{'is-disabled': loading}"
          />
          <div v-else class="editor-loading-placeholder">
              <i class="el-icon-loading"></i> Markdown 编辑器加载中...
          </div>
        </div>

        <h3 class="section-title" style="margin-top: 20px;">目标项目技术栈：</h3>
        <el-select v-model="form.framework" placeholder="选择前端框架" class="full-width" style="margin-bottom: 15px;">
          <el-option label="Vue" value="Vue"></el-option>
          <el-option label="React" value="React"></el-option>
          <el-option label="Angular" value="Angular"></el-option>
        </el-select>

        <el-select v-model="form.uiLibrary" placeholder="选择UI组件库/样式框架" class="full-width">
          <el-option label="Element UI" value="Element UI"></el-option>
          <el-option label="Ant Design" value="Ant Design"></el-option>
          <el-option label="Material-UI" value="Material-UI"></el-option>
          <el-option label="Chakra UI" value="Chakra UI"></el-option>
          <el-option label="Tailwind CSS" value="Tailwind CSS"></el-option>
        </el-select>

        <h3 class="section-title temperature-title" style="margin-top: 20px;">
          <span>创意度 (Temperature):</span>
          <span class="temperature-value">{{ form.temperature / 100 }}</span>
        </h3>
        <div class="temperature-slider">
          <span class="slider-label">Focused</span>
          <el-slider
            v-model="form.temperature"
            :min="0"
            :max="100"
            :step="1"
            :format-tooltip="formatTemperatureTooltip"
            style="flex-grow: 1; margin: 0 15px;"
          ></el-slider>
          <span class="slider-label">Creative</span>
        </div>
        <p class="temperature-desc">
          控制Agent生成Prompt的随机性。数值越低越专注，越高越有创造力。
        </p>

        <el-alert
          v-if="!apiKeySet"
          title="API Key Not Set"
          type="warning"
          description="To use the actual LLM API, please set your API key in the .env file. Currently using mock data."
          show-icon
          :closable="false"
          style="margin-top: 15px;"
        >
        </el-alert>

        <el-button
          type="primary"
          @click="handleGeneratePrdPrompt"
          :loading="loading"
          :disabled="!canGenerate"
          class="generate-button"
        >
          {{ loading ? '生成中...' : '生成 Agent PRD Prompt' }}
        </el-button>
      </el-card>
    </el-col>

    <el-col :span="12" class="card-column">
      <el-card class="controls-card">
        <div v-if="generatedPrdPrompt || loading" class="result-section">
          <div class="result-header">
            <h4>Generated Agent PRD Prompt:</h4>
            <el-button
              type="text"
              icon="el-icon-document-copy"
              @click="copyPrdPrompt"
            >
              {{ copied ? 'Copied!' : 'Copy' }}
            </el-button>
          </div>
          <div class="result-content" ref="prdPromptContainer" v-if="generatedPrdPrompt">
            <div v-html="formattedPrdPrompt"></div>
            <div v-for="(block, index) in codeBlocks" :key="index" class="code-block-wrapper">
                <el-button
                    type="text"
                    icon="el-icon-document-copy"
                    size="mini"
                    class="copy-code-btn"
                    @click="copyCode(block.content)"
                >
                    Copy Code
                </el-button>
                <pre><code :class="`language-${block.lang}`">{{ block.content }}</code></pre>
            </div>
          </div>
          <el-empty v-if="!generatedPrdPrompt && !loading" description="输入功能描述和项目信息，点击 '生成 Agent PRD Prompt'。" style="display:flex; flex-direction:column; justify-content:center; flex-grow: 1;"></el-empty>
          <div v-if="loading" style="display:flex; align-items:center; justify-content:center; flex-grow: 1;">
              <i class="el-icon-loading" style="font-size: 24px;"></i> Generating...
          </div>
        </div>
      </el-card>
    </el-col>
  </el-row>
</template>

<script>
import { mapState, mapActions } from 'vuex';
import { marked } from 'marked';
import { convertFileToString } from '@/utils/fileUtils';

let VueEasyMDE = null; // 声明为 null，等待动态导入
let hljs = null; // 声明为 null，等待动态导入

export default {
  name: 'PRDToAgentPanel',
  components: {
    // VueEasyMDE 动态注册
  },
  props: {
      loading: {
          type: Boolean,
          default: false
      }
  },
  data() {
    return {
      featureDescription: '',
      uploadedFileContent: '',
      uploadedFileName: '',
      form: {
        framework: 'Vue',
        uiLibrary: 'Element UI',
        temperature: 50,
      },
      copied: false,
      apiKeySet: false,
      mdeOptions: {
        spellChecker: false,
        autosave: {
          enabled: false,
          delay: 500,
          uniqueId: "prd-editor-autosave",
        },
        status: false,
        toolbar: false,
        renderingConfig: {
          codeSyntaxHighlighting: true,
        },
        placeholder: "在此处输入功能描述（支持 Markdown 格式）...",
        hideIcons: ["guide", "image", "fullscreen", "side-by-side", "preview"],
        forceSync: true,
      },
      isEditorLoaded: false, // 新增状态：编辑器是否已加载
    };
  },
  computed: {
    ...mapState({
        generatedPrdPrompt: state => state.generatedPrdPrompt,
    }),
    formattedPrdPrompt() {
      if (!this.generatedPrdPrompt) return '';
      return marked(this.generatedPrdPrompt);
    },
    codeBlocks() {
      if (!this.generatedPrdPrompt) return [];
      const blocks = [];
      const regex = /```(\w+)?\n([\s\S]*?)\n```/g;
      let match;

      while ((match = regex.exec(this.generatedPrdPrompt)) !== null) {
        const lang = match[1] || 'plaintext';
        const content = match[2];
        blocks.push({ type: 'code', lang: lang, content: content });
      }
      return blocks;
    },
    canGenerate() {
        return this.featureDescription.trim() &&
               this.form.framework && this.form.uiLibrary;
    }
  },
  watch: {
    loading(newVal) {
      // 只有编辑器加载完成后才设置 readOnly
      if (this.isEditorLoaded && this.$refs.mdeEditorRef && this.$refs.mdeEditorRef.easymde) {
        this.$refs.mdeEditorRef.easymde.codemirror.setOption("readOnly", newVal);
      }
    },
    generatedPrdPrompt() {
          this.$nextTick(() => {
              if (this.$refs.prdPromptContainer) {
                  this.$refs.prdPromptContainer.scrollTop = 0;
              }
          });
      }
  },
  // 在组件创建前动态导入
  beforeCreate() {
    if (!VueEasyMDE) { // 确保只加载一次
        Promise.all([
            import('vue-easymde'),
            import('easymde/dist/easymde.min.css'), // 引入样式
            import('highlight.js'), // 引入 highlight.js
            import('highlight.js/styles/github.css') // 引入 highlight.js 样式
        ]).then(([mdeModule, , hljsModule, ]) => {
            VueEasyMDE = mdeModule.default;
            hljs = hljsModule.default;

            // 动态注册组件
            this.$options.components.VueEasyMDE = VueEasyMDE;

            // 初始化 marked，使用 highlight.js 进行代码高亮
            marked.setOptions({
              highlight: function(code, lang) {
                const language = hljs.getLanguage(lang) ? lang : 'plaintext';
                return hljs.highlight(code, { language }).value;
              },
              langPrefix: 'language-',
              gfm: true,
              breaks: true,
            });
            this.isEditorLoaded = true; // 标记编辑器已加载
            this.$nextTick(() => { // 确保DOM更新后设置编辑器内容
                if (this.$refs.mdeEditorRef && this.$refs.mdeEditorRef.easymde && this.featureDescription) {
                    this.$refs.mdeEditorRef.easymde.value(this.featureDescription);
                }
                // 初始设置编辑器的只读状态
                if (this.$refs.mdeEditorRef && this.$refs.mdeEditorRef.easymde) {
                    this.$refs.mdeEditorRef.easymde.codemirror.setOption("readOnly", this.loading);
                }
            });
        }).catch(err => {
            console.error("Failed to load EasyMDE or Highlight.js:", err);
            this.$message.error('Markdown 编辑器加载失败。');
        });
    } else {
        this.isEditorLoaded = true; // 如果已经加载过，直接设置为true
    }
  },
  created() {
    this.apiKeySet = process.env.VUE_APP_GEMINI_API_KEY &&
                     process.env.VUE_APP_GEMINI_API_KEY !== 'your_api_key_here';
  },
  // mounted 钩子中不再需要初始化 marked 和设置 readOnly，因为已移到 beforeCreate 和 watch 中
  // mounted() { ... },
  methods: {
    ...mapActions(['generatePrdPrompt']),
    marked,
    convertFileToString,

    formatTemperatureTooltip(val) {
      return `${val / 100}`;
    },
    async handleFileUpload(file) {
      const allowedTypes = [
          'text/plain',
          'text/markdown',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      ];
      const fileExtension = file.name.split('.').pop().toLowerCase();
      const isAllowed = allowedTypes.includes(file.raw.type) || ['txt', 'md', 'docx'].includes(fileExtension);

      if (!isAllowed) {
        this.$message.error('只能上传 .txt, .md 或 .docx 文件!');
        if (this.$refs.prdFileUploaderRef) this.$refs.prdFileUploaderRef.clearFiles();
        return;
      }
      const isLt5M = file.size / 1024 / 1024 < 5;
      if (!isLt5M) {
        this.$message.error('上传文件大小不能超过 5MB!');
        if (this.$refs.prdFileUploaderRef) this.$refs.prdFileUploaderRef.clearFiles();
        return;
      }

      this.uploadedFileName = file.name;
      try {
        const content = await this.convertFileToString(file.raw);
        this.uploadedFileContent = content;
        this.featureDescription = content;

        this.$nextTick(() => {
            if (this.$refs.mdeEditorRef && this.$refs.mdeEditorRef.easymde) {
                this.$refs.mdeEditorRef.easymde.value(content);
            }
        });

      } catch (error) {
        console.error('Error reading uploaded file:', error);
        this.$message.error('文件读取失败: ' + error.message);
        this.removeUploadedFile();
      }
    },
    removeUploadedFile() {
      this.uploadedFileContent = '';
      this.uploadedFileName = '';
      this.featureDescription = '';

      if (this.$refs.mdeEditorRef && this.$refs.mdeEditorRef.easymde) {
          this.$refs.mdeEditorRef.easymde.value("");
      }
      if (this.$refs.prdFileUploaderRef) this.$refs.prdFileUploaderRef.clearFiles();
    },
    clearAllInput() {
        this.featureDescription = '';
        this.removeUploadedFile();
        this.form.framework = 'Vue';
        this.form.uiLibrary = 'Element UI';
        this.form.temperature = 50;
        this.$message.info('已清空所有输入。');
    },
    async handleGeneratePrdPrompt() {
      const descriptionToUse = this.featureDescription.trim();

      if (!descriptionToUse) {
        this.$message.warning('请输入功能描述或上传文件。');
        return;
      }
      if (!this.form.framework) {
        this.$message.warning('请选择前端框架。');
        return;
      }
      if (!this.form.uiLibrary) {
        this.$message.warning('请选择UI组件库/样式框架。');
        return;
      }

      await this.generatePrdPrompt({
        featureDescription: descriptionToUse,
        framework: this.form.framework,
        uiLibrary: this.form.uiLibrary,
        temperature: this.form.temperature / 100,
      });
    },
    async copyPrdPrompt() {
      try {
        await navigator.clipboard.writeText(this.generatedPrdPrompt);
        this.copied = true;
        this.$message.success('已复制到剪贴板');
        setTimeout(() => {
          this.copied = false;
        }, 2000);
      } catch (error) {
        this.$message.error('复制失败');
      }
    },
    async copyCode(codeContent) {
      try {
        await navigator.clipboard.writeText(codeContent);
        this.$message.success('代码已复制！');
      } catch (error) {
        this.$message.error('代码复制失败');
      }
    },
    clearState() {
        this.featureDescription = '';
        this.removeUploadedFile();
        this.form.framework = 'Vue';
        this.form.uiLibrary = 'Element UI';
        this.form.temperature = 50;
    }
  },
};
</script>

<style scoped>
/* Add placeholder for editor loading */
.editor-loading-placeholder {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px; /* 与编辑器最小高度保持一致 */
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    background-color: #f9fafc;
    color: #909399;
    font-size: 14px;
}
.editor-loading-placeholder .el-icon-loading {
    margin-right: 8px;
    font-size: 16px;
}

/* New styles for copy code button in result area */
.code-block-wrapper {
  position: relative;
  margin-bottom: 1em;
}
.code-block-wrapper pre {
  margin-top: 0;
}
.copy-code-btn {
  position: absolute;
  top: 5px;
  right: 5px;
  z-index: 10;
  background-color: rgba(255, 255, 255, 0.7);
  padding: 3px 8px;
  border-radius: 4px;
  font-size: 12px;
  opacity: 0;
  transition: opacity 0.3s ease;
}
.code-block-wrapper:hover .copy-code-btn {
  opacity: 1;
}

/* Ensure pre and code styles for highlight.js */
.result-content :deep(pre code) {
  display: block;
  overflow-x: auto;
  padding: 1em;
  background: #f1f1f1;
  border-radius: 4px;
}
.result-content :deep(pre) {
    position: relative;
    padding: 0;
}

/* Add styles for clearAllInput button area */
.upload-controls {
    display: flex;
    gap: 10px;
    margin-bottom: 5px;
}
</style>