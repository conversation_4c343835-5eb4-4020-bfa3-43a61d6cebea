<template>
  <div class="prd-to-agent-panel">
    <el-row :gutter="24" class="main-row">
      <!-- 左侧：输入和配置区域 -->
      <el-col :lg="10" :md="11" :sm="24" class="left-column">
        <el-card class="input-card">
          <div slot="header" class="input-header">
            <div class="header-title">
              <i class="el-icon-edit-outline header-icon"></i>
              <span>PRD 功能描述</span>
            </div>
          </div>

          <div class="input-body">
            <div class="description-input-area">
              <div class="upload-controls">
                <el-upload
                  action=""
                  :auto-upload="false"
                  :show-file-list="false"
                  :on-change="handleFileUpload"
                  class="upload-btn"
                  accept=".txt,.md,.docx"
                  :disabled="loading"
                  ref="prdFileUploaderRef"
                >
                  <el-button slot="trigger" size="small" icon="el-icon-upload2" :disabled="loading">上传文件</el-button>
                </el-upload>
                <el-button
                    size="small"
                    icon="el-icon-delete"
                    @click="clearAllInput"
                    :disabled="loading || (!featureDescription.trim() && !uploadedFileName)"
                >清空所有</el-button>
              </div>
              <div class="upload-tip">支持 .txt, .md, .docx 文件上传，文件内容将覆盖当前输入框内容。</div>

              <VueEasyMDE
                v-model="featureDescription"
                :options="mdeOptions"
                ref="mdeEditorRef"
                class="markdown-editor"
                :class="{'is-disabled': loading}"
              />
            </div>

            <div class="config-section">
              <h3 class="section-title">技术栈配置</h3>
              <div class="form-group">
                <label class="form-label">前端框架</label>
                <el-select v-model="form.framework" placeholder="选择前端框架" class="full-width">
                  <el-option label="Vue" value="Vue"></el-option>
                  <el-option label="React" value="React"></el-option>
                  <el-option label="Angular" value="Angular"></el-option>
                </el-select>
              </div>

              <div class="form-group">
                <label class="form-label">UI 组件库</label>
                <el-select v-model="form.uiLibrary" placeholder="选择UI组件库/样式框架" class="full-width">
                  <el-option label="Element UI" value="Element UI"></el-option>
                  <el-option label="Ant Design" value="Ant Design"></el-option>
                  <el-option label="Material-UI" value="Material-UI"></el-option>
                  <el-option label="Chakra UI" value="Chakra UI"></el-option>
                  <el-option label="Tailwind CSS" value="Tailwind CSS"></el-option>
                </el-select>
              </div>

              <div class="form-group">
                <div class="temperature-title">
                  <label class="form-label">创意度 (Temperature)</label>
                  <span class="temperature-value">{{ form.temperature / 100 }}</span>
                </div>
                <div class="temperature-slider">
                  <span class="slider-label">专注</span>
                  <el-slider
                    v-model="form.temperature"
                    :min="0"
                    :max="100"
                    :step="1"
                    :format-tooltip="formatTemperatureTooltip"
                    style="flex-grow: 1; margin: 0 15px;"
                  ></el-slider>
                  <span class="slider-label">创意</span>
                </div>
                <p class="temperature-desc">
                  控制Agent生成Prompt的随机性。数值越低越专注，越高越有创造力。
                </p>
              </div>
            </div>

            <el-alert
              v-if="!apiKeySet"
              title="API Key 未设置"
              type="warning"
              description="请在 .env 文件中设置您的 API key 以使用实际的 LLM API。"
              show-icon
              :closable="false"
              class="api-warning"
            />

            <el-button
              type="primary"
              @click="handleGeneratePrdPrompt"
              :loading="loading"
              :disabled="!canGenerate"
              class="generate-button"
            >
              {{ loading ? '生成中...' : '生成 Agent PRD Prompt' }}
            </el-button>
          </div>
        </el-card>
      </el-col>

      <!-- 右侧：结果展示区域 -->
      <el-col :lg="14" :md="13" :sm="24" class="right-column">
        <el-card class="result-card">
          <div slot="header" class="result-header">
            <div class="header-title">
              <i class="el-icon-document-copy header-icon"></i>
              <span>生成的 Agent PRD Prompt</span>
            </div>
            <div class="header-actions" v-if="generatedPrdPrompt && !loading">
              <el-button
                size="small"
                icon="el-icon-document-copy"
                @click="copyPrdPrompt"
                class="action-btn copy-btn"
              >
                {{ copied ? '已复制' : '复制' }}
              </el-button>
            </div>
          </div>

          <div class="result-body">
            <!-- 空状态：只在没有内容且不在加载时显示 -->
            <div v-if="!generatedPrdPrompt && !loading" class="empty-state">
              <div class="empty-icon">
                <i class="el-icon-cpu"></i>
              </div>
              <h3 class="empty-title">准备生成 Agent PRD Prompt</h3>
              <p class="empty-desc">输入功能描述，配置技术栈参数，然后点击生成按钮创建专业的 Agent PRD Prompt</p>
              <div class="empty-steps">
                <div class="step-item">
                  <div class="step-number">1</div>
                  <span>输入功能描述</span>
                </div>
                <div class="step-item">
                  <div class="step-number">2</div>
                  <span>配置技术栈</span>
                </div>
                <div class="step-item">
                  <div class="step-number">3</div>
                  <span>生成 Prompt</span>
                </div>
              </div>
            </div>

            <!-- 初始加载状态：只在开始生成且还没有内容时显示 -->
            <div v-else-if="loading && !generatedPrdPrompt" class="loading-container">
              <div class="loading-spinner">
                <i class="el-icon-loading"></i>
              </div>
              <p class="loading-text">正在生成 Agent PRD Prompt...</p>
              <div class="loading-progress">
                <div class="progress-bar"></div>
              </div>
            </div>

            <!-- 结果内容：有内容时就显示，支持流式渲染 -->
            <div class="result-content" ref="prdPromptContainer" v-if="generatedPrdPrompt">
              <div v-html="formattedPrdPrompt"></div>
              <!-- 流式生成时的加载指示器 -->
              <div v-if="loading" class="streaming-indicator">
                <i class="el-icon-loading streaming-icon"></i>
                <span class="streaming-text">正在生成中...</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { mapState, mapActions } from 'vuex';
import { marked } from 'marked';
import { convertFileToString } from '@/utils/fileUtils';
import VueEasyMDE from 'vue-easymde';
import 'easymde/dist/easymde.min.css';
import hljs from 'highlight.js';
import 'highlight.js/styles/github.css';

// 初始化 marked，使用 highlight.js 进行代码高亮
marked.setOptions({
  highlight: function(code, lang) {
    const language = hljs.getLanguage(lang) ? lang : 'plaintext';
    return hljs.highlight(code, { language }).value;
  },
  langPrefix: 'language-',
  gfm: true,
  breaks: true,
});

export default {
  name: 'PRDToAgentPanel',
  components: {
    VueEasyMDE
  },
  props: {
      loading: {
          type: Boolean,
          default: false
      }
  },
  data() {
    return {
      featureDescription: '',
      uploadedFileContent: '',
      uploadedFileName: '',
      form: {
        framework: 'Vue',
        uiLibrary: 'Element UI',
        temperature: 50,
      },
      copied: false,
      apiKeySet: false,
      mdeOptions: {
        spellChecker: false,
        autosave: {
          enabled: false,
          delay: 500,
          uniqueId: "prd-editor-autosave",
        },
        status: false,
        toolbar: false,
        renderingConfig: {
          codeSyntaxHighlighting: true,
        },
        placeholder: "在此处输入功能描述（支持 Markdown 格式）...",
        hideIcons: ["guide", "image", "fullscreen", "side-by-side", "preview"],
        forceSync: true,
      },

    };
  },
  computed: {
    ...mapState({
        generatedPrdPrompt: state => state.generatedPrdPrompt,
    }),
    formattedPrdPrompt() {
      if (!this.generatedPrdPrompt) return '';
      return marked(this.generatedPrdPrompt);
    },
    canGenerate() {
        return this.featureDescription.trim() &&
               this.form.framework && this.form.uiLibrary;
    }
  },
  watch: {
    loading(newVal) {
      // 设置编辑器的只读状态
      if (this.$refs.mdeEditorRef && this.$refs.mdeEditorRef.easymde) {
        this.$refs.mdeEditorRef.easymde.codemirror.setOption("readOnly", newVal);
      }
    },
    generatedPrdPrompt(newVal, oldVal) {
          this.$nextTick(() => {
              if (this.$refs.prdPromptContainer) {
                  // 如果内容正在流式生成，滚动到底部以显示最新内容
                  if (this.loading && newVal && newVal.length > (oldVal ? oldVal.length : 0)) {
                      // 平滑滚动到底部
                      this.$refs.prdPromptContainer.scrollTo({
                          top: this.$refs.prdPromptContainer.scrollHeight,
                          behavior: 'smooth'
                      });
                  }
              }
          });
      }
  },
  created() {
    this.apiKeySet = process.env.VUE_APP_GEMINI_API_KEY &&
                     process.env.VUE_APP_GEMINI_API_KEY !== 'your_api_key_here';
  },
  mounted() {
    this.$nextTick(() => {
        // 确保DOM更新后设置编辑器内容
        if (this.$refs.mdeEditorRef && this.$refs.mdeEditorRef.easymde && this.featureDescription) {
            this.$refs.mdeEditorRef.easymde.value(this.featureDescription);
        }
        // 初始设置编辑器的只读状态
        if (this.$refs.mdeEditorRef && this.$refs.mdeEditorRef.easymde) {
            this.$refs.mdeEditorRef.easymde.codemirror.setOption("readOnly", this.loading);
        }
    });
  },
  methods: {
    ...mapActions(['generatePrdPrompt']),
    marked,
    convertFileToString,

    formatTemperatureTooltip(val) {
      return `${val / 100}`;
    },
    async handleFileUpload(file) {
      const allowedTypes = [
          'text/plain',
          'text/markdown',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      ];
      const fileExtension = file.name.split('.').pop().toLowerCase();
      const isAllowed = allowedTypes.includes(file.raw.type) || ['txt', 'md', 'docx'].includes(fileExtension);

      if (!isAllowed) {
        this.$message.error('只能上传 .txt, .md 或 .docx 文件!');
        if (this.$refs.prdFileUploaderRef) this.$refs.prdFileUploaderRef.clearFiles();
        return;
      }
      const isLt5M = file.size / 1024 / 1024 < 5;
      if (!isLt5M) {
        this.$message.error('上传文件大小不能超过 5MB!');
        if (this.$refs.prdFileUploaderRef) this.$refs.prdFileUploaderRef.clearFiles();
        return;
      }

      this.uploadedFileName = file.name;
      try {
        const content = await this.convertFileToString(file.raw);
        this.uploadedFileContent = content;
        this.featureDescription = content;

        this.$nextTick(() => {
            if (this.$refs.mdeEditorRef && this.$refs.mdeEditorRef.easymde) {
                this.$refs.mdeEditorRef.easymde.value(content);
            }
        });

      } catch (error) {
        console.error('Error reading uploaded file:', error);
        this.$message.error('文件读取失败: ' + error.message);
        this.removeUploadedFile();
      }
    },
    removeUploadedFile() {
      this.uploadedFileContent = '';
      this.uploadedFileName = '';
      this.featureDescription = '';

      if (this.$refs.mdeEditorRef && this.$refs.mdeEditorRef.easymde) {
          this.$refs.mdeEditorRef.easymde.value("");
      }
      if (this.$refs.prdFileUploaderRef) this.$refs.prdFileUploaderRef.clearFiles();
    },
    clearAllInput() {
        this.featureDescription = '';
        this.removeUploadedFile();
        this.form.framework = 'Vue';
        this.form.uiLibrary = 'Element UI';
        this.form.temperature = 50;
        this.$message.info('已清空所有输入。');
    },
    async handleGeneratePrdPrompt() {
      const descriptionToUse = this.featureDescription.trim();

      if (!descriptionToUse) {
        this.$message.warning('请输入功能描述或上传文件。');
        return;
      }
      if (!this.form.framework) {
        this.$message.warning('请选择前端框架。');
        return;
      }
      if (!this.form.uiLibrary) {
        this.$message.warning('请选择UI组件库/样式框架。');
        return;
      }

      await this.generatePrdPrompt({
        featureDescription: descriptionToUse,
        framework: this.form.framework,
        uiLibrary: this.form.uiLibrary,
        temperature: this.form.temperature / 100,
      });
    },
    async copyPrdPrompt() {
      try {
        await navigator.clipboard.writeText(this.generatedPrdPrompt);
        this.copied = true;
        this.$message.success('已复制到剪贴板');
        setTimeout(() => {
          this.copied = false;
        }, 2000);
      } catch (error) {
        this.$message.error('复制失败');
      }
    },

    clearState() {
        this.featureDescription = '';
        this.removeUploadedFile();
        this.form.framework = 'Vue';
        this.form.uiLibrary = 'Element UI';
        this.form.temperature = 50;
    }
  },
};
</script>

<style scoped>
.prd-to-agent-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.main-row {
  height: 100%;
  margin: 0 !important;
}

.left-column,
.right-column {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.left-column {
  padding-right: 12px !important;
}

.right-column {
  padding-left: 12px !important;
}

.input-card,
.result-card {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  border-radius: 16px;
  transition: all 0.3s ease;
  height: 100%;
}

.input-card:hover,
.result-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
}

.input-header,
.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: rgba(255, 255, 255, 0.8);
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  border-radius: 16px 16px 0 0;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 18px;
  font-weight: 600;
  color: #1a202c;
}

.header-icon {
  font-size: 20px;
  color: #667eea;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  border-radius: 8px !important;
  font-weight: 500 !important;
  transition: all 0.3s ease !important;
}

.copy-btn.copied {
  background: #10b981 !important;
  border-color: #10b981 !important;
  color: white !important;
}

.input-body,
.result-body {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  padding: 24px;
  overflow: hidden;
}

/* Markdown 编辑器样式 */
.markdown-editor {
    min-height: 240px;
    margin-top: 12px;
    border-radius: 12px;
    overflow: hidden;
    border: 2px solid #e2e8f0;
    transition: all 0.3s ease;
}

.markdown-editor:focus-within {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.markdown-editor.is-disabled {
    opacity: 0.6;
    pointer-events: none;
}

.markdown-editor :deep(.CodeMirror) {
    border-radius: 12px;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 14px;
    line-height: 1.6;
    padding: 16px;
}

.markdown-editor :deep(.CodeMirror-scroll) {
    min-height: 240px;
}

.markdown-editor :deep(.CodeMirror-placeholder) {
    color: #9ca3af;
    font-style: italic;
}



/* 上传控制区域 */
.upload-controls {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
    align-items: center;
}

.upload-controls :deep(.el-button) {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.upload-controls :deep(.el-button--primary) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.upload-controls :deep(.el-button--primary:hover) {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

/* 确保编辑器在卡片中正确显示 */
.prd-to-agent-panel .el-card {
    height: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    border-radius: 16px;
    transition: all 0.3s ease;
}

.prd-to-agent-panel .el-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
}

.description-input-area {
    margin-bottom: 32px;
}

.upload-tip {
    font-size: 13px;
    color: #6b7280;
    margin-bottom: 12px;
    padding: 12px 16px;
    background: rgba(102, 126, 234, 0.05);
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.config-section {
    margin-bottom: 32px;
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    font-size: 15px;
    font-weight: 600;
    color: #374151;
    margin-bottom: 8px;
}

.api-warning {
    margin: 20px 0;
    border-radius: 12px !important;
}

.section-title {
    font-size: 20px;
    font-weight: 700;
    color: #1a202c;
    margin: 24px 0 16px;
    padding-bottom: 12px;
    position: relative;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40px;
    height: 3px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 2px;
}

.section-title:first-child {
    margin-top: 0;
}

.temperature-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.temperature-value {
    font-size: 16px;
    font-weight: 600;
    color: #667eea;
}

.temperature-slider {
    display: flex;
    align-items: center;
    margin: 12px 0;
}

.temperature-slider :deep(.el-slider) {
    flex: 1;
    margin: 0 16px;
}

.temperature-slider :deep(.el-slider__runway) {
    background: #e5e7eb;
    border-radius: 6px;
    height: 6px;
}

.temperature-slider :deep(.el-slider__bar) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 6px;
}

.temperature-slider :deep(.el-slider__button) {
    border: 3px solid #667eea;
    background: #fff;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.slider-label {
    font-size: 13px;
    color: #6b7280;
    font-weight: 500;
    min-width: 30px;
    text-align: center;
}

.temperature-desc {
    font-size: 13px;
    color: #6b7280;
    margin: 8px 0 20px;
    line-height: 1.4;
}

.generate-button {
    width: 100%;
    margin-top: 24px;
    height: 48px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    transition: all 0.3s ease;
    color: white;
}

.generate-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.generate-button:disabled {
    opacity: 0.6;
    transform: none;
    box-shadow: none;
}

.full-width {
    width: 100%;
}

/* 表单项优化 */
:deep(.el-form-item__label) {
    font-weight: 600;
    color: #374151;
    font-size: 15px;
    margin-bottom: 8px;
}

:deep(.el-select),
:deep(.el-input) {
    border-radius: 12px;
}

:deep(.el-select .el-input__inner),
:deep(.el-input__inner) {
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    transition: all 0.3s ease;
    font-size: 14px;
    padding: 12px 16px;
}

:deep(.el-select .el-input__inner:focus),
:deep(.el-input__inner:focus) {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 结果区域样式 */
.result-content {
    flex-grow: 1;
    overflow-y: auto;
    padding: 20px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 12px;
    border: 2px solid #e2e8f0;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
}

.result-content :deep(h1),
.result-content :deep(h2),
.result-content :deep(h3),
.result-content :deep(h4),
.result-content :deep(h5),
.result-content :deep(h6) {
    color: #1a202c;
    font-weight: 700;
    margin: 24px 0 16px 0;
    line-height: 1.3;
}

.result-content :deep(h1) { font-size: 28px; }
.result-content :deep(h2) { font-size: 24px; }
.result-content :deep(h3) { font-size: 20px; }
.result-content :deep(h4) { font-size: 18px; }

.result-content :deep(p) {
    color: #374151;
    margin: 16px 0;
    font-size: 15px;
}

.result-content :deep(ul),
.result-content :deep(ol) {
    margin: 16px 0;
    padding-left: 24px;
}

.result-content :deep(li) {
    color: #374151;
    margin: 8px 0;
    font-size: 15px;
}

.result-content :deep(code) {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
    padding: 2px 6px;
    border-radius: 6px;
    font-family: 'JetBrains Mono', 'Fira Code', Consolas, monospace;
    font-size: 13px;
}

.result-content :deep(pre) {
    background: #1a202c;
    color: #e2e8f0;
    padding: 20px;
    border-radius: 12px;
    overflow-x: auto;
    margin: 20px 0;
    border: 1px solid #374151;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.result-content :deep(pre code) {
    background: transparent;
    color: inherit;
    padding: 0;
    border-radius: 0;
    font-family: 'JetBrains Mono', 'Fira Code', Consolas, monospace;
    font-size: 14px;
    line-height: 1.5;
}

/* 加载状态和空状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  flex-grow: 1;
}

.loading-spinner {
  position: relative;
  margin-bottom: 24px;
}

.loading-spinner .el-icon-loading {
  font-size: 48px;
  color: #667eea;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.loading-text {
  font-size: 18px;
  font-weight: 500;
  color: #4a5568;
  margin-bottom: 24px;
}

.loading-progress {
  width: 200px;
  height: 4px;
  background: #e2e8f0;
  border-radius: 2px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 2px;
  animation: progress 2s ease-in-out infinite;
}

@keyframes progress {
  0% { width: 0%; }
  50% { width: 70%; }
  100% { width: 100%; }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  text-align: center;
  flex-grow: 1;
}

.empty-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24px;
}

.empty-icon i {
  font-size: 36px;
  color: #667eea;
}

.empty-title {
  font-size: 20px;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 12px 0;
}

.empty-desc {
  font-size: 15px;
  color: #6b7280;
  line-height: 1.6;
  max-width: 400px;
  margin: 0 0 32px 0;
}

.empty-steps {
  display: flex;
  gap: 24px;
  justify-content: center;
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.step-item:hover {
  opacity: 1;
}

.step-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 600;
}

.step-item span {
  font-size: 13px;
  color: #6b7280;
  font-weight: 500;
}

/* 流式生成指示器 */
.streaming-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 16px;
  margin-top: 16px;
  background: rgba(102, 126, 234, 0.05);
  border: 1px solid rgba(102, 126, 234, 0.2);
  border-radius: 8px;
  color: #667eea;
  font-size: 14px;
  font-weight: 500;
}

.streaming-icon {
  font-size: 16px;
  animation: spin 1s linear infinite;
}

.streaming-text {
  opacity: 0.8;
}

/* 响应式优化 */
@media screen and (max-width: 1200px) {
  .left-column {
    padding-right: 8px !important;
  }

  .right-column {
    padding-left: 8px !important;
  }

  .input-header,
  .result-header {
    padding: 16px 20px;
  }

  .input-body,
  .result-body {
    padding: 20px;
  }

  .header-title {
    font-size: 16px;
  }

  .action-btn {
    font-size: 13px !important;
    padding: 6px 12px !important;
  }
}

@media screen and (max-width: 768px) {
  .main-row {
    flex-direction: column;
  }

  .left-column,
  .right-column {
    padding: 0 !important;
    margin-bottom: 16px;
  }

  .right-column {
    margin-bottom: 0;
  }

  .input-header,
  .result-header {
    padding: 16px;
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .input-body,
  .result-body {
    padding: 16px;
  }

  .section-title {
    font-size: 18px;
    margin: 20px 0 12px;
  }

  .markdown-editor {
    min-height: 200px;
    border-radius: 8px;
  }

  .result-content {
    padding: 16px;
    border-radius: 8px;
  }

  .generate-button {
    height: 44px;
    font-size: 15px;
  }

  .empty-state {
    padding: 60px 16px;
  }

  .empty-steps {
    flex-direction: column;
    gap: 16px;
  }

  .step-item {
    flex-direction: row;
    gap: 12px;
  }

  .loading-container {
    padding: 60px 16px;
  }

  .loading-text {
    font-size: 16px;
  }

  .loading-progress {
    width: 150px;
  }
}

@media screen and (max-width: 480px) {
  .header-actions {
    flex-wrap: wrap;
    gap: 6px;
  }

  .action-btn {
    font-size: 12px !important;
    padding: 5px 10px !important;
  }

  .empty-title {
    font-size: 18px;
  }

  .empty-desc {
    font-size: 14px;
  }

  .step-number {
    width: 28px;
    height: 28px;
    font-size: 12px;
  }

  .step-item span {
    font-size: 12px;
  }
}
</style>