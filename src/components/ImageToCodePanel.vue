// src/components/ImageToCodePanel.vue
<template>
  <el-row :gutter="20" class="image-to-code-panel">
    <el-col :span="12" class="card-column">
      <!-- UploadAndSettings Component -->
      <UploadAndSettings
        ref="uploadAndSettingsRef"
        :generate-button-loading="loading"
        :initial-image-base64="initialImageBase64"
        :initial-form-settings="initialFormSettings"
        @file-updated="onFileUpdated"
        @settings-changed="onSettingsChanged"
        @generate-prompt-requested="handleGenerateRequest"
        @save-session-requested="initiateSaveSession"
        @load-session-file-selected="handleLoadSessionFile"
      />
    </el-col>

    <el-col :span="12" class="card-column">
      <el-card class="controls-card">
        <!-- Results Section -->
        <div v-if="generatedPrompt || loading" class="result-section">
          <div class="result-header">
            <h4>Generated Prompt:</h4>
            <div>
              <el-button
                icon="el-icon-time"
                size="small"
                @click="$emit('open-history-dialog')"
                style="margin-right: 10px;"
                :disabled="promptVersions.length === 0"
              > History </el-button>
              <el-button
                type="primary"
                icon="el-icon-chat-dot-round"
                size="small"
                @click="$emit('open-chat-dialog')"
                style="margin-right: 10px;"
              >
                优化提示词
              </el-button>
              <el-button
                type="text"
                icon="el-icon-document-copy"
                @click="copyPrompt"
              >
                {{ copied ? 'Copied!' : 'Copy' }}
              </el-button>
            </div>
          </div>
          <div class="result-content" ref="promptContainer" v-if="generatedPrompt">
            <div v-html="formattedPrompt"></div>
            <!-- 代码块复制按钮 -->
            <div v-for="(block, index) in codeBlocks" :key="index" class="code-block-wrapper">
                <el-button
                    type="text"
                    icon="el-icon-document-copy"
                    size="mini"
                    class="copy-code-btn"
                    @click="copyCode(block.content)"
                >
                    Copy Code
                </el-button>
                <pre><code :class="`language-${block.lang}`">{{ block.content }}</code></pre>
            </div>
          </div>
          <el-empty v-if="!generatedPrompt && !loading" description="No prompt generated yet. Upload an image and click 'Generate prompt'." style="display:flex; flex-direction:column; justify-content:center; flex-grow: 1;"></el-empty>
          <div v-if="loading" style="display:flex; align-items:center; justify-content:center; flex-grow: 1;">
              <i class="el-icon-loading" style="font-size: 24px;"></i> Generating...
          </div>
        </div>
      </el-card>
    </el-col>
  </el-row>
</template>

<script>
import { mapState, mapActions } from 'vuex';
import { marked } from 'marked';
import UploadAndSettings from '@/components/UploadAndSettings.vue';
import hljs from 'highlight.js'; // 导入 highlight.js
import 'highlight.js/styles/github.css'; // 选择一个代码高亮主题

export default {
  name: 'ImageToCodePanel',
  components: {
    UploadAndSettings,
  },
  props: {
    initialImageBase64: {
      type: String,
      default: ''
    },
    initialFormSettings: {
      type: Object,
      default: () => ({})
    },
    promptVersions: {
      type: Array,
      default: () => []
    },
    loading: { // 这个 loading 现在是 isCodeGenerating
        type: Boolean,
        default: false
    }
  },
  data() {
    return {
      copied: false,
      form: {
        appType: 'web',
        temperature: 0.5,
        componentLibrary: 'yxfe',
      },
      currentImageBase64: '',
      currentUploadedFileRaw: null,
    };
  },
  computed: {
    ...mapState({
        generatedPrompt: state => state.generatedCodePrompt,
    }),
    formattedPrompt() {
      if (!this.generatedPrompt) return '';
      return marked(this.generatedPrompt);
    },
    codeBlocks() {
      if (!this.generatedPrompt) return [];
      const blocks = [];
      // Regex to find ```[lang]\n...\n``` blocks
      const regex = /```(\w+)?\n([\s\S]*?)\n```/g;
      let match;
      let lastIndex = 0;

      while ((match = regex.exec(this.generatedPrompt)) !== null) {
        const fullMatch = match[0];
        const lang = match[1] || 'plaintext'; // 语言，默认为 plaintext
        const content = match[2]; // 代码内容

        // Add preceding text as a non-code block
        if (match.index > lastIndex) {
          blocks.push({ type: 'text', content: this.generatedPrompt.substring(lastIndex, match.index) });
        }
        
        blocks.push({ type: 'code', lang: lang, content: content });
        lastIndex = match.index + fullMatch.length;
      }

      // Add any remaining text after the last code block
      if (lastIndex < this.generatedPrompt.length) {
        blocks.push({ type: 'text', content: this.generatedPrompt.substring(lastIndex) });
      }

      return blocks;
    }
  },
  watch: {
      initialImageBase64(newVal) {
          this.currentImageBase64 = newVal;
      },
      initialFormSettings: {
          handler(newVal) {
              if (newVal) {
                  this.form.appType = newVal.appType || 'web';
                  this.form.componentLibrary = newVal.componentLibrary || 'yxfe';
                  this.form.temperature = (newVal.temperature === undefined ? 50 : newVal.temperature) / 100;
              }
          },
          deep: true,
          immediate: true
      },
      // 监听 generatedPrompt 变化，并在 DOM 更新后滚动
      generatedPrompt() {
          this.$nextTick(() => {
              if (this.$refs.promptContainer) {
                  this.$refs.promptContainer.scrollTop = 0; // 滚动到顶部
              }
          });
      }
  },
  mounted() {
    // 初始化 marked，使用 highlight.js 进行代码高亮
    marked.setOptions({
      highlight: function(code, lang) {
        const language = hljs.getLanguage(lang) ? lang : 'plaintext';
        return hljs.highlight(code, { language }).value;
      },
      langPrefix: 'language-', // highlight.js css expects a top-level 'language-' class
      gfm: true, // 启用 GitHub Flavored Markdown
      breaks: true, // 启用换行符解析为 <br>
    });
  },
  methods: {
    ...mapActions(['generateCodePrompt']),
    marked,

    onFileUpdated({ base64Image, fileObject }) {
        this.currentImageBase64 = base64Image;
        this.currentUploadedFileRaw = fileObject;
        this.$emit('file-updated', { base64Image, fileObject });
    },
    onSettingsChanged(settings) {
      this.form.appType = settings.appType;
      this.form.componentLibrary = settings.componentLibrary;
      this.form.temperature = settings.temperature / 100;
      this.$emit('settings-changed', settings);
    },
    async handleGenerateRequest(payload) {
      if (!payload.imageBase64) {
         this.$message.warning('没有图片数据可用于生成。');
         return;
      }
      this.$emit('generate-code-prompt-requested', payload);
    },
    initiateSaveSession(uploadSettingsData) {
        this.$emit('save-session-requested', {
            ...uploadSettingsData,
            generatedCodePrompt: this.generatedPrompt,
            currentImageBase64: this.currentImageBase64,
            currentUploadedFileRaw: this.currentUploadedFileRaw,
        });
    },
    handleLoadSessionFile(file) {
        this.$emit('load-session-file-selected', file);
    },
    async copyPrompt() {
      try {
        await navigator.clipboard.writeText(this.generatedPrompt);
        this.copied = true;
        this.$message.success('已复制到剪贴板');
        setTimeout(() => {
          this.copied = false;
        }, 2000);
      } catch (error) {
        this.$message.error('复制失败');
      }
    },
    async copyCode(codeContent) {
      try {
        await navigator.clipboard.writeText(codeContent);
        this.$message.success('代码已复制！');
      } catch (error) {
        this.$message.error('代码复制失败');
      }
    },
    clearUploadState() {
        if (this.$refs.uploadAndSettingsRef) {
            this.$refs.uploadAndSettingsRef.clearUploadState();
        }
    }
  }
}
</script>

<style scoped>
/* Add styles for code block copy button */
.code-block-wrapper {
  position: relative;
  margin-bottom: 1em; /* Ensure spacing between code blocks */
}
.code-block-wrapper pre {
  margin-top: 0; /* Remove default margin from pre if present */
}
.copy-code-btn {
  position: absolute;
  top: 5px;
  right: 5px;
  z-index: 10; /* Ensure button is above code */
  background-color: rgba(255, 255, 255, 0.7);
  padding: 3px 8px;
  border-radius: 4px;
  font-size: 12px;
  opacity: 0; /* Initially hidden */
  transition: opacity 0.3s ease;
}
.code-block-wrapper:hover .copy-code-btn {
  opacity: 1; /* Show on hover */
}

/* Ensure pre and code styles are correctly applied for highlight.js */
.result-content :deep(pre code) {
  display: block; /* Make sure code takes full width inside pre */
  overflow-x: auto; /* Allow horizontal scrolling for long lines */
  padding: 1em; /* Add padding inside the code block */
  background: #f1f1f1; /* Match background for consistency */
  border-radius: 4px; /* Match border radius */
}

/* Override .result-content :deep(pre) styles if needed to make space for button */
.result-content :deep(pre) {
    position: relative; /* For positioning copy button */
    padding: 0; /* Remove padding if code has it */
}

/* Add media queries for responsiveness */
@media screen and (max-width: 768px) {
  .image-to-code-panel {
    flex-direction: column;
  }
  .card-column {
    width: 100%;
    max-width: 100%;
  }
  .card-column:last-child {
    margin-top: 20px;
    max-height: none;
  }
  .result-section {
    max-height: 500px; /* Example max height for smaller screens */
  }
}
</style>