<template>
  <el-card class="controls-card">
    <div class="upload-section">
       <el-card class="upload-card-inner" shadow="never">
        <div class="upload-container">
          <el-upload
            ref="mainUploaderRef"
            class="uploader"
            drag
            action=""
            :auto-upload="false"
            :show-file-list="false"
            :on-change="handleFileChange"
            v-if="!localImagePreview"
          >
          <i class="el-icon-upload"></i>
            <div class="el-upload__text">
              拖拽网站、Figma 设计或 UI 模型图片到此处
            </div>
            <div class="el-upload__tip">
              或 <em>点击上传</em>
            </div>
            <div class="el-upload__tip" slot="tip">
              注意：一次只能上传一张图片。
            </div>
          </el-upload>

          <div v-if="localImagePreview" class="image-preview">
            <img :src="localImagePreview" alt="Uploaded design" class="preview-image" />
            <div class="image-actions">
              <el-button type="danger" @click="removeImage" icon="el-icon-delete">
                Remove Image
              </el-button>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <div class="settings-section">
      <h3 class="section-title">Choose analysis focus:</h3>
      <el-select v-model="localForm.appType" placeholder="Select application type" class="full-width" @change="onSettingsChange">
        <el-option label="Web applications" value="web"></el-option>
      </el-select>

      <h3 class="section-title" style="margin-top: 20px;">Choose component library:</h3>
      <el-select v-model="localForm.componentLibrary" placeholder="Select component library" class="full-width" @change="onSettingsChange">
        <el-option
          v-for="item in libraryOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value">
        </el-option>
      </el-select>

      <h3 class="section-title temperature-title" style="margin-top: 20px;">
        <span>Creativity (Temperature):</span>
        <span class="temperature-value">{{ localForm.temperature / 100 }}</span>
      </h3>
      <div class="temperature-slider">
        <span class="slider-label">Focused</span>
        <el-slider
          v-model="localForm.temperature"
          :min="0"
          :max="100"
          :step="1"
          :format-tooltip="formatTemperatureTooltip"
          style="flex-grow: 1; margin: 0 15px;"
          @change="onSettingsChange"
        ></el-slider>
        <span class="slider-label">Creative</span>
      </div>
      <p class="temperature-desc">
        Controls randomness. Lower: more focused, Higher: more creative. Default: 0.5.
      </p>

      <div v-if="!apiKeySet" class="api-key-warning">
        <el-alert
          title="API Key Not Set"
          type="warning"
          description="To use the actual LLM API, please set your API key in the .env file. Currently using mock data."
          show-icon
          :closable="false"
        >
        </el-alert>
      </div>

      <el-button
        type="primary"
        @click="handleGenerateClick"
        :loading="generateButtonLoading"
        :disabled="!localFile && !localImagePreview"
        class="generate-button"
      >
        {{ generateButtonLoading ? 'Generating...' : 'Generate prompt' }}
      </el-button>

      <el-row :gutter="10" style="margin-top: 15px;">
        <el-col :span="12">
          <el-button @click="handleSaveSessionClick" class="full-width" icon="el-icon-download">Save Session</el-button>
        </el-col>
        <el-col :span="12">
           <input type="file" ref="loadSessionInputRef" @change="handleLoadSessionFileTrigger" accept=".json" style="display: none" />
          <el-button @click="triggerLoadSessionInput" class="full-width" icon="el-icon-upload2">Load Session</el-button>
        </el-col>
      </el-row>
    </div>
  </el-card>
</template>

<script>
import { compressAndConvertImage } from '@/utils/fileUtils'; // 引入新的图片处理函数

export default {
  name: 'UploadAndSettings',
  props: {
    generateButtonLoading: {
        type: Boolean,
        default: false,
    },
    initialImageBase64: {
        type: String,
        default: ''
    },
    initialFormSettings: {
        type: Object,
        default: () => ({
            appType: 'web',
            componentLibrary: 'yxfe',
            temperature: 50,
        })
    }
  },
  data() {
    return {
      localFile: null, 
      localImagePreview: '', 
      localForm: {
        appType: 'web',
        temperature: 50,
        componentLibrary: 'yxfe',
      },
      libraryOptions: [
        { label: '@yxfe/components', value: 'yxfe' },
        { label: 'MTD Components', value: 'mtd' },
      ],
      apiKeySet: false,
    };
  },
  watch: {
      initialImageBase64(newVal) {
          this.localImagePreview = newVal;
          if (newVal) {
              this.localFile = null;
              if (this.$refs.mainUploaderRef) {
                  this.$refs.mainUploaderRef.clearFiles();
              }
          }
      },
      initialFormSettings: {
          handler(newVal) {
              if (newVal) {
                  this.localForm.appType = newVal.appType || 'web';
                  this.localForm.componentLibrary = newVal.componentLibrary || 'yxfe';
                  this.localForm.temperature = newVal.temperature === undefined ? 50 : newVal.temperature;
              }
          },
          deep: true,
          immediate: true 
      }
  },
  created() {
    this.apiKeySet = process.env.VUE_APP_GEMINI_API_KEY &&
                     process.env.VUE_APP_GEMINI_API_KEY !== 'your_api_key_here'; 
    if (this.initialImageBase64) {
        this.localImagePreview = this.initialImageBase64;
    }
    if (this.initialFormSettings) {
        this.localForm = { ...this.localForm, ...this.initialFormSettings };
    }
  },
  methods: {
    // Marked and file utils are now handled directly
    // marked, // Make marked available in template
    // convertFileToBase64,
    // convertFileToString,

    async handleFileChange(file) {
      this.localFile = file;
      const isImage = file.raw.type.startsWith('image/');
      if (!isImage) {
        this.$message.error('只能上传图片文件!');
        if (this.$refs.mainUploaderRef) this.$refs.mainUploaderRef.clearFiles();
        return;
      }
      // 不再限制文件大小，因为我们会压缩
      // const isLt2M = file.size / 1024 / 1024 < 2;
      // if (!isLt2M) {
      //   this.$message.error('上传图片大小不能超过 2MB!');
      //   if (this.$refs.mainUploaderRef) this.$refs.mainUploaderRef.clearFiles();
      //   return;
      // }

      try {
          // 使用压缩函数
          const base64 = await compressAndConvertImage(file.raw);
          this.localImagePreview = base64;
          this.$emit('file-updated', { base64Image: base64, fileObject: file.raw });
          this.onSettingsChange();
      } catch(e) {
          this.$message.error(`图片处理失败: ${e.message || '请尝试其他图片。'}`); // 更友好的错误提示
          this.removeImage();
      }
    },
    removeImage() {
      this.localFile = null;
      this.localImagePreview = '';
      if (this.$refs.mainUploaderRef) {
        this.$refs.mainUploaderRef.clearFiles();
      }
      this.$emit('file-updated', { base64Image: null, fileObject: null });
      this.onSettingsChange();
    },
    formatTemperatureTooltip(val) {
      return `${val / 100}`;
    },
    onSettingsChange() {
      this.$emit('settings-changed', { ...this.localForm });
    },
    handleGenerateClick() {
        if (!this.localFile && !this.localImagePreview) {
            this.$message.warning('请先上传一张图片。'); // 友好的错误提示
            return;
        }
        this.$emit('generate-prompt-requested', {
            imageBase64: this.localImagePreview,
            appType: this.localForm.appType,
            temperature: this.localForm.temperature / 100,
            componentLibrary: this.localForm.componentLibrary,
            newRawFileObject: this.localFile ? this.localFile.raw : null
      });
    },
    handleSaveSessionClick() {
        this.$emit('save-session-requested', {
            imageBase64: this.localImagePreview,
            formSettings: { ...this.localForm }
        });
    },
    triggerLoadSessionInput() {
        this.$refs.loadSessionInputRef.click();
    },
    handleLoadSessionFileTrigger(event) {
        const file = event.target.files[0];
        if (!file) return;
        this.$emit('load-session-file-selected', file);
        if (this.$refs.loadSessionInputRef) {
            this.$refs.loadSessionInputRef.value = null;
        }
    },
    clearUploadState() {
        this.localFile = null;
        this.localImagePreview = '';
        if (this.$refs.mainUploaderRef) {
            this.$refs.mainUploaderRef.clearFiles();
        }
    }
  },
};
</script>

<style scoped>
.upload-settings-card {
  width: 100%; 
  height: 100%;
  display: flex;
  flex-direction: column;
}

.settings-section {
  flex-grow: 1;
  overflow-y: auto; 
  min-height: 0; 
  padding: 0 20px 20px 20px;
}

.upload-card-inner {
    border: none;
    padding: 0;
    margin-bottom: 20px;
}
.upload-card-inner :deep(.el-card__body) {
    padding: 0;
}
.upload-container {
  display: flex;
  flex-direction: column;
  min-height: 280px; 
}
.uploader {
  display: flex;
  flex-direction: column;
  flex-grow: 1; 
}
.uploader :deep(.el-upload-dragger) {
  border: 2px dashed #dcdfe6;
  border-radius: 8px;
  background-color: #f9fafc;
  padding: 40px; 
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%; 
  min-height: 280px; 
  box-sizing: border-box;
  transition: border-color 0.3s, background-color 0.3s;
}
.uploader :deep(.el-upload-dragger:hover),
.uploader :deep(.el-upload-dragger.is-dragover) {
  border-color: #409EFF;
  background-color: #f5f9ff;
}
.uploader :deep(.el-icon-upload) {
  font-size: 60px;
  color: #c0c4cc;
  margin: 0 0 20px;
  line-height: 1;
  transition: color 0.3s;
}
.uploader :deep(.el-upload-dragger:hover .el-icon-upload),
.uploader :deep(.el-upload-dragger.is-dragover .el-icon-upload) {
  color: #409EFF;
}
.uploader :deep(.el-upload__text) {
  color: #606266;
  font-size: 16px;
  line-height: 1.5;
}
.uploader :deep(.el-upload__text em) {
  color: #409EFF;
  font-style: normal;
}
.uploader :deep(.el-upload__tip) {
  color: #909399;
  font-size: 13px;
  margin-top: 10px;
  line-height: 1.4;
}

.image-preview {
  position: relative;
  width: 100%;
  max-height: 350px; 
  overflow: hidden;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
  border: 1px solid #e0e6ed; 
  min-height: 280px; 
}
.preview-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}
.image-actions {
  position: absolute;
  top: 0; left: 0; right: 0; bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.6);
  opacity: 0;
  transition: opacity 0.3s;
}
.image-preview:hover .image-actions {
  opacity: 1;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  margin: 20px 0 10px;
}
.section-title:first-child {
    margin-top: 0;
}
.temperature-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.temperature-value {
  font-size: 14px;
  color: #666;
}
.temperature-slider {
  display: flex;
  align-items: center;
  margin: 10px 0;
}
.slider-label {
  font-size: 14px;
  color: #666;
  width: auto;
}
.temperature-desc {
  font-size: 12px;
  color: #999;
  margin: 5px 0 20px;
}
.api-key-warning {
  margin: 15px 0;
}
.generate-button {
  width: 100%;
  margin-top: 20px;
}
.full-width {
  width: 100%;
}
</style>