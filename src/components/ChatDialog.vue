<template>
  <el-dialog
    title="优化提示词"
    :visible.sync="dialogVisible"
    width="70%"
    :before-close="handleBeforeClose"
    class="chat-dialog"
    append-to-body
    @opened="onDialogOpened"
  >
    <div class="chat-container">
      <div class="chat-messages" ref="chatMessages">
        <div v-for="(message, index) in chatHistory" :key="index"
             :class="['message', message.role === 'user' ? 'user-message' : 'assistant-message']">
          <div class="message-content">
            <div v-if="message.role === 'assistant'">
              <div v-html="formatMessage(message.content)"></div>
              <!-- 代码块复制按钮 -->
              <div v-for="(block, blockIndex) in getCodeBlocks(message.content)" :key="`chat-code-${index}-${blockIndex}`" class="code-block-wrapper">
                  <el-button
                      type="text"
                      icon="el-icon-document-copy"
                      size="mini"
                      class="copy-code-btn"
                      @click="copyCode(block.content)"
                  >
                      Copy Code
                  </el-button>
                  <pre><code :class="`language-${block.lang}`">{{ block.content }}</code></pre>
              </div>
            </div>
            <div v-else> <!-- user message -->
              <p v-if="message.text">{{ message.text }}</p>
              <img v-if="message.imagePreview" :src="message.imagePreview" alt="User uploaded image" class="chat-image-preview" />
              <div v-if="message.documentName" class="chat-document-indicator">
                <i class="el-icon-document"></i> 交互文档: {{ message.documentName }}
              </div>
            </div>
          </div>
        </div>
        <div v-if="chatLoading && !isWaitingForContinuation" class="message assistant-message">
          <div class="message-content">
            <div class="typing-indicator">
              <span></span>
              <span></span>
              <span></span>
            </div>
          </div>
        </div>
      </div>
      <div class="chat-input-area">
        <div class="chat-controls">
          <el-upload
            action=""
            :auto-upload="false"
            :show-file-list="false"
            :on-change="handleChatFileChange"
            class="chat-image-uploader"
            accept="image/*"
            v-if="!chatImagePreview"
            style="margin-right: 8px;"
            ref="chatImageUploaderRef"
          >
            <el-button
              slot="trigger"
              size="small"
              icon="el-icon-picture-outline"
              :disabled="chatLoading"
            ></el-button>
          </el-upload>
          <el-upload
            action=""
            :auto-upload="false"
            :show-file-list="false"
            :on-change="handleChatDocumentChange"
            class="chat-document-uploader"
            accept=".txt,.md"
            style="margin-right: 8px;"
            v-if="!chatDocumentFile"
            ref="chatDocumentUploaderRef"
          >
            <el-button slot="trigger" size="small" icon="el-icon-document" :disabled="chatLoading"></el-button>
          </el-upload>
          <div v-if="chatImagePreview" class="chat-image-upload-preview-item" style="margin-right: 8px; display: flex; align-items: center;">
            <img :src="chatImagePreview" alt="Image to send" style="max-width: 50px; max-height: 50px; border-radius: 3px;"/>
            <el-button type="text" icon="el-icon-close" @click="removeChatImage" style="margin-left: 5px; padding:0;"></el-button>
          </div>
          <div v-if="chatDocumentName" class="chat-document-indicator-item" style="margin-right: 8px; display: flex; align-items: center;">
            <i class="el-icon-document" style="margin-right: 4px;"></i>
            <span style="font-size: 0.9em; max-width: 100px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;" :title="chatDocumentName">{{ chatDocumentName }}</span>
            <el-button type="text" icon="el-icon-close" @click="removeChatDocument" style="margin-left: 5px; padding:0;"></el-button>
          </div>

          <el-input
            v-model="userInput"
            type="textarea"
            :rows="3"
            placeholder="输入您的优化建议 (Ctrl+Enter 发送)..."
            :disabled="chatLoading"
            @keyup.ctrl.enter.native="sendMessage"
            class="chat-textarea"
            ref="userInputRef"
          ></el-input>
          <el-button
            type="primary"
            @click="sendMessage"
            :loading="chatLoading"
            :disabled="sendButtonDisabled"
            class="send-button"
          >
            发送
          </el-button>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="requestClose">取消</el-button>
      <el-button type="primary" @click="applyRefinedPrompt" :disabled="chatLoading">应用优化后的提示词</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { marked } from 'marked';
import { streamLongCatResponse } from '@/lib/longcat.js';
import { convertFileToBase64, convertFileToString } from '@/utils/fileUtils';
import hljs from 'highlight.js';
import 'highlight.js/styles/github.css'; 

export default {
  name: 'ChatDialog',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    initialPrompt: {
      type: String,
      default: '',
    },
    componentLibrary: {
      type: String,
      default: 'yxfe',
    },
    existingChatHistory: {
        type: Array,
        default: () => []
    },
    existingRefinedPromptBase: {
        type: String,
        default: ''
    }
  },
  data() {
    return {
      userInput: '',
      chatHistory: [],
      chatLoading: false,
      currentPromptBaseForAPI: '',
      chatImageFile: null,
      chatImagePreview: '',
      chatDocumentFile: null,
      chatDocumentName: '',
      accumulatedResponseForThisTurn: '',
      isWaitingForContinuation: false,
    };
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(value) {
        this.$emit('update:visible', value);
      },
    },
    sendButtonDisabled() {
        const isContinueCommand = this.userInput.trim().toLowerCase() === '继续' || this.userInput.trim().toLowerCase() === 'continue';
        if (this.chatLoading) return true;
        if (isContinueCommand && this.isWaitingForContinuation) return false;
        return !this.userInput.trim() && !this.chatImageFile && !this.chatDocumentFile;
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.initializeChat();
      }
    },
    initialPrompt(newVal) {
        if (!this.visible) {
            this.currentPromptBaseForAPI = newVal || '';
        }
    },
    existingRefinedPromptBase(newVal) {
        if (newVal && newVal !== this.currentPromptBaseForAPI) {
             this.currentPromptBaseForAPI = newVal;
        }
    }
  },
  mounted() {
    // 初始化 marked，使用 highlight.js 进行代码高亮
    marked.setOptions({
      highlight: function(code, lang) {
        const language = hljs.getLanguage(lang) ? lang : 'plaintext';
        return hljs.highlight(code, { language }).value;
      },
      langPrefix: 'language-',
      gfm: true,
      breaks: true,
    });
  },
  methods: {
    marked,
    convertFileToBase64,
    convertFileToString,

    initializeChat() {
      this.currentPromptBaseForAPI = this.existingRefinedPromptBase || this.initialPrompt;
      this.chatHistory = [...this.existingChatHistory];
      this.userInput = '';
      this.removeChatImage();
      this.removeChatDocument();
      this.accumulatedResponseForThisTurn = '';
      this.isWaitingForContinuation = false;
      this.$nextTick(() => {
        this.scrollToBottom();
        if (this.$refs.userInputRef) {
            this.$refs.userInputRef.focus();
        }
      });
    },
    onDialogOpened() {
        this.initializeChat();
    },
    handleBeforeClose(done) {
      if (this.chatLoading) {
        this.$confirm('正在生成回复，确定要关闭对话框吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(() => {
          done();
          this.$emit('closed');
        }).catch(() => {});
      } else {
        done();
        this.$emit('closed');
      }
    },
    requestClose() {
        this.handleBeforeClose(() => {
            this.dialogVisible = false;
        });
    },
    async handleChatFileChange(file) {
      const isImage = file.raw.type.startsWith('image/');
      if (!isImage) {
        this.$message.error('只能上传图片文件!');
        if (this.$refs.chatImageUploaderRef) this.$refs.chatImageUploaderRef.clearFiles();
        return;
      }
      const isLt2M = file.size / 1024 / 1024 < 2;
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!');
        if (this.$refs.chatImageUploaderRef) this.$refs.chatImageUploaderRef.clearFiles();
        return;
      }
      this.chatImageFile = file;
      try {
        this.chatImagePreview = await this.convertFileToBase64(file.raw);
      } catch(e) {
        this.$message.error('图片预览失败!');
        this.removeChatImage();
      }
    },
    removeChatImage() {
      this.chatImageFile = null;
      this.chatImagePreview = '';
      if (this.$refs.chatImageUploaderRef) this.$refs.chatImageUploaderRef.clearFiles();
    },
    async handleChatDocumentChange(file) {
      const allowedTypes = ['text/plain', 'text/markdown'];
      const fileExtension = file.name.split('.').pop().toLowerCase();
      const isAllowed = allowedTypes.includes(file.raw.type) || ['txt', 'md'].includes(fileExtension);

      if (!isAllowed) {
        this.$message.error('只能上传 .txt 或 .md 文件!');
        if (this.$refs.chatDocumentUploaderRef) this.$refs.chatDocumentUploaderRef.clearFiles();
        return;
      }
      const isLt5M = file.size / 1024 / 1024 < 5;
      if (!isLt5M) {
        this.$message.error('上传文件大小不能超过 5MB!');
        if (this.$refs.chatDocumentUploaderRef) this.$refs.chatDocumentUploaderRef.clearFiles();
        return;
      }
      this.chatDocumentFile = file;
      this.chatDocumentName = file.name;
    },
    removeChatDocument() {
      this.chatDocumentFile = null;
      this.chatDocumentName = '';
      if (this.$refs.chatDocumentUploaderRef) this.$refs.chatDocumentUploaderRef.clearFiles();
    },
    async sendMessage() {
      const userText = this.userInput.trim();
      const isContinueCommand = userText.toLowerCase() === '继续' || userText.toLowerCase() === 'continue';

      if (this.sendButtonDisabled && !(isContinueCommand && this.isWaitingForContinuation)) {
          return;
      }

      this.chatLoading = true;

      let messageForApi = userText;
      let imageBase64Data = null;
      let documentContent = null;

      const currentChatImagePreviewForHistory = this.chatImagePreview;
      const currentChatDocumentNameForHistory = this.chatDocumentName;

      if (!isContinueCommand) {
          this.accumulatedResponseForThisTurn = '';
          this.isWaitingForContinuation = false;
      }

      if (this.chatImageFile && !isContinueCommand) {
        try {
          imageBase64Data = await this.convertFileToBase64(this.chatImageFile.raw);
        } catch (error) {
          console.error('Error converting chat image to Base64:', error);
          this.$message.error('图片转换失败，请重试。');
          this.chatLoading = false;
          return;
        }
      }

      if (this.chatDocumentFile && !isContinueCommand) {
        try {
          documentContent = await this.convertFileToString(this.chatDocumentFile.raw);
        } catch (error) {
          console.error('Error reading document file:', error);
          this.$message.error('文档读取失败，请重试。');
          this.chatLoading = false;
          return;
        }
      }

      let documentPromptSection = "";
      if (documentContent) {
        documentPromptSection = `\n\n[来自用户上传的文档 - "${currentChatDocumentNameForHistory || this.chatDocumentFile.name}"]:\n\`\`\`\n${documentContent}\n\`\`\`\n请分析此文档中的交互逻辑，并将其整合到主提示中。`;
      }

      if (isContinueCommand && this.isWaitingForContinuation) {
          messageForApi = "请继续你上一条未完成的回复。";
          imageBase64Data = null;
          documentContent = null;
      } else if (imageBase64Data && userText) {
          messageForApi = `我上传了一张新图片，请分析它并将其描述整合到主提示的相关部分。我对这张图片的具体说明是："${userText}"。${documentPromptSection}`;
      } else if (imageBase64Data && !userText) {
          messageForApi = `我上传了一张新图片，请分析它并将其描述适当地整合到主提示中。${documentPromptSection}`;
      } else if (!imageBase64Data && userText) {
          messageForApi = `${userText}${documentPromptSection}`;
      } else if (!imageBase64Data && !userText && documentContent) {
          messageForApi = `我上传了一个文档，请分析此文档中的交互逻辑，并将其整合到主提示中。文档内容如下：\n[来自用户上传的文档 - "${currentChatDocumentNameForHistory || this.chatDocumentFile.name}"]:\n\`\`\`\n${documentContent}\n\`\`\`\n`;
      } else if (!userText && !imageBase64Data && !documentContent && isContinueCommand) {
          this.chatLoading = false;
          return;
      }

      const lastUserMessageText = this.chatHistory.length > 0 && this.chatHistory[this.chatHistory.length -1].role === 'user' ? this.chatHistory[this.chatHistory.length -1].text : null;
      if (!isContinueCommand || (isContinueCommand && userText !== "请继续你上一条未完成的回复." && userText !== lastUserMessageText) ) {
        const userMessageEntry = { role: 'user', text: userText };
        if (currentChatImagePreviewForHistory && !isContinueCommand) {
          userMessageEntry.imagePreview = currentChatImagePreviewForHistory;
        }
        if (currentChatDocumentNameForHistory && !isContinueCommand) {
          userMessageEntry.documentName = currentChatDocumentNameForHistory;
        }
        this.chatHistory.push(userMessageEntry);
      }

      this.userInput = '';
      if (!isContinueCommand) {
        this.removeChatImage();
        this.removeChatDocument();
      }

      this.$nextTick(() => this.scrollToBottom());

      try {
        const historyForApi = this.chatHistory
          .filter(msg => msg.role === 'user' || (msg.role === 'assistant' && msg.content))
          .slice(0, (isContinueCommand && this.isWaitingForContinuation && this.chatHistory.length > 0 && this.chatHistory[this.chatHistory.length -1].role === 'user' && this.chatHistory[this.chatHistory.length -1].text === messageForApi ) ? -1 : undefined)
          .map(msg => {
            if (msg.role === 'user') {
              return { role: 'user', content: msg.text || '' };
            }
            return { role: 'assistant', content: msg.content };
          });

        const stream = await streamLongCatResponse(
          this.currentPromptBaseForAPI,
          historyForApi,
          messageForApi,
          imageBase64Data,
          0.2,
          this.componentLibrary
        );

        let assistantMessageIndex = -1;
        if (this.isWaitingForContinuation && isContinueCommand) {
            for (let i = this.chatHistory.length - 1; i >= 0; i--) {
                if (this.chatHistory[i].role === 'assistant') {
                    assistantMessageIndex = i;
                    if (this.chatHistory[assistantMessageIndex].content.includes('请输入"继续"')) {
                       this.chatHistory[assistantMessageIndex].content = this.chatHistory[assistantMessageIndex].content.replace(/\n\n\*\([^)]*\)\*$/, '');
                    }
                    break;
                }
            }
        }

        if (assistantMessageIndex === -1) {
            this.chatHistory.push({ role: 'assistant', content: '' });
            assistantMessageIndex = this.chatHistory.length - 1;
            this.accumulatedResponseForThisTurn = '';
        }

        let streamedContentThisChunk = '';
        for await (const chunk of stream) {
          if (chunk.choices && chunk.choices[0]?.delta?.content) {
            const contentPart = chunk.choices[0].delta.content;
            streamedContentThisChunk += contentPart;
            if (this.isWaitingForContinuation && isContinueCommand) {
              this.chatHistory[assistantMessageIndex].content = this.accumulatedResponseForThisTurn + streamedContentThisChunk;
            } else {
              this.chatHistory[assistantMessageIndex].content = this.accumulatedResponseForThisTurn + streamedContentThisChunk;
            }
            this.$nextTick(() => this.scrollToBottom());
          }
        }

        if (this.isWaitingForContinuation && isContinueCommand) {
          this.accumulatedResponseForThisTurn += streamedContentThisChunk;
        } else {
          this.accumulatedResponseForThisTurn = this.accumulatedResponseForThisTurn + streamedContentThisChunk;
        }

        this.chatHistory[assistantMessageIndex].content = this.accumulatedResponseForThisTurn;

        const likelyCutOff = streamedContentThisChunk.trim().length > 100 &&
                            (streamedContentThisChunk.trim().endsWith('...') ||
                             !['.', '!', '?', '。', '！', '？', '`'].some(endChar => streamedContentThisChunk.trim().endsWith(endChar)) ||
                             /continue|more|next|继续|更多/i.test(streamedContentThisChunk.slice(-150)));

        if (likelyCutOff) {
          this.isWaitingForContinuation = true;
          if (!this.chatHistory[assistantMessageIndex].content.includes('请输入"继续"')) {
             this.chatHistory[assistantMessageIndex].content += "\n\n*(内容可能未完整，请输入\"继续\"获取剩余部分)*";
          }
        } else {
          this.currentPromptBaseForAPI = this.accumulatedResponseForThisTurn;
          this.isWaitingForContinuation = false;
        }

      } catch (error) {
        console.error('Error in chat:', error);
        const errorMsg = '与 AI 对话时出错: ' + (error.message || '未知错误') + '。请检查API配置或稍后重试。'; // 友好的错误提示
        this.$message.error(errorMsg);
        if (this.chatHistory.length > 0 && this.chatHistory[this.chatHistory.length -1].role === 'assistant') {
            this.chatHistory[this.chatHistory.length -1].content = (this.accumulatedResponseForThisTurn || '') + '\n\n' + errorMsg;
        } else {
            this.chatHistory.push({role: 'assistant', content: errorMsg});
        }
        this.isWaitingForContinuation = false;
      } finally {
        this.chatLoading = false;
        this.$nextTick(() => {
            this.scrollToBottom();
            if (this.$refs.userInputRef) {
                 this.$refs.userInputRef.focus();
            }
        });
      }
    },
    applyRefinedPrompt() {
      let promptToApply = '';
      if (this.isWaitingForContinuation) {
        this.$confirm('当前AI的回复可能还不完整，您确定要应用当前已生成的内容吗？输入"继续"可以让AI完成回复。', '提示', {
          confirmButtonText: '仍要应用',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          promptToApply = this.accumulatedResponseForThisTurn;
          this.finalizeApplyPrompt(promptToApply);
        }).catch(() => {});
        return;
      }

      promptToApply = this.currentPromptBaseForAPI || this.accumulatedResponseForThisTurn;
      this.finalizeApplyPrompt(promptToApply);
    },
    finalizeApplyPrompt(promptContent) {
        if (promptContent && !promptContent.startsWith("抱歉，处理您的请求时出现了错误") && !promptContent.startsWith("与 AI 对话时出错")) {
            this.$emit('apply-prompt', promptContent, [...this.chatHistory], this.currentPromptBaseForAPI);
            this.dialogVisible = false;
        } else if (!promptContent) {
            this.$message.warning('没有可应用的有效提示词。');
        } else {
            this.$message.error('提示词包含错误，无法应用。请修正或重新生成。');
        }
    },
    formatMessage(content) {
      if (!content) return '';
      // marked 已经配置了 highlight.js
      return marked(content);
    },
    // 新增：从消息内容中提取代码块（和 ImageToCodePanel 类似）
    getCodeBlocks(messageContent) {
        if (!messageContent) return [];
        const blocks = [];
        const regex = /```(\w+)?\n([\s\S]*?)\n```/g;
        let match;
        // 注意：这里只返回代码块，不返回文本块，因为文本由 v-html 渲染
        while ((match = regex.exec(messageContent)) !== null) {
            const lang = match[1] || 'plaintext';
            const content = match[2];
            blocks.push({ type: 'code', lang: lang, content: content });
        }
        return blocks;
    },
    async copyCode(codeContent) {
      try {
        await navigator.clipboard.writeText(codeContent);
        this.$message.success('代码已复制！');
      } catch (error) {
        this.$message.error('代码复制失败');
      }
    },
    scrollToBottom() {
      this.$nextTick(() => {
        if (this.$refs.chatMessages) {
          this.$refs.chatMessages.scrollTop = this.$refs.chatMessages.scrollHeight;
        }
      });
    },
  },
};
</script>

<style scoped>
/* Styles from Home.vue relevant to chat dialog, plus any new ones */
.chat-dialog :deep(.el-dialog__body) {
  padding: 0;
}

.chat-container {
  display: flex;
  flex-direction: column;
  height: 60vh; /* Or a fixed height like 500px */
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background-color: #f9f9f9;
}

.message {
  margin-bottom: 15px;
  display: flex;
  flex-direction: column;
}

.user-message {
  align-items: flex-end;
}

.assistant-message {
  align-items: flex-start;
}

.message-content {
  max-width: 80%;
  padding: 12px 16px;
  border-radius: 8px;
  word-break: break-word;
  /* Ensure pre-wrap for user and assistant messages if not handled by marked */
  white-space: pre-wrap;
}

.user-message .message-content {
  background-color: #409EFF;
  color: white;
}

.assistant-message .message-content {
  background-color: #f1f1f1;
  color: #333;
}
.assistant-message .code-block-wrapper { /* 确保只在助手消息中生效 */
  position: relative;
  margin-top: 10px; /* 与上方文本内容保持间距 */
  margin-bottom: 1em;
}
.assistant-message .code-block-wrapper pre {
  margin-top: 0;
}
.assistant-message .copy-code-btn {
  position: absolute;
  top: 5px;
  right: 5px;
  z-index: 10;
  background-color: rgba(255, 255, 255, 0.7);
  padding: 3px 8px;
  border-radius: 4px;
  font-size: 12px;
  opacity: 0;
  transition: opacity 0.3s ease;
}
.assistant-message .code-block-wrapper:hover .copy-code-btn {
  opacity: 1;
}

/* Ensure pre and code styles for highlight.js in chat messages */
.assistant-message .message-content :deep(pre code) {
  display: block;
  overflow-x: auto;
  padding: 1em;
  background: #f1f1f1;
  border-radius: 4px;
}
.assistant-message .message-content :deep(pre) {
    position: relative;
    padding: 0;
}

/* Markdown styles within assistant messages */
.assistant-message .message-content :deep(p) {
  margin: 0 0 10px 0;
}
.assistant-message .message-content :deep(p:last-child) {
  margin-bottom: 0;
}

.assistant-message .message-content :deep(code) {
  background-color: rgba(0, 0, 0, 0.08);
  padding: 2px 5px;
  border-radius: 4px;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
  font-size: 0.9em;
}

/* .assistant-message .message-content :deep(pre) {
  background-color: rgba(0, 0, 0, 0.08);
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
  white-space: pre-wrap; 
  word-break: break-all; 
} */

.assistant-message .message-content :deep(ul),
.assistant-message .message-content :deep(ol) {
  padding-left: 25px;
  margin: 10px 0;
}
.assistant-message .message-content :deep(li) {
  margin-bottom: 5px;
}


.typing-indicator {
  display: flex;
  align-items: center;
}

.typing-indicator span {
  height: 8px;
  width: 8px;
  background-color: #999;
  border-radius: 50%;
  display: inline-block;
  margin: 0 2px;
  animation: bounce 1.5s infinite ease-in-out;
}
.typing-indicator span:nth-child(2) { animation-delay: 0.2s; }
.typing-indicator span:nth-child(3) { animation-delay: 0.4s; }

.chat-image-preview { /* For images within chat messages */
    max-width: 200px; /* Increased size for better visibility */
    max-height: 200px;
    border-radius: 4px;
    margin-top: 8px;
    display: block;
    border: 1px solid #eee;
    cursor: pointer; /* Optional: if you want to add click to enlarge */
}
.chat-document-indicator {
  font-size: 0.9em;
  color: #555; /* Darker for better readability */
  margin-top: 8px;
  padding: 6px 10px;
  background-color: #e9e9eb; /* Slightly different background */
  border-radius: 4px;
}

@keyframes bounce {
  0%, 60%, 100% { transform: translateY(0); }
  30% { transform: translateY(-5px); }
}
</style>