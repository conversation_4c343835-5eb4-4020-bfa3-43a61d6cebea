<template>
  <div id="app">
    <el-container>
      <el-header class="header">
        <div class="header-content">
          <div class="logo">Copy Coder</div>
        </div>
      </el-header>
      <el-main>
        <router-view></router-view>
      </el-main>
    </el-container>
  </div>
</template>

<script>
export default {
  name: 'App'
}
</script>

<style>
#app {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen,
    Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  height: 100vh;
  background-color: #f5f7fa;
  display: flex; 
  flex-direction: column;
}

html {
  height: 100%;
}

body {
  background-color: #f5f7fa; 
  min-height: 100vh; 
  height: 100%;
  margin: 0;
}

.el-container {
  height: 100%; 
  display: flex;
  flex-direction: column;
}

.header {
  background-color: #fff;
  border-bottom: 1px solid #dcdfe6;
  position: fixed;
  width: 100%;
  z-index: 100;
  top: 0;
  flex-shrink: 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.logo {
  font-size: 20px;
  font-weight: bold;
  color: #409EFF;
}

.el-main {
  margin-top: 60px; 
  padding: 0;
  flex-grow: 1; 
  overflow-y: auto;
  display: flex; 
  flex-direction: column;
}
</style>
